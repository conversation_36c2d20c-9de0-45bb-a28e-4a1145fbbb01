import { Question } from "./qfjson";

// Answer types for different question types
export type MultipleChoiceAnswer = string | string[];
export type TrueFalseAnswer = boolean;
export type ShortAnswer = string;
export type MatchingAnswer = Array<{ stem_id: string; option_id: string }>;
export type FillInTheBlankAnswer = string[];
export type EssayAnswer = string;

// Union type for all possible answers
export type QuestionAnswer = 
  | MultipleChoiceAnswer 
  | TrueFalseAnswer 
  | ShortAnswer 
  | MatchingAnswer 
  | FillInTheBlankAnswer 
  | EssayAnswer 
  | null 
  | undefined;

// Question result interface
export interface QuestionResult {
  isCorrect: boolean;
  userAnswer: QuestionAnswer;
  correctAnswer: QuestionAnswer;
}

// Answer display configuration
export interface AnswerDisplayConfig {
  showCorrectAnswer: boolean;
  showUserAnswer: boolean;
  showExplanationAfterAnswer: boolean;
  highlightCorrectAnswer: boolean;
  immediateAnswerFeedback: boolean;
}

// Question renderer props
export interface QuestionRendererProps {
  question: Question;
  answer: QuestionAnswer;
  onAnswerChange: (answer: QuestionAnswer) => void;
  locale?: string;
  showFeedback?: boolean;
  showExplanation?: boolean;
  showAnswerButton?: boolean;
  showAnswer?: boolean;
  answerDisplayConfig?: AnswerDisplayConfig;
  questionResult?: QuestionResult;
  isAnswered?: boolean;
  onAnswerRevealed?: () => void;
}

// Specific question type interfaces
export interface MultipleChoiceQuestionProps {
  question: Question & { type: 'multiple_choice' };
  answer: MultipleChoiceAnswer;
  onAnswerChange: (answer: MultipleChoiceAnswer) => void;
  locale?: string;
  showAnswer?: boolean;
  answerDisplayConfig?: AnswerDisplayConfig;
}

export interface TrueFalseQuestionProps {
  question: Question & { type: 'true_false' };
  answer: TrueFalseAnswer;
  onAnswerChange: (answer: TrueFalseAnswer) => void;
  showAnswer?: boolean;
  answerDisplayConfig?: AnswerDisplayConfig;
}

export interface ShortAnswerQuestionProps {
  question: Question & { type: 'short_answer' };
  answer: ShortAnswer;
  onAnswerChange: (answer: ShortAnswer) => void;
  showAnswer?: boolean;
  answerDisplayConfig?: AnswerDisplayConfig;
}

export interface MatchingQuestionProps {
  question: Question & { type: 'matching' };
  answer: MatchingAnswer;
  onAnswerChange: (answer: MatchingAnswer) => void;
  locale?: string;
  showAnswer?: boolean;
  answerDisplayConfig?: AnswerDisplayConfig;
}

export interface FillInTheBlankQuestionProps {
  question: Question & { type: 'fill_in_the_blank' };
  answer: FillInTheBlankAnswer;
  onAnswerChange: (answer: FillInTheBlankAnswer) => void;
  locale?: string;
  showAnswer?: boolean;
  answerDisplayConfig?: AnswerDisplayConfig;
}

export interface EssayQuestionProps {
  question: Question & { type: 'essay' };
  answer: EssayAnswer;
  onAnswerChange: (answer: EssayAnswer) => void;
  showAnswer?: boolean;
  answerDisplayConfig?: AnswerDisplayConfig;
}

// Quiz renderer state types
export interface QuizState {
  currentQuestionIndex: number;
  questions: Question[];
  answers: Record<string, QuestionAnswer>;
  score: number;
  isCompleted: boolean;
  timeRemaining: number | null;
  questionAnswered: Record<string, boolean>;
  questionResults: Record<string, QuestionResult>;
  answersRevealed: Record<string, boolean>;
  answersShown: Record<string, boolean>;
}

// Quiz completion callback
export type QuizCompletionCallback = (score: number, answers: Record<string, QuestionAnswer>) => void;
