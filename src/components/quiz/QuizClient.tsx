"use client";

import React from "react";
import dynamic from "next/dynamic";
import { QuizFlowJSON } from "@/types/qfjson";

// Use dynamic import with SSR disabled for the QuizRenderer component
const QuizRenderer = dynamic(() => import("./QuizRenderer"), { ssr: false });

interface QuizClientProps {
  quiz: QuizFlowJSON;
  showAnswerButton?: boolean;
  answerDisplayConfig?: {
    showCorrectAnswer: boolean;
    showUserAnswer: boolean;
    showExplanationAfterAnswer: boolean;
    highlightCorrectAnswer: boolean;
    immediateAnswerFeedback: boolean;
  };
}

const QuizClient: React.FC<QuizClientProps> = ({ quiz, showAnswerButton = true, answerDisplayConfig }) => {
  return <QuizRenderer quiz={quiz} showAnswerButton={showAnswerButton} answerDisplayConfig={answerDisplayConfig} />;
};

export default QuizClient;
