"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, RotateCcw, Eye } from "lucide-react";
import { QuizFlowJSON } from "@/types/qfjson";

interface QuizPreviewClientProps {
  quiz: QuizFlowJSON;
}

export default function QuizPreviewClient({ quiz }: QuizPreviewClientProps) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<Record<string, any>>({});
  const [showAnswers, setShowAnswers] = useState(false);

  const questions = quiz.quiz.questions || [];
  const currentQuestion = questions[currentQuestionIndex];
  const totalQuestions = questions.length;

  const handleAnswerSelect = (questionId: string, answer: any) => {
    setSelectedAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const goToNextQuestion = () => {
    if (currentQuestionIndex < totalQuestions - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const goToPreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const resetPreview = () => {
    setCurrentQuestionIndex(0);
    setSelectedAnswers({});
    setShowAnswers(false);
  };

  const toggleShowAnswers = () => {
    setShowAnswers(!showAnswers);
  };

  const renderQuestionContent = () => {
    if (!currentQuestion) return null;

    const selectedAnswer = selectedAnswers[currentQuestion.question_id];

    switch (currentQuestion.type) {
      case 'multiple_choice':
        if ('options' in currentQuestion) {
          return (
            <div className="space-y-3">
              {currentQuestion.options.map((option, index: number) => {
                const isSelected = selectedAnswer === index;
                const isCorrect = option.is_correct;
                const showCorrectness = showAnswers && (isSelected || isCorrect);

                return (
                  <div
                    key={index}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      isSelected
                        ? showAnswers
                          ? isCorrect
                            ? 'border-green-500 bg-green-50'
                            : 'border-red-500 bg-red-50'
                          : 'border-blue-500 bg-blue-50'
                        : showAnswers && isCorrect
                          ? 'border-green-500 bg-green-50'
                          : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => !showAnswers && handleAnswerSelect(currentQuestion.question_id, index)}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                        isSelected
                          ? showAnswers
                            ? isCorrect
                              ? 'border-green-500 bg-green-500'
                              : 'border-red-500 bg-red-500'
                            : 'border-blue-500 bg-blue-500'
                          : showAnswers && isCorrect
                            ? 'border-green-500 bg-green-500'
                            : 'border-gray-300'
                      }`}>
                        {(isSelected || (showAnswers && isCorrect)) && (
                          <div className="w-2 h-2 rounded-full bg-white" />
                        )}
                      </div>
                      <span className="text-sm font-medium">{typeof option.text === 'string' ? option.text : option.text.default}</span>
                      {showCorrectness && (
                        <div className="ml-auto">
                          {isCorrect ? (
                            <Badge variant="default" className="bg-green-500">Correct</Badge>
                          ) : isSelected ? (
                            <Badge variant="destructive">Incorrect</Badge>
                          ) : null}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          );
        }
        break;

      case 'true_false':
        if ('correct_answer' in currentQuestion) {
          return (
            <div className="space-y-3">
              {['True', 'False'].map((option, index) => {
                const isSelected = selectedAnswer === (index === 0);
                const isCorrect = currentQuestion.correct_answer === (index === 0);
                const showCorrectness = showAnswers && (isSelected || isCorrect);

              return (
                <div
                  key={index}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    isSelected
                      ? showAnswers
                        ? isCorrect
                          ? 'border-green-500 bg-green-50'
                          : 'border-red-500 bg-red-50'
                        : 'border-blue-500 bg-blue-50'
                      : showAnswers && isCorrect
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => !showAnswers && handleAnswerSelect(currentQuestion.question_id, index === 0)}
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                      isSelected
                        ? showAnswers
                          ? isCorrect
                            ? 'border-green-500 bg-green-500'
                            : 'border-red-500 bg-red-500'
                          : 'border-blue-500 bg-blue-500'
                        : showAnswers && isCorrect
                          ? 'border-green-500 bg-green-500'
                          : 'border-gray-300'
                    }`}>
                      {(isSelected || (showAnswers && isCorrect)) && (
                        <div className="w-2 h-2 rounded-full bg-white" />
                      )}
                    </div>
                    <span className="text-sm font-medium">{option}</span>
                    {showCorrectness && (
                      <div className="ml-auto">
                        {isCorrect ? (
                          <Badge variant="default" className="bg-green-500">Correct</Badge>
                        ) : isSelected ? (
                          <Badge variant="destructive">Incorrect</Badge>
                        ) : null}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        );
        }
        break;

      case 'short_answer':
        if ('correct_answers' in currentQuestion) {
          return (
            <div className="space-y-4">
              <textarea
                className="w-full p-3 border rounded-lg resize-none"
                rows={4}
                placeholder="Type your answer here..."
                value={selectedAnswer || ''}
                onChange={(e) => !showAnswers && handleAnswerSelect(currentQuestion.question_id, e.target.value)}
                disabled={showAnswers}
              />
              {showAnswers && currentQuestion.correct_answers && (
                <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-sm font-medium text-green-800 mb-1">Sample Answer:</p>
                  <p className="text-sm text-green-700">{currentQuestion.correct_answers[0]}</p>
                </div>
              )}
            </div>
          );
        }
        break;

      default:
        return (
          <div className="p-4 bg-gray-50 border rounded-lg">
            <p className="text-sm text-gray-600">
              Question type "{currentQuestion.type}" preview not implemented yet.
            </p>
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      {/* Question Progress */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Badge variant="outline">
            Question {currentQuestionIndex + 1} of {totalQuestions}
          </Badge>
          <div className="w-64 bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentQuestionIndex + 1) / totalQuestions) * 100}%` }}
            />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={toggleShowAnswers}
          >
            <Eye className="h-4 w-4 mr-2" />
            {showAnswers ? 'Hide' : 'Show'} Answers
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={resetPreview}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
        </div>
      </div>

      {/* Question Card */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg">
                {typeof currentQuestion?.text === 'string' ? currentQuestion.text : currentQuestion?.text?.default}
              </CardTitle>
              {currentQuestion?.hint && currentQuestion.hint.length > 0 && (
                <CardDescription className="mt-2">
                  💡 Hint: {currentQuestion.hint[0].text}
                </CardDescription>
              )}
            </div>
            <Badge variant="secondary">
              {currentQuestion?.points} point{currentQuestion?.points !== 1 ? 's' : ''}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          {renderQuestionContent()}

          {/* Explanation */}
          {showAnswers && currentQuestion?.explanation && (
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Explanation</h4>
              <p className="text-blue-800 text-sm">
                {typeof currentQuestion.explanation === 'string'
                  ? currentQuestion.explanation
                  : currentQuestion.explanation?.default || 'No explanation available'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between items-center">
        <Button
          variant="outline"
          onClick={goToPreviousQuestion}
          disabled={currentQuestionIndex === 0}
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>

        <span className="text-sm text-muted-foreground">
          {currentQuestionIndex + 1} / {totalQuestions}
        </span>

        <Button
          variant="outline"
          onClick={goToNextQuestion}
          disabled={currentQuestionIndex === totalQuestions - 1}
        >
          Next
          <ChevronRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  );
}
