// Simple scoring logic test without UI components
import { Question } from '@/types/quiz';

// Test the scoring logic directly
function checkAnswerCorrectness(question: Question, userAnswer: any): boolean {
  // For short answer questions, allow empty strings to be processed
  if (question.type !== "short_answer" && !userAnswer) {
    return false;
  }

  // For short answer questions, check if it's undefined or null
  if (question.type === "short_answer" && (userAnswer === undefined || userAnswer === null)) {
    return false;
  }

  switch (question.type) {
    case "true_false": {
      const tfQuestion = question as any;
      // Check new format first (correctAnswer)
      if (tfQuestion.correctAnswer !== undefined) {
        return userAnswer === tfQuestion.correctAnswer;
      }
      // Fallback to old format (correct_answer)
      return userAnswer === tfQuestion.correct_answer;
    }
    case "multiple_choice": {
      const mcQuestion = question as any;
      if (!mcQuestion.options || !Array.isArray(mcQuestion.options)) {
        return false;
      }

      // Find correct options
      const correctOptions = mcQuestion.options.filter((opt: any) => opt.is_correct);
      if (correctOptions.length === 0) {
        return false;
      }

      // Check if it's single or multiple correct answers
      if (correctOptions.length === 1) {
        // Single correct answer
        const correctOption = correctOptions[0];
        return userAnswer === correctOption.id;
      } else {
        // Multiple correct answers
        const correctIds = correctOptions.map((opt: any) => opt.id);
        const userAnswers = Array.isArray(userAnswer) ? userAnswer : [userAnswer];
        return correctIds.length === userAnswers.length &&
               correctIds.every((id: string) => userAnswers.includes(id));
      }
    }
    case "short_answer": {
      const saQuestion = question as any;
      const userAnswerStr = String(userAnswer).trim();

      // Check new format first (correctAnswer)
      if (saQuestion.correctAnswer && Array.isArray(saQuestion.correctAnswer)) {
        return saQuestion.correctAnswer.some((correct: string) => {
          if (saQuestion.case_sensitive === false) {
            return userAnswerStr.toLowerCase() === correct.toLowerCase();
          }
          return userAnswerStr === correct;
        });
      }

      // Fallback to old format (correct_answers)
      if (!saQuestion.correct_answers || !Array.isArray(saQuestion.correct_answers)) {
        return false;
      }
      return saQuestion.correct_answers.some((correct: string) => {
        if (saQuestion.case_sensitive === false) {
          return userAnswerStr.toLowerCase() === correct.toLowerCase();
        }
        return userAnswerStr === correct;
      });
    }
    default:
      return false;
  }
}

// Test questions that match the actual quiz structure
const testQuestions: Question[] = [
  {
    question_id: "q1",
    type: "multiple_choice",
    question: "What is 2 + 2?",
    points: 2,
    options: [
      { id: "a", text: "3", is_correct: false },
      { id: "b", text: "4", is_correct: true },
      { id: "c", text: "5", is_correct: false }
    ]
  } as any,
  {
    question_id: "q2",
    type: "true_false",
    question: "The sky is blue",
    points: 1,
    correct_answer: true
  } as any,
  {
    question_id: "q3",
    type: "short_answer",
    question: "What is the capital of France?",
    points: 2,
    correct_answers: ["Paris", "paris"],
    case_sensitive: false
  } as any
];

describe('Quiz Scoring Logic', () => {
  test('should score multiple choice questions correctly', () => {
    const mcQuestion = testQuestions[0];

    // Test correct answer
    const correctResult = checkAnswerCorrectness(mcQuestion, "b");
    console.log('Multiple choice correct answer test:', correctResult);
    expect(correctResult).toBe(true);

    // Test incorrect answer
    const incorrectResult = checkAnswerCorrectness(mcQuestion, "a");
    console.log('Multiple choice incorrect answer test:', incorrectResult);
    expect(incorrectResult).toBe(false);
  });

  test('should score true/false questions correctly', () => {
    const tfQuestion = testQuestions[1];

    // Test correct answer
    const correctResult = checkAnswerCorrectness(tfQuestion, true);
    console.log('True/false correct answer test:', correctResult);
    expect(correctResult).toBe(true);

    // Test incorrect answer
    const incorrectResult = checkAnswerCorrectness(tfQuestion, false);
    console.log('True/false incorrect answer test:', incorrectResult);
    expect(incorrectResult).toBe(false);
  });

  test('should score short answer questions correctly', () => {
    const saQuestion = testQuestions[2];

    // Test correct answer (exact case)
    const correctResult1 = checkAnswerCorrectness(saQuestion, "Paris");
    console.log('Short answer correct (Paris) test:', correctResult1);
    expect(correctResult1).toBe(true);

    // Test correct answer (different case)
    const correctResult2 = checkAnswerCorrectness(saQuestion, "paris");
    console.log('Short answer correct (paris) test:', correctResult2);
    expect(correctResult2).toBe(true);

    // Test incorrect answer
    const incorrectResult = checkAnswerCorrectness(saQuestion, "London");
    console.log('Short answer incorrect test:', incorrectResult);
    expect(incorrectResult).toBe(false);
  });

  test('should calculate total score correctly', () => {
    const answers = {
      "q1": "b",  // Correct (2 points)
      "q2": true, // Correct (1 point)
      "q3": "Paris" // Correct (2 points)
    };

    let totalScore = 0;
    const answersRevealed = {}; // No answers revealed

    testQuestions.forEach(question => {
      // Skip questions where the answer was revealed
      if (answersRevealed[question.question_id]) {
        return;
      }

      const answer = answers[question.question_id];
      if (answer !== undefined && answer !== null) {
        const isCorrect = checkAnswerCorrectness(question, answer);
        console.log(`Question ${question.question_id}: answer=${answer}, isCorrect=${isCorrect}, points=${question.points}`);
        if (isCorrect) {
          totalScore += question.points;
        }
      }
    });

    console.log('Total score calculated:', totalScore);
    expect(totalScore).toBe(5); // 2 + 1 + 2 = 5 points
  });

  test('should handle mixed correct and incorrect answers', () => {
    const answers = {
      "q1": "a",    // Incorrect (0 points)
      "q2": true,   // Correct (1 point)
      "q3": "Paris" // Correct (2 points)
    };

    let totalScore = 0;
    const answersRevealed = {}; // No answers revealed

    testQuestions.forEach(question => {
      if (answersRevealed[question.question_id]) {
        return;
      }

      const answer = answers[question.question_id];
      if (answer !== undefined && answer !== null) {
        const isCorrect = checkAnswerCorrectness(question, answer);
        console.log(`Question ${question.question_id}: answer=${answer}, isCorrect=${isCorrect}, points=${question.points}`);
        if (isCorrect) {
          totalScore += question.points;
        }
      }
    });

    console.log('Mixed answers total score:', totalScore);
    expect(totalScore).toBe(3); // 0 + 1 + 2 = 3 points
  });

  test('should handle actual quiz data structure', () => {
    // Test with the actual quiz data structure from the database
    const actualQuizQuestions = [
      {
        question_id: "q1",
        type: "multiple_choice",
        points: 2,
        options: [
          {"id":"q1_opt1","text":"Phishing","is_correct":true},
          {"id":"q1_opt2","text":"DDoS","is_correct":false},
          {"id":"q1_opt3","text":"Malware","is_correct":false},
          {"id":"q1_opt4","text":"SQL Injection","is_correct":false}
        ],
        correctAnswer: ["q1_opt1"]
      } as any,
      {
        question_id: "q2",
        type: "multiple_choice",
        points: 3,
        options: [
          {"id":"q2_opt1","text":"Using a mix of uppercase and lowercase letters","is_correct":true},
          {"id":"q2_opt2","text":"Including numbers and special characters","is_correct":true},
          {"id":"q2_opt3","text":"Using the same password for all accounts","is_correct":false},
          {"id":"q2_opt4","text":"Making passwords at least 12 characters long","is_correct":true},
          {"id":"q2_opt5","text":"Using personal information like birthdate","is_correct":false}
        ],
        correctAnswer: ["q2_opt1","q2_opt2","q2_opt4"]
      } as any,
      {
        question_id: "q3",
        type: "true_false",
        points: 1,
        correctAnswer: true
      } as any,
      {
        question_id: "q4",
        type: "short_answer",
        points: 2,
        correctAnswer: ["Confidentiality, Integrity, Availability"]
      } as any
    ];

    // Test all correct answers
    const correctAnswers = {
      "q1": "q1_opt1",  // Correct single choice (2 points)
      "q2": ["q2_opt1","q2_opt2","q2_opt4"], // Correct multiple choice (3 points)
      "q3": true,       // Correct true/false (1 point)
      "q4": "Confidentiality, Integrity, Availability" // Correct short answer (2 points)
    };

    let totalScore = 0;
    const answersRevealed = {}; // No answers revealed

    actualQuizQuestions.forEach(question => {
      if (answersRevealed[question.question_id]) {
        return;
      }

      const answer = correctAnswers[question.question_id];
      if (answer !== undefined && answer !== null) {
        const isCorrect = checkAnswerCorrectness(question, answer);
        console.log(`Actual Quiz Question ${question.question_id}: answer=${JSON.stringify(answer)}, isCorrect=${isCorrect}, points=${question.points}`);
        if (isCorrect) {
          totalScore += question.points;
        }
      }
    });

    console.log('Actual quiz structure total score:', totalScore);
    expect(totalScore).toBe(8); // 2 + 3 + 1 + 2 = 8 points
  });
});
