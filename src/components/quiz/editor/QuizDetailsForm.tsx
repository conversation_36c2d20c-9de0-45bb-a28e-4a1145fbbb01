"use client";

import { useState } from "react";
import { Quiz } from "@/generated/prisma";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";

interface QuizDetailsFormProps {
  quiz: Quiz;
  onSave: (updatedDetails: Partial<Quiz>) => void;
  isSaving: boolean;
}

export default function QuizDetailsForm({ quiz, onSave, isSaving }: QuizDetailsFormProps) {
  const [title, setTitle] = useState(quiz.title);
  const [description, setDescription] = useState(quiz.description || "");
  const [tags, setTags] = useState(quiz.tags.join(", "));
  const [passingScore, setPassingScore] = useState(quiz.passingScore?.toString() || "70");
  const [timeLimit, setTimeLimit] = useState(quiz.timeLimit?.toString() || "15");
  const [locale, setLocale] = useState(quiz.locale);
  const [markupFormat, setMarkupFormat] = useState(quiz.markupFormat);

  // Answer display settings
  const [showCorrectAnswer, setShowCorrectAnswer] = useState(quiz.showCorrectAnswer || false);
  const [showUserAnswer, setShowUserAnswer] = useState(quiz.showUserAnswer ?? true);
  const [showExplanationAfterAnswer, setShowExplanationAfterAnswer] = useState(quiz.showExplanationAfterAnswer ?? true);
  const [highlightCorrectAnswer, setHighlightCorrectAnswer] = useState(quiz.highlightCorrectAnswer || false);
  const [immediateAnswerFeedback, setImmediateAnswerFeedback] = useState(quiz.immediateAnswerFeedback || false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const updatedDetails: Partial<Quiz> = {
      title,
      description: description || null,
      tags: tags.split(",").map(tag => tag.trim()).filter(tag => tag),
      passingScore: passingScore ? parseFloat(passingScore) : null,
      timeLimit: timeLimit ? parseInt(timeLimit) : null,
      locale,
      markupFormat,
      // Answer display settings
      showCorrectAnswer,
      showUserAnswer,
      showExplanationAfterAnswer,
      highlightCorrectAnswer,
      immediateAnswerFeedback,
    };

    onSave(updatedDetails);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-2">
        <label htmlFor="title" className="text-sm font-medium">
          Quiz Title
        </label>
        <input
          id="title"
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="w-full p-2 border rounded-md"
          required
        />
      </div>

      <div className="space-y-2">
        <label htmlFor="description" className="text-sm font-medium">
          Description
        </label>
        <textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          className="w-full p-2 border rounded-md min-h-[100px]"
          placeholder="Enter quiz description"
        />
      </div>

      <div className="space-y-2">
        <label htmlFor="tags" className="text-sm font-medium">
          Tags
        </label>
        <input
          id="tags"
          type="text"
          value={tags}
          onChange={(e) => setTags(e.target.value)}
          className="w-full p-2 border rounded-md"
          placeholder="Enter tags separated by commas (e.g., security, basics, networking)"
        />
        <p className="text-xs text-muted-foreground">
          Separate tags with commas (e.g., security, basics, networking)
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="passingScore" className="text-sm font-medium">
            Passing Score (%)
          </label>
          <input
            id="passingScore"
            type="number"
            min="0"
            max="100"
            value={passingScore}
            onChange={(e) => setPassingScore(e.target.value)}
            className="w-full p-2 border rounded-md"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="timeLimit" className="text-sm font-medium">
            Time Limit (minutes)
          </label>
          <input
            id="timeLimit"
            type="number"
            min="1"
            value={timeLimit}
            onChange={(e) => setTimeLimit(e.target.value)}
            className="w-full p-2 border rounded-md"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="locale" className="text-sm font-medium">
            Locale
          </label>
          <select
            id="locale"
            value={locale}
            onChange={(e) => setLocale(e.target.value)}
            className="w-full p-2 border rounded-md"
          >
            <option value="en-US">English (US)</option>
            <option value="en-GB">English (UK)</option>
            <option value="es-ES">Spanish</option>
            <option value="fr-FR">French</option>
            <option value="de-DE">German</option>
            <option value="ja-JP">Japanese</option>
            <option value="zh-CN">Chinese (Simplified)</option>
          </select>
        </div>

        <div className="space-y-2">
          <label htmlFor="markupFormat" className="text-sm font-medium">
            Markup Format
          </label>
          <select
            id="markupFormat"
            value={markupFormat}
            onChange={(e) => setMarkupFormat(e.target.value)}
            className="w-full p-2 border rounded-md"
          >
            <option value="markdown">Markdown</option>
            <option value="html">HTML</option>
            <option value="plain_text">Plain Text</option>
          </select>
        </div>
      </div>

      <Separator />

      {/* Answer Display Settings */}
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-medium">Answer Display Settings</h3>
          <p className="text-sm text-muted-foreground">
            Configure how answers and feedback are shown to users during the quiz
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="immediateAnswerFeedback"
                checked={immediateAnswerFeedback}
                onCheckedChange={(checked) => setImmediateAnswerFeedback(checked === true)}
              />
              <Label htmlFor="immediateAnswerFeedback" className="text-sm">
                Show if answer is correct/incorrect immediately
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="showUserAnswer"
                checked={showUserAnswer}
                onCheckedChange={(checked) => setShowUserAnswer(checked === true)}
              />
              <Label htmlFor="showUserAnswer" className="text-sm">
                Show user's answer after each question
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="showCorrectAnswer"
                checked={showCorrectAnswer}
                onCheckedChange={(checked) => setShowCorrectAnswer(checked === true)}
              />
              <Label htmlFor="showCorrectAnswer" className="text-sm">
                Show correct answer after each question
              </Label>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="highlightCorrectAnswer"
                checked={highlightCorrectAnswer}
                onCheckedChange={(checked) => setHighlightCorrectAnswer(checked === true)}
              />
              <Label htmlFor="highlightCorrectAnswer" className="text-sm">
                Highlight correct answer when user is wrong
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="showExplanationAfterAnswer"
                checked={showExplanationAfterAnswer}
                onCheckedChange={(checked) => setShowExplanationAfterAnswer(checked === true)}
              />
              <Label htmlFor="showExplanationAfterAnswer" className="text-sm">
                Show explanations after user answers
              </Label>
            </div>
          </div>
        </div>

        <div className="p-4 bg-muted/50 rounded-md">
          <h4 className="text-sm font-medium mb-2">Configuration Guide:</h4>
          <ul className="text-xs text-muted-foreground space-y-1">
            <li>• <strong>Immediate Feedback:</strong> Shows ✓/✗ right after answering</li>
            <li>• <strong>Show User Answer:</strong> Displays what the user selected</li>
            <li>• <strong>Show Correct Answer:</strong> Reveals the right answer</li>
            <li>• <strong>Highlight Correct:</strong> Visually emphasizes correct options when wrong</li>
            <li>• <strong>Show Explanations:</strong> Displays detailed explanations</li>
          </ul>
        </div>
      </div>

      <div className="flex justify-end pt-4">
        <Button type="submit" disabled={isSaving}>
          {isSaving ? "Saving..." : "Save Details"}
        </Button>
      </div>
    </form>
  );
}
