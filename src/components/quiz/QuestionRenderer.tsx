"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import { Question } from "@/types/qfjson";
import { QuestionRendererProps, QuestionAnswer } from "@/types/quiz-components";
import { getLocalizedText } from "@/lib/utils/qfjson-parser";
import ReactMarkdown from "react-markdown";
import CodeSnippetRenderer from "./CodeSnippetRenderer";
// import { cn } from "@/lib/utils";

// Import question type components
import MultipleChoiceQuestion from "./question-types/MultipleChoiceQuestion";
import TrueFalseQuestion from "./question-types/TrueFalseQuestion";
import ShortAnswerQuestion from "./question-types/ShortAnswerQuestion";
import MatchingQuestion from "./question-types/MatchingQuestion";
import FillInTheBlankQuestion from "./question-types/FillInTheBlankQuestion";
import EssayQuestion from "./question-types/EssayQuestion";

const QuestionRenderer: React.FC<QuestionRendererProps> = ({
  question,
  answer,
  onAnswerChange,
  locale = "en-US",
  showFeedback = false,
  showExplanation = false,
  showAnswerButton = false,
  showAnswer = false,
  onAnswerRevealed,
  answerDisplayConfig,
  questionResult,
  isAnswered = false,
}) => {
  const [visibleHints, setVisibleHints] = useState<string[]>([]);
  const [availableHints, setAvailableHints] = useState<string[]>([]);
  const [hintTimers, setHintTimers] = useState<Map<string, NodeJS.Timeout>>(new Map());

  // Reset hints when question changes
  useEffect(() => {
    setVisibleHints([]);
    setAvailableHints([]);
    // Clear any existing timers
    setHintTimers(prevTimers => {
      prevTimers.forEach(timer => clearTimeout(timer));
      return new Map();
    });
  }, [question.question_id]);

  // Handle hint visibility based on delay_seconds
  useEffect(() => {
    // Ensure question.hint is an array before proceeding
    if (!question.hint || !Array.isArray(question.hint) || question.hint.length === 0) return;

    const timers = new Map<string, NodeJS.Timeout>();
    const immediate: string[] = [];
    const delayed: string[] = [];

    question.hint.forEach((hint) => {
      // Ensure hint is an object with required properties
      if (!hint || typeof hint !== 'object' || !hint.text) return;

      if (hint.delay_seconds && hint.delay_seconds > 0) {
        // Add to delayed hints that will become available later
        const timer = setTimeout(() => {
          setAvailableHints(prev => [...prev, hint.text]);
        }, hint.delay_seconds * 1000);

        timers.set(hint.text, timer);
        delayed.push(hint.text);
      } else {
        // Show hints with no delay immediately
        immediate.push(hint.text);
      }
    });

    setVisibleHints(immediate);
    setHintTimers(timers);

    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [question.hint]);

  // Show a hint manually
  const showHint = (hintText: string) => {
    setVisibleHints(prev => [...prev, hintText]);
    setAvailableHints(prev => prev.filter(h => h !== hintText));
  };

  // Get the correct answer for display
  const getCorrectAnswer = () => {
    switch (question.type) {
      case "multiple_choice": {
        const mcQuestion = question as any;
        const correctOptions = mcQuestion.options?.filter((opt: any) => opt.is_correct) || [];
        return correctOptions.map((opt: any) => getLocalizedText(opt.text, locale)).join(", ");
      }
      case "true_false": {
        const tfQuestion = question as any;
        return tfQuestion.correct_answer ? "True" : "False";
      }
      case "short_answer": {
        const saQuestion = question as any;
        return saQuestion.correct_answers?.[0] || "No answer provided";
      }
      case "matching": {
        const matchQuestion = question as any;
        if (!matchQuestion.correct_pairs || !matchQuestion.stems || !matchQuestion.options) {
          return "Matching pairs not available";
        }
        return matchQuestion.correct_pairs.map((pair: any) => {
          const stem = matchQuestion.stems.find((s: any) => s.id === pair.stem_id);
          const option = matchQuestion.options.find((o: any) => o.id === pair.option_id);
          return `${getLocalizedText(stem?.text || "", locale)} → ${getLocalizedText(option?.text || "", locale)}`;
        }).join("; ");
      }
      case "fill_in_the_blank": {
        const fibQuestion = question as any;
        if (!fibQuestion.blanks) return "No blanks defined";
        return fibQuestion.blanks.map((blank: any, index: number) =>
          `Blank ${index + 1}: ${blank.correct_answers?.[0] || "No answer"}`
        ).join("; ");
      }
      case "essay": {
        return "Essay questions don't have a single correct answer";
      }
      default:
        return "Answer format not supported";
    }
  };

  const formatUserAnswer = (userAnswer: any) => {
    if (!userAnswer) return "No answer provided";

    switch (question.type) {
      case "multiple_choice": {
        const mcQuestion = question as any;
        if (Array.isArray(userAnswer)) {
          // Multiple selection
          const selectedOptions = mcQuestion.options?.filter((opt: any) => userAnswer.includes(opt.id)) || [];
          return selectedOptions.map((opt: any) => getLocalizedText(opt.text, locale)).join(", ");
        } else {
          // Single selection
          const selectedOption = mcQuestion.options?.find((opt: any) => opt.id === userAnswer);
          return selectedOption ? getLocalizedText(selectedOption.text, locale) : "Unknown option";
        }
      }
      case "true_false": {
        return userAnswer ? "True" : "False";
      }
      case "short_answer": {
        return String(userAnswer);
      }
      case "matching": {
        if (!Array.isArray(userAnswer)) return "Invalid answer format";
        const matchQuestion = question as any;
        return userAnswer.map((pair: any) => {
          const stem = matchQuestion.stems?.find((s: any) => s.id === pair.stem_id);
          const option = matchQuestion.options?.find((o: any) => o.id === pair.option_id);
          return `${getLocalizedText(stem?.text || "", locale)} → ${getLocalizedText(option?.text || "", locale)}`;
        }).join("; ");
      }
      case "fill_in_the_blank": {
        if (!Array.isArray(userAnswer)) return "Invalid answer format";
        return userAnswer.map((answer, index) => `Blank ${index + 1}: ${answer || "(empty)"}`).join("; ");
      }
      case "essay": {
        return String(userAnswer).substring(0, 100) + (String(userAnswer).length > 100 ? "..." : "");
      }
      default:
        return String(userAnswer);
    }
  };

  // Handle show answer button click
  const handleShowAnswer = () => {
    if (onAnswerRevealed) {
      onAnswerRevealed();
    }
  };

  // Safe JSON parsing function
  const safeJsonParse = (value: any, fallback: any = null) => {
    if (typeof value !== 'string') {
      return value; // Already parsed or not a string
    }

    try {
      return JSON.parse(value);
    } catch (error) {
      console.error('Failed to parse JSON:', error);
      return fallback;
    }
  };

  // Render question text with markdown support
  const renderQuestionText = () => {
    // Parse the question text if it's a JSON string
    const parsedText = safeJsonParse(question.text, { default: 'Question text' });
    const text = getLocalizedText(parsedText, locale);

    return (
      <div className="prose dark:prose-invert max-w-none">
        <ReactMarkdown>
          {text}
        </ReactMarkdown>
      </div>
    );
  };

  // Render media items
  const renderMedia = () => {
    if (!question.media || question.media.length === 0) return null;

    return (
      <div className="mt-4 space-y-4">
        {question.media.map((media, index) => {
          if (media.type === "image") {
            return (
              <figure key={index} className="relative">
                <Image
                  src={media.url}
                  alt={media.alt_text || "Question image"}
                  width={800}
                  height={600}
                  className="max-w-full rounded-md"
                  style={{ width: 'auto', height: 'auto' }}
                />
                {media.caption && (
                  <figcaption className="text-sm text-muted-foreground mt-2">
                    {media.caption}
                  </figcaption>
                )}
              </figure>
            );
          } else if (media.type === "video") {
            return (
              <figure key={index} className="relative">
                <video
                  src={media.url}
                  controls
                  className="max-w-full rounded-md"
                />
                {media.caption && (
                  <figcaption className="text-sm text-muted-foreground mt-2">
                    {media.caption}
                  </figcaption>
                )}
              </figure>
            );
          } else if (media.type === "audio") {
            return (
              <figure key={index} className="relative">
                <audio src={media.url} controls className="w-full" />
                {media.caption && (
                  <figcaption className="text-sm text-muted-foreground mt-2">
                    {media.caption}
                  </figcaption>
                )}
              </figure>
            );
          }
          return null;
        })}
      </div>
    );
  };

  // Render code snippet
  const renderCodeSnippet = () => {
    if (!question.code_snippet) return null;

    return (
      <div className="my-4">
        <CodeSnippetRenderer codeSnippet={question.code_snippet} />
      </div>
    );
  };

  // Render hints
  const renderHints = () => {
    const hasVisibleHints = visibleHints.length > 0;
    const hasAvailableHints = availableHints.length > 0;

    if (!hasVisibleHints && !hasAvailableHints) return null;

    return (
      <div className="mt-4 space-y-3">
        {hasVisibleHints && (
          <div className="p-4 bg-muted rounded-md">
            <h4 className="font-medium mb-2">💡 Hints:</h4>
            <ul className="list-disc pl-5 space-y-1">
              {visibleHints.map((hint, index) => (
                <li key={index} className="text-sm">{hint}</li>
              ))}
            </ul>
          </div>
        )}

        {hasAvailableHints && (
          <div className="flex flex-wrap gap-2">
            {availableHints.map((hint, index) => (
              <button
                key={index}
                onClick={() => showHint(hint)}
                className="px-3 py-1 text-sm bg-yellow-100 hover:bg-yellow-200 text-yellow-800 rounded-md transition-colors border border-yellow-300"
              >
                💡 Show Hint {visibleHints.length + index + 1}
              </button>
            ))}
          </div>
        )}
      </div>
    );
  };

  // Render feedback if showFeedback is true
  const renderFeedback = () => {
    if (!showFeedback) return null;

    // This is a simplified implementation - in a real app, you would need
    // to determine if the answer is correct based on question type
    const isCorrect = false; // Replace with actual logic

    if (isCorrect && question.feedback_correct) {
      return (
        <div className="mt-4 p-4 bg-green-50 text-green-800 dark:bg-green-900 dark:text-green-50 rounded-md">
          <ReactMarkdown>{question.feedback_correct}</ReactMarkdown>
        </div>
      );
    } else if (!isCorrect && question.feedback_incorrect) {
      return (
        <div className="mt-4 p-4 bg-red-50 text-red-800 dark:bg-red-900 dark:text-red-50 rounded-md">
          <ReactMarkdown>{question.feedback_incorrect}</ReactMarkdown>
        </div>
      );
    }

    return null;
  };

  // Render explanation if showExplanation is true
  const renderExplanation = () => {
    if (!showExplanation || !question.explanation) return null;

    return (
      <div className="mt-4 p-4 bg-blue-50 text-blue-800 dark:bg-blue-900 dark:text-blue-50 rounded-md">
        <h4 className="font-medium mb-2">Explanation:</h4>
        <ReactMarkdown>{getLocalizedText(question.explanation, locale)}</ReactMarkdown>
      </div>
    );
  };

  // Check if question type supports visual answer highlighting
  const supportsVisualHighlighting = () => {
    return question.type === "multiple_choice";
  };

  // Render show answer section
  const renderShowAnswer = () => {
    if (!showAnswerButton && !showAnswer) return null;

    return (
      <div className="mt-4">
        {!showAnswer && showAnswerButton && (
          <button
            onClick={handleShowAnswer}
            className="px-4 py-2 bg-orange-100 hover:bg-orange-200 text-orange-800 rounded-md transition-colors border border-orange-300 font-medium"
          >
            🔍 Show Answer
          </button>
        )}

        {/* Only show text-based answer for questions that don't support visual highlighting */}
        {showAnswer && !supportsVisualHighlighting() && (
          <div className="p-4 bg-green-50 text-green-800 dark:bg-green-900 dark:text-green-50 rounded-md">
            <h4 className="font-medium mb-2">✅ Correct Answer:</h4>
            <div className="text-sm font-mono bg-white dark:bg-gray-800 p-2 rounded border">
              {getCorrectAnswer()}
            </div>
            <p className="text-xs text-green-700 mt-2">
              ⚠️ This question will not count toward your final score.
            </p>
          </div>
        )}

        {/* Show a message for multiple choice questions that answers are highlighted */}
        {showAnswer && supportsVisualHighlighting() && (
          <div className="p-4 bg-blue-50 text-blue-800 dark:bg-blue-900 dark:text-blue-50 rounded-md">
            <h4 className="font-medium mb-2">💡 Answer Revealed:</h4>
            <p className="text-sm">The correct answer(s) are highlighted in green above.</p>
            <p className="text-xs text-blue-700 mt-2">
              ⚠️ This question will not count toward your final score.
            </p>
          </div>
        )}
      </div>
    );
  };

  // Render the appropriate question type component
  const renderQuestionTypeComponent = () => {
    switch (question.type) {
      case "multiple_choice":
        return (
          <MultipleChoiceQuestion
            question={question as any}
            answer={answer}
            onAnswerChange={onAnswerChange}
            locale={locale}
            showCorrectAnswers={showAnswer}
          />
        );
      case "true_false":
        return (
          <TrueFalseQuestion
            question={question as any}
            answer={answer}
            onAnswerChange={onAnswerChange}
          />
        );
      case "short_answer":
        return (
          <ShortAnswerQuestion
            question={question as any}
            answer={answer}
            onAnswerChange={onAnswerChange}
          />
        );
      case "matching":
        return (
          <MatchingQuestion
            question={question as any}
            answer={answer}
            onAnswerChange={onAnswerChange}
            locale={locale}
          />
        );
      case "fill_in_the_blank":
        return (
          <FillInTheBlankQuestion
            question={question as any}
            answer={answer}
            onAnswerChange={onAnswerChange}
            locale={locale}
          />
        );
      case "essay":
        return (
          <EssayQuestion
            question={question as any}
            answer={answer}
            onAnswerChange={onAnswerChange}
          />
        );
      default:
        return (
          <div className="p-4 bg-yellow-50 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-50 rounded-md">
            Unsupported question type: {(question as any).type}
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        {renderQuestionText()}
        {renderMedia()}
        {renderCodeSnippet()}
      </div>

      <div className="mt-4">
        {renderQuestionTypeComponent()}
      </div>

      {/* Immediate Answer Feedback */}
      {answerDisplayConfig?.immediateAnswerFeedback && isAnswered && questionResult && (
        <div className={`p-4 rounded-md border ${
          questionResult.isCorrect
            ? 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200'
            : 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200'
        }`}>
          <div className="flex items-center gap-2 mb-2">
            {questionResult.isCorrect ? (
              <>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="font-medium">Correct!</span>
              </>
            ) : (
              <>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <span className="font-medium">Incorrect</span>
              </>
            )}
          </div>

          {/* Show user's answer if configured */}
          {answerDisplayConfig?.showUserAnswer && (
            <div className="mb-2">
              <span className="text-sm font-medium">Your answer: </span>
              <span className="text-sm">{formatUserAnswer(questionResult.userAnswer)}</span>
            </div>
          )}

          {/* Show correct answer if configured and user was wrong */}
          {answerDisplayConfig?.showCorrectAnswer && !questionResult.isCorrect && (
            <div className="mb-2">
              <span className="text-sm font-medium">Correct answer: </span>
              <span className="text-sm font-medium text-green-700 dark:text-green-300">
                {getCorrectAnswer()}
              </span>
            </div>
          )}

          {/* Highlight correct answer if configured and user was wrong */}
          {answerDisplayConfig?.highlightCorrectAnswer && !questionResult.isCorrect && (
            <div className="text-xs text-muted-foreground mt-2">
              💡 The correct answer is highlighted above
            </div>
          )}
        </div>
      )}

      {renderHints()}
      {renderShowAnswer()}
      {renderFeedback()}
      {renderExplanation()}
    </div>
  );
};

export default QuestionRenderer;
