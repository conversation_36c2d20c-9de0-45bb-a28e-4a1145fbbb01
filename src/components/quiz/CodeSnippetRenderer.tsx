"use client";

import React from "react";
import { Prism as Syntax<PERSON><PERSON>lighter } from "react-syntax-highlighter";
import { vscDarkPlus, vs } from "react-syntax-highlighter/dist/esm/styles/prism";
import { useTheme } from "next-themes";
import { CodeSnippet } from "@/types/qfjson";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Copy, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

interface CodeSnippetRendererProps {
  codeSnippet: CodeSnippet;
  className?: string;
}

const CodeSnippetRenderer: React.FC<CodeSnippetRendererProps> = ({
  codeSnippet,
  className = "",
}) => {
  const { theme } = useTheme();
  
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(codeSnippet.code);
      toast.success("Code copied to clipboard!");
    } catch (err) {
      toast.error("Failed to copy code");
    }
  };

  // Custom line props to highlight specific lines
  const getLineProps = (lineNumber: number) => {
    const isHighlighted = codeSnippet.highlight_lines?.includes(lineNumber);
    
    return {
      style: {
        backgroundColor: isHighlighted 
          ? theme === 'dark' 
            ? 'rgba(255, 255, 0, 0.1)' 
            : 'rgba(255, 255, 0, 0.2)'
          : 'transparent',
        display: 'block',
        width: '100%',
        paddingLeft: '1rem',
        paddingRight: '1rem',
        borderLeft: isHighlighted 
          ? '3px solid #fbbf24' 
          : '3px solid transparent',
      },
    };
  };

  // Language mapping for better display names
  const getLanguageDisplayName = (lang: string): string => {
    const languageMap: Record<string, string> = {
      'javascript': 'JavaScript',
      'typescript': 'TypeScript',
      'python': 'Python',
      'java': 'Java',
      'csharp': 'C#',
      'cpp': 'C++',
      'c': 'C',
      'php': 'PHP',
      'ruby': 'Ruby',
      'go': 'Go',
      'rust': 'Rust',
      'sql': 'SQL',
      'bash': 'Bash',
      'powershell': 'PowerShell',
      'json': 'JSON',
      'xml': 'XML',
      'yaml': 'YAML',
      'html': 'HTML',
      'css': 'CSS',
      'scss': 'SCSS',
      'markdown': 'Markdown',
    };
    
    return languageMap[lang.toLowerCase()] || lang.toUpperCase();
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <CardTitle className="text-sm font-medium">
              {codeSnippet.filename || 'Code Snippet'}
            </CardTitle>
            <Badge variant="secondary" className="text-xs">
              {getLanguageDisplayName(codeSnippet.language)}
            </Badge>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={copyToClipboard}
            className="h-8 w-8 p-0"
            title="Copy code"
          >
            <Copy className="h-3 w-3" />
          </Button>
        </div>
        {codeSnippet.caption && (
          <p className="text-sm text-muted-foreground mt-1">
            {codeSnippet.caption}
          </p>
        )}
      </CardHeader>
      <CardContent className="p-0">
        <div className="relative">
          <SyntaxHighlighter
            language={codeSnippet.language}
            style={theme === 'dark' ? vscDarkPlus : vs}
            showLineNumbers={true}
            lineNumberStyle={{
              minWidth: '3em',
              paddingRight: '1em',
              textAlign: 'right',
              userSelect: 'none',
              opacity: 0.6,
            }}
            customStyle={{
              margin: 0,
              borderRadius: '0 0 0.5rem 0.5rem',
              fontSize: '0.875rem',
              lineHeight: '1.5',
            }}
            lineProps={(lineNumber) => getLineProps(lineNumber)}
            wrapLines={true}
            wrapLongLines={true}
          >
            {codeSnippet.code}
          </SyntaxHighlighter>
          
          {/* Highlight indicator */}
          {codeSnippet.highlight_lines && codeSnippet.highlight_lines.length > 0 && (
            <div className="absolute top-2 right-2">
              <Badge variant="outline" className="text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                {codeSnippet.highlight_lines.length === 1 
                  ? `Line ${codeSnippet.highlight_lines[0]} highlighted`
                  : `${codeSnippet.highlight_lines.length} lines highlighted`
                }
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default CodeSnippetRenderer;
