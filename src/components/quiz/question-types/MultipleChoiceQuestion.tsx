"use client";

import React from "react";
import { MultipleChoiceQuestion as MultipleChoiceQuestionType } from "@/types/qfjson";
import { getLocalizedText } from "@/lib/utils/qfjson-parser";
import { cn } from "@/lib/utils";

interface MultipleChoiceQuestionProps {
  question: MultipleChoiceQuestionType;
  answer: string | string[];
  onAnswerChange: (answer: string | string[]) => void;
  locale?: string;
  disabled?: boolean;
  showCorrectAnswers?: boolean;
}

const MultipleChoiceQuestion: React.FC<MultipleChoiceQuestionProps> = ({
  question,
  answer,
  onAnswerChange,
  locale = "en-US",
  disabled = false,
  showCorrectAnswers = false,
}) => {
  const handleSingleChoiceChange = (optionId: string) => {
    onAnswerChange(optionId);
  };

  const handleMultipleChoiceChange = (optionId: string) => {
    const currentAnswers = Array.isArray(answer) ? answer : answer ? [answer] : [];

    if (currentAnswers.includes(optionId)) {
      // Remove the option if already selected
      onAnswerChange(currentAnswers.filter(id => id !== optionId));
    } else {
      // Add the option if not already selected
      onAnswerChange([...currentAnswers, optionId]);
    }
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        {question.options.map((option) => {
          const isSelected = Array.isArray(answer)
            ? answer.includes(option.id)
            : answer === option.id;
          const isCorrect = option.is_correct;
          const shouldHighlightCorrect = showCorrectAnswers && isCorrect;

          return (
            <div
              key={option.id}
              className={cn(
                "flex items-start space-x-3 p-3 border rounded-md transition-colors",
                // Show correct answer highlighting when showCorrectAnswers is true
                shouldHighlightCorrect && "border-green-500 bg-green-50 dark:bg-green-900/20",
                // Show selected state when not showing answers
                !showCorrectAnswers && isSelected && "border-primary bg-primary/5",
                // Default state
                !showCorrectAnswers && !isSelected && "border-input hover:border-primary/50 cursor-pointer",
                // Disable interaction when showing answers
                showCorrectAnswers && "cursor-default",
                disabled && "opacity-60 cursor-not-allowed"
              )}
              onClick={() => {
                if (disabled || showCorrectAnswers) return;
                question.single_correct_answer
                  ? handleSingleChoiceChange(option.id)
                  : handleMultipleChoiceChange(option.id);
              }}
            >
              <div className="flex-shrink-0 mt-0.5">
                {question.single_correct_answer ? (
                  <div className={cn(
                    "w-5 h-5 border-2 rounded-full flex items-center justify-center",
                    shouldHighlightCorrect ? "border-green-500" :
                    isSelected ? "border-primary" : "border-muted-foreground"
                  )}>
                    {(isSelected || shouldHighlightCorrect) && (
                      <div className={cn(
                        "w-3 h-3 rounded-full",
                        shouldHighlightCorrect ? "bg-green-500" : "bg-primary"
                      )} />
                    )}
                  </div>
                ) : (
                  <div className={cn(
                    "w-5 h-5 border-2 rounded-sm flex items-center justify-center",
                    shouldHighlightCorrect ? "border-green-500 bg-green-500" :
                    isSelected ? "border-primary bg-primary" : "border-muted-foreground"
                  )}>
                    {(isSelected || shouldHighlightCorrect) && (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="3"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="w-3 h-3 text-primary-foreground"
                      >
                        <polyline points="20 6 9 17 4 12" />
                      </svg>
                    )}
                  </div>
                )}
              </div>
              <div className="flex-1">
                <div className={cn(
                  "text-sm font-medium",
                  shouldHighlightCorrect && "text-green-700 dark:text-green-300"
                )}>
                  {getLocalizedText(option.text, locale)}
                  {shouldHighlightCorrect && (
                    <span className="ml-2 text-green-600 dark:text-green-400">
                      ✓ Correct
                    </span>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MultipleChoiceQuestion;
