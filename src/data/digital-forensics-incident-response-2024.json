{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "digital-forensics-incident-response-2024", "title": "Digital Forensics & Incident Response", "description": "Comprehensive digital forensics and incident response techniques covering memory analysis, disk forensics, network forensics, and timeline reconstruction.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T16:00:00Z", "tags": ["digital-forensics", "incident-response", "memory-analysis", "disk-forensics", "volatility", "autopsy"], "passing_score_percentage": 85, "time_limit_minutes": 50, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "memory_dump_analysis", "type": "multiple_choice", "text": "During incident response, you've acquired a memory dump from a compromised Windows 10 system. Which Volatility command would **best identify** running processes that might be malicious or hidden?", "points": 2, "difficulty": "advanced", "options": [{"id": "opt1", "text": "volatility -f memory.dmp --profile=Win10x64 pslist", "is_correct": false, "feedback": "pslist shows running processes but may miss hidden or terminated processes."}, {"id": "opt2", "text": "volatility -f memory.dmp --profile=Win10x64 psscan", "is_correct": true, "feedback": "Correct! psscan finds process structures in memory, including hidden and terminated processes."}, {"id": "opt3", "text": "volatility -f memory.dmp --profile=Win10x64 filescan", "is_correct": false, "feedback": "filescan identifies file objects, not processes."}, {"id": "opt4", "text": "volatility -f memory.dmp --profile=Win10x64 netscan", "is_correct": false, "feedback": "netscan shows network connections, not process information."}], "hint": [{"text": "Consider which command can find processes that might be hidden from normal process lists.", "delay_seconds": 30}, {"text": "psscan searches for process structures throughout memory, finding hidden processes.", "delay_seconds": 60}], "feedback_correct": "Excellent! psscan is the most comprehensive method for process discovery in memory analysis.", "feedback_incorrect": "Use psscan to find all process structures in memory, including hidden ones.", "explanation": "**Memory Forensics - Process Analysis:**\\n\\n**Volatility Process Commands:**\\n```bash\\n# List active processes\\nvolatility -f memory.dmp --profile=Win10x64 pslist\\n\\n# Scan for all process structures\\nvolatility -f memory.dmp --profile=Win10x64 psscan\\n\\n# Show process tree\\nvolatility -f memory.dmp --profile=Win10x64 pstree\\n\\n# Find hidden processes\\nvolatility -f memory.dmp --profile=Win10x64 psxview\\n```\\n\\n**Why psscan is Superior:**\\n1. **Comprehensive Search**: Scans entire memory space for process structures\\n2. **Hidden Process Detection**: Finds processes hidden by rootkits\\n3. **Terminated Process Recovery**: Locates recently terminated processes\\n4. **EPROCESS Structure**: Directly searches for Windows process structures\\n\\n**Malware Detection Techniques:**\\n- **Process Hollowing**: Legitimate process with malicious code\\n- **DLL Injection**: Malicious DLLs in legitimate processes\\n- **Rootkit Hiding**: Processes hidden from standard APIs\\n- **Persistence Mechanisms**: Services, scheduled tasks, registry\\n\\n**Advanced Analysis Commands:**\\n```bash\\n# Dump process memory\\nvolatility -f memory.dmp --profile=Win10x64 procdump -p PID -D output/\\n\\n# Analyze process handles\\nvolatility -f memory.dmp --profile=Win10x64 handles -p PID\\n\\n# Check process command lines\\nvolatility -f memory.dmp --profile=Win10x64 cmdline\\n\\n# Examine loaded DLLs\\nvolatility -f memory.dmp --profile=Win10x64 dlllist -p PID\\n```", "single_correct_answer": true}]}}