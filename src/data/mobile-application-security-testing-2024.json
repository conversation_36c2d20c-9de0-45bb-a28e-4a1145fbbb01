{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "mobile-application-security-testing-2024", "title": "Mobile Application Security Testing", "description": "Comprehensive mobile app security testing covering Android and iOS platforms, static analysis, dynamic analysis, and common vulnerability patterns.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T18:00:00Z", "tags": ["mobile-security", "android", "ios", "static-analysis", "dynamic-analysis", "owasp-masvs"], "passing_score_percentage": 80, "time_limit_minutes": 45, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "android_manifest_analysis", "type": "multiple_choice", "text": "During Android app security testing, you find this permission in AndroidManifest.xml: `<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />`. What security risk does this present?", "points": 2, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "App can read sensitive data from other applications", "is_correct": false, "feedback": "This permission doesn't grant access to other apps' private data."}, {"id": "opt2", "text": "App can write files to shared external storage accessible by other apps", "is_correct": true, "feedback": "Correct! This allows writing to shared storage that other apps can access."}, {"id": "opt3", "text": "App can modify system settings and configurations", "is_correct": false, "feedback": "This would require WRITE_SETTINGS or similar system permissions."}, {"id": "opt4", "text": "App can install other applications without user consent", "is_correct": false, "feedback": "This would require INSTALL_PACKAGES permission (system apps only)."}], "hint": [{"text": "Consider what 'external storage' means in Android and who can access it.", "delay_seconds": 30}, {"text": "External storage is shared between apps and accessible by any app with read permissions.", "delay_seconds": 60}], "feedback_correct": "Excellent! External storage permissions create data exposure risks.", "feedback_incorrect": "WRITE_EXTERNAL_STORAGE allows writing to shared storage accessible by other apps.", "explanation": "**Android Storage Security Model:**\\n\\n**Storage Types:**\\n1. **Internal Storage**: App-private, not accessible by other apps\\n2. **External Storage**: Shared, accessible by apps with permissions\\n3. **Scoped Storage**: Android 10+ restricted external access\\n\\n**Security Implications:**\\n- **Data Exposure**: Files written to external storage can be read by other apps\\n- **Data Tampering**: Other apps can modify files in external storage\\n- **Privacy Leaks**: Sensitive data accidentally stored in shared locations\\n\\n**OWASP MASVS Requirements:**\\n- **MSTG-STORAGE-1**: Sensitive data not stored in external storage\\n- **MSTG-STORAGE-2**: No sensitive data in application logs\\n- **MSTG-STORAGE-3**: No sensitive data shared with third parties\\n\\n**Best Practices:**\\n```xml\\n<!-- Avoid if possible -->\\n<uses-permission android:name=\\\"android.permission.WRITE_EXTERNAL_STORAGE\\\" />\\n\\n<!-- Use scoped storage instead -->\\n<uses-permission android:name=\\\"android.permission.READ_MEDIA_IMAGES\\\" />\\n```\\n\\n**Testing Methodology:**\\n1. **Static Analysis**: Review AndroidManifest.xml permissions\\n2. **Dynamic Analysis**: Monitor file system access\\n3. **Data Flow Analysis**: Track sensitive data storage\\n4. **Runtime Testing**: Verify data protection mechanisms", "single_correct_answer": true}]}}