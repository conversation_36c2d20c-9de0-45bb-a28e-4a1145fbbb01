{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "advanced-sql-injection-techniques", "title": "Advanced SQL Injection Techniques & Bypass Methods", "description": "Advanced SQL injection techniques including blind SQLi, time-based attacks, WAF bypass methods, and NoSQL injection scenarios based on real-world exploits.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T20:00:00Z", "tags": ["sql-injection", "blind-sqli", "waf-bypass", "nosql", "database-security"], "passing_score_percentage": 85, "time_limit_minutes": 50, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "blind_sqli_time_based_detection", "type": "multiple_choice", "text": "You're testing a web application that doesn't return database errors or visible output changes. Which technique would be most effective for detecting **blind SQL injection** vulnerabilities?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Union-based injection with SELECT statements", "is_correct": false, "feedback": "Union-based attacks require visible output, which isn't available in blind SQLi scenarios."}, {"id": "opt2", "text": "Time-based injection using SLEEP() or WAITFOR DELAY", "is_correct": true, "feedback": "Correct! Time-based attacks are ideal for blind SQLi as they rely on response timing rather than visible output."}, {"id": "opt3", "text": "Error-based injection with invalid syntax", "is_correct": false, "feedback": "Error-based attacks won't work if the application doesn't display database errors."}, {"id": "opt4", "text": "Boolean-based injection without conditional logic", "is_correct": false, "feedback": "Boolean-based attacks still require some form of observable difference in responses."}], "hint": [{"text": "Consider what you can observe when the application doesn't show errors or data changes.", "delay_seconds": 30}, {"text": "Think about how response timing can be used as a side channel for information extraction.", "delay_seconds": 60}], "feedback_correct": "Excellent! Time-based blind SQLi is a crucial technique for stealth testing.", "feedback_incorrect": "In blind SQLi scenarios, time-based attacks are most effective since they don't rely on visible output.", "explanation": "**Time-Based Blind SQL Injection:**\\n\\n**Scenario**: Application doesn't show errors or data changes\\n\\n**Technique**: Use database time delay functions to infer information\\n\\n**MySQL Example:**\\n```sql\\n# Test for vulnerability\\n' AND (SELECT SLEEP(5))--\\n\\n# Extract data character by character\\n' AND (SELECT CASE WHEN (ASCII(SUBSTRING((SELECT database()),1,1))>64) THEN SLEEP(5) ELSE 0 END)--\\n```\\n\\n**SQL Server Example:**\\n```sql\\n'; WAITFOR DELAY '00:00:05'--\\n\\n# Conditional timing\\n'; IF (ASCII(SUBSTRING((SELECT DB_NAME()),1,1))>64) WAITFOR DELAY '00:00:05'--\\n```\\n\\n**Detection Process:**\\n1. **Baseline**: Measure normal response time\\n2. **Injection**: Insert time delay payload\\n3. **Analysis**: Compare response times\\n4. **Confirmation**: Repeat with different delays\\n\\n**Automation Tools:**\\n- **SQLMap**: `sqlmap -u 'url' --technique=T`\\n- **Custom Scripts**: Python with requests library\\n- **Burp Suite**: Intruder with time-based payloads\\n\\n**Defense:**\\n- Parameterized queries/prepared statements\\n- Input validation and sanitization\\n- Database user privilege restrictions\\n- Web Application Firewalls (WAF)\\n- Response time normalization"}, {"question_id": "waf_bypass_encoding_techniques", "type": "short_answer", "text": "A web application firewall (WAF) is blocking your SQL injection attempts. What encoding technique could you use to bypass filters that block the word 'UNION' in your payload?", "points": 2, "difficulty": "advanced", "correct_answers": ["URL encoding", "Double URL encoding", "Hex encoding", "Unicode encoding", "HTML entity encoding", "Base64 encoding"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Consider how different character encodings can represent the same text in ways that bypass pattern matching.", "delay_seconds": 30}, {"text": "Think about URL encoding, hex encoding, or other character representation methods.", "delay_seconds": 60}], "feedback_correct": "Correct! Encoding techniques are essential for WAF bypass in advanced SQL injection.", "feedback_incorrect": "Various encoding techniques like URL encoding, hex encoding, or Unicode can bypass WAF filters.", "explanation": "**WAF Bypass Encoding Techniques:**\\n\\n**Common Encoding Methods:**\\n\\n**1. URL Encoding:**\\n```sql\\n# Original: UNION SELECT\\n# Encoded: %55%4E%49%4F%4E%20%53%45%4C%45%43%54\\n```\\n\\n**2. Double URL Encoding:**\\n```sql\\n# Original: UNION\\n# Single: %55%4E%49%4F%4E\\n# Double: %2555%254E%2549%254F%254E\\n```\\n\\n**3. Hex Encoding:**\\n```sql\\n# MySQL: SELECT 0x554E494F4E (hex for 'UNION')\\n# Concatenation: CONCAT(0x554E494F4E, 0x2053454C454354)\\n```\\n\\n**4. Unicode Encoding:**\\n```sql\\n# Unicode normalization\\n# U+FF35 (fullwidth U) instead of U+0055\\n```\\n\\n**5. Mixed Case + Comments:**\\n```sql\\n# Original: UNION SELECT\\n# Obfuscated: uNiOn/**/sElEcT\\n# With comments: UN/*comment*/ION SE/**/LECT\\n```\\n\\n**6. Alternative Representations:**\\n```sql\\n# Char function: CHAR(85,78,73,79,78) = 'UNION'\\n# Concatenation: 'UN'+'ION'\\n# Variables: SET @a='UNION'; SELECT @a\\n```\\n\\n**Advanced Bypass Techniques:**\\n- **HTTP Parameter Pollution**: Splitting payloads across parameters\\n- **Content-Type Manipulation**: Using different MIME types\\n- **HTTP Method Variation**: POST vs GET vs PUT\\n- **Fragment Injection**: Breaking keywords across HTTP headers\\n\\n**Testing Methodology:**\\n1. Identify blocked keywords/patterns\\n2. Test various encoding methods\\n3. Combine multiple bypass techniques\\n4. Monitor WAF logs for detection patterns"}]}}