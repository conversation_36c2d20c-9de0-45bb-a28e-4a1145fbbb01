{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "advanced-network-reconnaissance", "title": "Advanced Network Reconnaissance Techniques", "description": "Advanced network reconnaissance and enumeration techniques used in real-world penetration testing, including stealth scanning, service enumeration, and OSINT gathering.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T23:00:00Z", "tags": ["reconnaissance", "nmap", "osint", "enumeration", "stealth-scanning"], "passing_score_percentage": 85, "time_limit_minutes": 45, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "stealth_scanning_tcp_syn", "type": "multiple_choice", "text": "During a penetration test, you need to perform reconnaissance without triggering IDS alerts. Which Nmap scanning technique would be **most stealthy** while still providing accurate port state information?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "TCP Connect scan (-sT) with random timing", "is_correct": false, "feedback": "TCP Connect scans complete the full handshake, making them more detectable."}, {"id": "opt2", "text": "SYN stealth scan (-sS) with decoy hosts and timing controls", "is_correct": true, "feedback": "Correct! SYN scans with decoys and timing controls provide stealth while maintaining accuracy."}, {"id": "opt3", "text": "UDP scan (-sU) with version detection enabled", "is_correct": false, "feedback": "UDP scans are slower and version detection increases network noise."}, {"id": "opt4", "text": "FIN scan (-sF) without any timing controls", "is_correct": false, "feedback": "FIN scans can be stealthy but may provide less reliable results than SYN scans."}], "hint": [{"text": "Consider which scan type doesn't complete the full TCP handshake.", "delay_seconds": 30}, {"text": "Think about techniques that can hide your source IP and control scan timing.", "delay_seconds": 60}], "feedback_correct": "Excellent! SYN stealth scanning with proper evasion techniques is ideal for covert reconnaissance.", "feedback_incorrect": "SYN stealth scans with decoys and timing controls provide the best balance of stealth and accuracy.", "explanation": "**Advanced Stealth Scanning Techniques:**\\n\\n**SYN Stealth Scan Advantages:**\\n- Doesn't complete TCP handshake (half-open)\\n- Faster than TCP Connect scans\\n- Less likely to be logged by applications\\n- Works against most TCP services\\n\\n**Nmap Stealth Command Examples:**\\n```bash\\n# Basic SYN stealth scan\\nnmap -sS target.com\\n\\n# Advanced stealth with evasion\\nnmap -sS -D RND:10 -T2 --randomize-hosts target.com\\n\\n# Maximum stealth configuration\\nnmap -sS -f -D *************,*************,ME -T1 \\\\\\n  --source-port 53 --data-length 25 target.com\\n```\\n\\n**Evasion Techniques Explained:**\\n\\n**1. Decoy Scanning (-D):**\\n```bash\\n# Use specific decoys\\nnmap -sS -D ********,********,ME target.com\\n\\n# Random decoys\\nnmap -sS -D RND:5 target.com\\n```\\n\\n**2. Timing Controls (-T0 to -T5):**\\n- **T0 (Paranoid)**: 5-minute delays between probes\\n- **T1 (Sneaky)**: 15-second delays\\n- **T2 (Polite)**: 0.4-second delays\\n- **T3 (Normal)**: Default timing\\n- **T4 (Aggressive)**: Faster scans\\n- **T5 (Insane)**: Maximum speed\\n\\n**3. Fragmentation (-f):**\\n```bash\\n# Fragment packets to evade firewalls\\nnmap -sS -f target.com\\n\\n# Use specific MTU\\nnmap -sS --mtu 16 target.com\\n```\\n\\n**4. Source Port Manipulation:**\\n```bash\\n# Use common source ports\\nnmap -sS --source-port 53 target.com  # DNS\\nnmap -sS --source-port 80 target.com  # HTTP\\nnmap -sS --source-port 443 target.com # HTTPS\\n```\\n\\n**5. Data Padding:**\\n```bash\\n# Add random data to packets\\nnmap -sS --data-length 25 target.com\\n\\n# Use specific data\\nnmap -sS --data-string \\\"HTTP/1.1\\\" target.com\\n```\\n\\n**Detection Evasion Best Practices:**\\n1. **Randomize scan order**: `--randomize-hosts`\\n2. **Vary timing**: Mix different timing templates\\n3. **Use legitimate source ports**: 53, 80, 443\\n4. **Fragment packets**: Evade simple packet filters\\n5. **Limit concurrent scans**: Avoid overwhelming targets\\n\\n**IDS/IPS Evasion:**\\n- Monitor for scan detection signatures\\n- Use distributed scanning from multiple sources\\n- Implement scan scheduling over extended periods\\n- Combine with legitimate traffic patterns"}]}}