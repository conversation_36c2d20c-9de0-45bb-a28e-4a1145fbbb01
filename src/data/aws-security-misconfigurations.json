{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "aws-security-misconfigurations", "title": "AWS Security Misconfigurations & Exploitation", "description": "Real-world AWS security misconfigurations, exploitation techniques, and remediation strategies based on actual cloud security incidents and research.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T21:00:00Z", "tags": ["aws", "cloud-security", "misconfigurations", "s3", "iam", "ec2"], "passing_score_percentage": 80, "time_limit_minutes": 40, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "s3_bucket_public_exposure_2019", "type": "multiple_choice", "text": "In 2019, a major data breach occurred when a company accidentally exposed 540 million Facebook user records in an S3 bucket. What was the **primary misconfiguration** that led to this exposure?", "points": 3, "difficulty": "intermediate", "single_correct_answer": true, "options": [{"id": "opt1", "text": "S3 bucket with public read permissions and no access logging", "is_correct": true, "feedback": "Correct! The bucket was configured with public read access, making all data accessible to anyone."}, {"id": "opt2", "text": "Weak IAM role permissions with overly broad S3 access", "is_correct": false, "feedback": "While IAM misconfigurations are common, this specific incident involved direct bucket permissions."}, {"id": "opt3", "text": "Unencrypted S3 bucket with default AWS KMS keys", "is_correct": false, "feedback": "Encryption wouldn't prevent access if the bucket permissions allow public read."}, {"id": "opt4", "text": "S3 bucket in the wrong AWS region with cross-region replication", "is_correct": false, "feedback": "Region placement doesn't affect bucket security if permissions are misconfigured."}], "hint": [{"text": "This incident involved direct access to the S3 bucket without authentication.", "delay_seconds": 30}, {"text": "The misconfiguration was in the bucket's access control list (ACL) or bucket policy.", "delay_seconds": 60}], "feedback_correct": "Excellent! Understanding S3 bucket permissions is crucial for cloud security.", "feedback_incorrect": "The key issue was public read permissions on the S3 bucket, allowing unauthenticated access.", "explanation": "**S3 Public Bucket Exposure Analysis:**\\n\\n**Incident Details:**\\n- **Company**: Cultura Colectiva (Mexican media company)\\n- **Data**: 540 million Facebook user records\\n- **Discovery**: Security researcher found publicly accessible bucket\\n- **Impact**: Names, IDs, comments, reactions, account names\\n\\n**Technical Misconfiguration:**\\n```json\\n{\\n  \\\"Version\\\": \\\"2012-10-17\\\",\\n  \\\"Statement\\\": [\\n    {\\n      \\\"Sid\\\": \\\"PublicReadGetObject\\\",\\n      \\\"Effect\\\": \\\"Allow\\\",\\n      \\\"Principal\\\": \\\"*\\\",\\n      \\\"Action\\\": \\\"s3:GetObject\\\",\\n      \\\"Resource\\\": \\\"arn:aws:s3:::bucket-name/*\\\"\\n    }\\n  ]\\n}\\n```\\n\\n**How to Detect:**\\n```bash\\n# Check bucket permissions\\naws s3api get-bucket-acl --bucket bucket-name\\n\\n# List public buckets\\naws s3api list-buckets --query 'Buckets[?contains(Name, `public`)]'\\n\\n# Check bucket policy\\naws s3api get-bucket-policy --bucket bucket-name\\n```\\n\\n**Prevention Strategies:**\\n1. **Block Public Access**: Enable S3 Block Public Access settings\\n2. **Least Privilege**: Use specific IAM policies instead of wildcard permissions\\n3. **Monitoring**: CloudTrail logging and GuardDuty alerts\\n4. **Automation**: AWS Config rules for compliance checking\\n\\n**Secure S3 Configuration:**\\n```bash\\n# Block all public access\\naws s3api put-public-access-block --bucket bucket-name \\\\\\n  --public-access-block-configuration \\\\\\n  BlockPublicAcls=true,IgnorePublicAcls=true,BlockPublicPolicy=true,RestrictPublicBuckets=true\\n```\\n\\n**Lessons Learned:**\\n- Default S3 buckets should never be public\\n- Regular security audits of cloud resources\\n- Implement infrastructure as code for consistent security\\n- Use AWS Security Hub for centralized security monitoring"}]}}