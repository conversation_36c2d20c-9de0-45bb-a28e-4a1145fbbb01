{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "api-security-testing-practical", "title": "API Security Testing - Practical Exploitation", "description": "Hands-on API security testing techniques including REST/GraphQL vulnerabilities, authentication bypass, and real-world API exploitation scenarios.", "author": "QuizFlow Security Team", "creation_date": "2024-01-26T05:00:00Z", "tags": ["api-security", "rest", "graphql", "authentication", "practical-testing"], "passing_score_percentage": 80, "time_limit_minutes": 40, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "graphql_introspection_2022", "type": "multiple_choice", "text": "During a 2022 bug bounty program, researchers discovered that a major e-commerce platform's GraphQL API had introspection enabled in production. What was the **primary security risk** this exposed?", "points": 3, "difficulty": "intermediate", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Complete API schema disclosure revealing sensitive fields and operations", "is_correct": true, "feedback": "Correct! GraphQL introspection exposes the entire API schema, revealing sensitive data structures and operations."}, {"id": "opt2", "text": "SQL injection vulnerabilities in the GraphQL resolver functions", "is_correct": false, "feedback": "While SQL injection can occur in resolvers, introspection specifically exposes schema information."}, {"id": "opt3", "text": "Cross-site scripting (XSS) through GraphQL query responses", "is_correct": false, "feedback": "XSS is a different vulnerability class; introspection relates to information disclosure."}, {"id": "opt4", "text": "Denial of service through complex nested GraphQL queries", "is_correct": false, "feedback": "Query complexity attacks are separate from introspection-based information disclosure."}], "hint": [{"text": "Consider what information GraphQL introspection queries can reveal about an API.", "delay_seconds": 30}, {"text": "Think about how attackers can use schema information to craft targeted attacks.", "delay_seconds": 60}], "feedback_correct": "Excellent! Understanding GraphQL introspection risks is crucial for API security.", "feedback_incorrect": "GraphQL introspection exposes the complete API schema, revealing sensitive operations and data structures.", "explanation": "**GraphQL Introspection Security Analysis:**\\n\\n**What is GraphQL Introspection?**\\nIntrospection allows clients to query the GraphQL schema to discover available types, fields, and operations.\\n\\n**Introspection Query Example:**\\n```graphql\\nquery IntrospectionQuery {\\n  __schema {\\n    queryType {\\n      name\\n      fields {\\n        name\\n        description\\n        type {\\n          name\\n          kind\\n        }\\n      }\\n    }\\n    mutationType {\\n      name\\n      fields {\\n        name\\n        args {\\n          name\\n          type {\\n            name\\n          }\\n        }\\n      }\\n    }\\n  }\\n}\\n```\\n\\n**Information Disclosed:**\\n\\n**1. Complete Schema Structure:**\\n```graphql\\n# Revealed sensitive fields\\ntype User {\\n  id: ID!\\n  email: String!\\n  password: String!     # Sensitive!\\n  ssn: String!          # PII!\\n  creditCard: String!   # Financial data!\\n  adminNotes: String!   # Internal data!\\n}\\n```\\n\\n**2. Hidden Operations:**\\n```graphql\\n# Internal mutations exposed\\ntype Mutation {\\n  deleteAllUsers: Boolean!\\n  promoteToAdmin(userId: ID!): User!\\n  bypassPayment(orderId: ID!): Order!\\n  debugMode(enabled: Boolean!): String!\\n}\\n```\\n\\n**3. Business Logic Insights:**\\n```graphql\\n# Reveals business relationships\\ntype Order {\\n  internalCost: Float!      # Profit margins\\n  supplierInfo: Supplier!   # Business relationships\\n  fraudScore: Float!        # Risk algorithms\\n}\\n```\\n\\n**Real-World Attack Scenarios:**\\n\\n**1. Privilege Escalation:**\\n```graphql\\n# Discovered through introspection\\nmutation {\\n  promoteToAdmin(userId: \\\"victim_user_id\\\") {\\n    id\\n    role\\n  }\\n}\\n```\\n\\n**2. Data Exfiltration:**\\n```graphql\\n# Target sensitive fields found via introspection\\nquery {\\n  users {\\n    email\\n    ssn\\n    creditCard\\n    adminNotes\\n  }\\n}\\n```\\n\\n**3. Business Intelligence Theft:**\\n```graphql\\n# Extract competitive information\\nquery {\\n  products {\\n    internalCost\\n    profitMargin\\n    supplierInfo {\\n      name\\n      pricing\\n    }\\n  }\\n}\\n```\\n\\n**Detection Methods:**\\n```bash\\n# Test for introspection\\ncurl -X POST https://api.target.com/graphql \\\\\\n  -H \\\"Content-Type: application/json\\\" \\\\\\n  -d '{\\\"query\\\":\\\"query { __schema { types { name } } }\\\"}'\\n\\n# Automated introspection tools\\ngraphql-voyager --introspect https://api.target.com/graphql\\ninql --target https://api.target.com/graphql\\n```\\n\\n**Mitigation Strategies:**\\n\\n**1. Disable Introspection in Production:**\\n```javascript\\n// Apollo Server\\nconst server = new ApolloServer({\\n  typeDefs,\\n  resolvers,\\n  introspection: false,  // Disable in production\\n  playground: false      // Disable GraphQL Playground\\n});\\n\\n// GraphQL Yoga\\nconst server = new GraphQLServer({\\n  typeDefs,\\n  resolvers,\\n  introspection: process.env.NODE_ENV !== 'production'\\n});\\n```\\n\\n**2. Schema Filtering:**\\n```javascript\\n// Remove sensitive fields from public schema\\nconst publicSchema = removeDirectives(\\n  transformSchema(privateSchema, [\\n    new FilterRootFields((operation, fieldName) => {\\n      return !fieldName.startsWith('admin');\\n    })\\n  ])\\n);\\n```\\n\\n**3. Query Depth Limiting:**\\n```javascript\\n// Prevent complex nested queries\\nconst depthLimit = require('graphql-depth-limit');\\n\\nconst server = new ApolloServer({\\n  typeDefs,\\n  resolvers,\\n  validationRules: [depthLimit(5)]\\n});\\n```\\n\\n**4. Rate Limiting and Monitoring:**\\n```javascript\\n// Implement query complexity analysis\\nconst costAnalysis = require('graphql-cost-analysis');\\n\\nconst server = new ApolloServer({\\n  typeDefs,\\n  resolvers,\\n  plugins: [\\n    costAnalysis({\\n      maximumCost: 1000,\\n      onComplete: (cost) => {\\n        console.log(`Query cost: ${cost}`);\\n      }\\n    })\\n  ]\\n});\\n```"}]}}