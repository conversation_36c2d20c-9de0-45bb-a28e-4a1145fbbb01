{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "5g-network-security-2024", "title": "5G Network Security & Emerging Protocols", "description": "Advanced 5G security covering network slicing vulnerabilities, edge computing threats, and real-world 5G attack scenarios with practical defense strategies.", "author": "QuizFlow Security Team", "creation_date": "2024-01-27T10:00:00Z", "tags": ["5g-security", "network-protocols", "edge-computing", "network-slicing", "practical-scenarios"], "passing_score_percentage": 80, "time_limit_minutes": 35, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "5g_network_slicing_attack_2024", "type": "multiple_choice", "text": "During a 5G security assessment, you discover that a malicious application is consuming excessive bandwidth and affecting other network slices. The attacker has gained access to a network slice intended for IoT devices and is using it to launch DDoS attacks. What 5G security principle has been violated?", "points": 4, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Network slice isolation failure", "is_correct": true, "feedback": "Correct! Network slicing should provide isolation between different service types and tenants."}, {"id": "opt2", "text": "Authentication bypass", "is_correct": false, "feedback": "While authentication may be involved, the core issue is slice isolation."}, {"id": "opt3", "text": "Encryption weakness", "is_correct": false, "feedback": "This scenario describes isolation failure, not encryption issues."}, {"id": "opt4", "text": "Radio access network vulnerability", "is_correct": false, "feedback": "The issue is in network slicing logic, not RAN components."}], "hint": [{"text": "Consider what should prevent one network slice from affecting others.", "delay_seconds": 30}, {"text": "Think about the fundamental security principle that network slicing should provide.", "delay_seconds": 60}], "feedback_correct": "Excellent! Network slice isolation is critical for 5G security.", "feedback_incorrect": "This scenario demonstrates network slice isolation failure - slices should be completely separated.", "explanation": "**5G Network Slicing Security Analysis:**\\n\\n**Attack Scenario:**\\nAn attacker compromised an IoT device slice and used it to launch attacks affecting other network slices, violating the fundamental isolation principle.\\n\\n**Network Slicing Fundamentals:**\\n\\n**1. Slice Isolation Requirements:**\\n- **Resource Isolation**: CPU, memory, bandwidth separation\\n- **Traffic Isolation**: No cross-slice communication\\n- **Security Isolation**: Independent security policies\\n- **Management Isolation**: Separate control planes\\n\\n**2. 5G Slice Architecture:**\\n```\\nNetwork Slice Types:\\n- eMBB (Enhanced Mobile Broadband)\\n- URLLC (Ultra-Reliable Low Latency)\\n- mMTC (Massive Machine Type Communications)\\n\\nEach slice should be completely isolated\\n```\\n\\n**3. Real-World Attack Examples:**\\n\\n**Slice Hopping Attack:**\\n- **Method**: Exploit slice management APIs\\n- **Impact**: Access unauthorized network resources\\n- **Mitigation**: Strong API authentication and authorization\\n\\n**Resource Exhaustion:**\\n- **Method**: Consume excessive slice resources\\n- **Impact**: Denial of service to other slices\\n- **Mitigation**: Resource quotas and monitoring\\n\\n**4. Security Implementation:**\\n\\n**Network Function Virtualization (NFV):**\\n```yaml\\n# Secure slice configuration\\nnetwork_slice:\\n  slice_id: iot_slice_001\\n  isolation_level: strict\\n  resource_limits:\\n    bandwidth: 100Mbps\\n    cpu: 2_cores\\n    memory: 4GB\\n  security_policies:\\n    - no_cross_slice_access\\n    - encrypted_traffic_only\\n    - device_authentication_required\\n```\\n\\n**5. Detection and Monitoring:**\\n\\n**Slice Monitoring:**\\n```bash\\n# Monitor slice resource usage\\nkubectl get pods -n slice-iot --show-labels\\nkubectl top pods -n slice-iot\\n\\n# Check for cross-slice traffic\\ntcpdump -i any 'not (src net 10.1.0.0/24 and dst net 10.1.0.0/24)'\\n```\\n\\n**6. Best Practices:**\\n- **Zero Trust Architecture**: Verify every slice interaction\\n- **Micro-segmentation**: Granular network controls\\n- **Continuous Monitoring**: Real-time slice behavior analysis\\n- **Incident Response**: Automated slice isolation capabilities"}, {"question_id": "5g_edge_computing_threat_2024", "type": "short_answer", "text": "A 5G edge computing node has been compromised, and the attacker is now intercepting traffic from mobile devices before it reaches the core network. What specific 5G security mechanism should have prevented this attack?", "points": 3, "difficulty": "intermediate", "correct_answers": ["mutual authentication", "device-to-edge authentication", "edge node authentication", "certificate-based authentication", "PKI authentication", "5G-AKA authentication"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Consider how mobile devices should verify the legitimacy of edge computing nodes.", "delay_seconds": 30}, {"text": "Think about the authentication mechanisms that should work in both directions.", "delay_seconds": 60}], "feedback_correct": "Correct! Mutual authentication ensures both device and edge node verify each other's identity.", "feedback_incorrect": "The answer is mutual authentication - devices should authenticate edge nodes before sending traffic.", "explanation": "**5G Edge Computing Security Analysis:**\\n\\n**Attack Scenario:**\\nA compromised edge node is performing man-in-the-middle attacks because devices failed to properly authenticate the edge infrastructure.\\n\\n**5G Authentication Architecture:**\\n\\n**1. 5G-AKA (Authentication and Key Agreement):**\\n```\\nDevice ←→ gNodeB ←→ AMF ←→ AUSF ←→ UDM\\n\\nSteps:\\n1. <PERSON><PERSON> sends authentication request\\n2. Network challenges device with RAND\\n3. <PERSON><PERSON> responds with SRES\\n4. Mutual authentication established\\n5. Session keys derived\\n```\\n\\n**2. Edge Node Authentication:**\\n\\n**Certificate-Based Verification:**\\n```bash\\n# Edge node certificate validation\\nopenssl verify -CAfile 5g_ca.pem edge_node.pem\\n\\n# Check certificate subject\\nopenssl x509 -in edge_node.pem -subject -noout\\nsubject= /C=US/O=5G-Operator/CN=edge-node-001.5g.net\\n```\\n\\n**3. Mutual Authentication Process:**\\n\\n**Device-to-Edge Verification:**\\n```\\n1. <PERSON>ce connects to edge node\\n2. Edge node presents certificate\\n3. Device validates certificate chain\\n4. Device presents its credentials\\n5. Edge node validates device identity\\n6. Secure channel established\\n```\\n\\n**4. Real-World Threats:**\\n\\n**Rogue Edge Nodes:**\\n- **Attack**: Fake edge infrastructure\\n- **Method**: DNS spoofing, BGP hijacking\\n- **Impact**: Traffic interception, data theft\\n\\n**Edge Node Compromise:**\\n- **Attack**: Legitimate edge node takeover\\n- **Method**: Software vulnerabilities, insider threats\\n- **Impact**: Persistent traffic monitoring\\n\\n**5. Security Implementation:**\\n\\n**PKI Infrastructure:**\\n```yaml\\n# 5G PKI configuration\\npki_config:\\n  root_ca: 5g_operator_root\\n  intermediate_ca: edge_infrastructure\\n  certificates:\\n    - type: edge_node\\n      validity: 1_year\\n      key_usage: [digital_signature, key_encipherment]\\n    - type: device\\n      validity: 5_years\\n      key_usage: [digital_signature]\\n```\\n\\n**6. Detection Methods:**\\n\\n**Certificate Monitoring:**\\n```python\\n# Monitor for certificate anomalies\\nimport ssl\\nimport socket\\n\\ndef verify_edge_certificate(hostname, port):\\n    context = ssl.create_default_context()\\n    with socket.create_connection((hostname, port)) as sock:\\n        with context.wrap_socket(sock, server_hostname=hostname) as ssock:\\n            cert = ssock.getpeercert()\\n            # Verify certificate fields\\n            if cert['subject'][0][0][1] != expected_cn:\\n                raise SecurityError('Invalid certificate')\\n```\\n\\n**7. Best Practices:**\\n- **Certificate Pinning**: Pin expected edge node certificates\\n- **Regular Rotation**: Frequent certificate updates\\n- **Anomaly Detection**: Monitor authentication patterns\\n- **Zero Trust**: Never trust, always verify edge nodes"}]}}