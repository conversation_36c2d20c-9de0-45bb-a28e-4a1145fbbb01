{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "kubernetes-security-hardening", "title": "Kubernetes Security Hardening & Best Practices", "description": "Advanced Kubernetes security hardening techniques, real-world misconfigurations, and comprehensive defense strategies for container orchestration environments.", "author": "QuizFlow Security Team", "creation_date": "2024-01-26T04:00:00Z", "tags": ["kubernetes", "container-security", "k8s", "orchestration", "hardening"], "passing_score_percentage": 85, "time_limit_minutes": 45, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "k8s_rbac_privilege_escalation_2021", "type": "multiple_choice", "text": "In 2021, security researchers demonstrated a privilege escalation attack in Kubernetes clusters with misconfigured RBAC. What was the **most common misconfiguration** that allowed attackers to gain cluster-admin privileges?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Overly permissive ClusterRoleBinding with wildcard permissions", "is_correct": true, "feedback": "Correct! Wildcard permissions in ClusterRoleBindings often grant excessive privileges leading to escalation."}, {"id": "opt2", "text": "Unencrypted etcd database with default credentials", "is_correct": false, "feedback": "While etcd security is important, this specific escalation involved RBAC misconfigurations."}, {"id": "opt3", "text": "Insecure kubelet API without authentication", "is_correct": false, "feedback": "Kubelet security is crucial but this attack focused on RBAC privilege escalation."}, {"id": "opt4", "text": "Container escape through privileged pod execution", "is_correct": false, "feedback": "Container escapes are different from RBAC-based privilege escalation within Kubernetes."}], "hint": [{"text": "Consider what happens when RBAC rules are too broad or use wildcards inappropriately.", "delay_seconds": 30}, {"text": "Think about how ClusterRoleBindings can grant cluster-wide permissions.", "delay_seconds": 60}], "feedback_correct": "Excellent! Understanding RBAC misconfigurations is critical for Kubernetes security.", "feedback_incorrect": "The attack exploited overly permissive ClusterRoleBindings with wildcard permissions.", "explanation": "**Kubernetes RBAC Privilege Escalation:**\\n\\n**Common RBAC Misconfigurations:**\\n\\n**1. Overly Broad ClusterRoleBinding:**\\n```yaml\\napiVersion: rbac.authorization.k8s.io/v1\\nkind: ClusterRoleBinding\\nmetadata:\\n  name: dangerous-binding\\nsubjects:\\n- kind: User\\n  name: developer\\n  apiGroup: rbac.authorization.k8s.io\\nroleRef:\\n  kind: ClusterRole\\n  name: cluster-admin  # TOO BROAD!\\n  apiGroup: rbac.authorization.k8s.io\\n```\\n\\n**2. Wildcard Permissions:**\\n```yaml\\napiVersion: rbac.authorization.k8s.io/v1\\nkind: ClusterRole\\nmetadata:\\n  name: wildcard-role\\nrules:\\n- apiGroups: [\\\"*\\\"]  # ALL API GROUPS\\n  resources: [\\\"*\\\"]   # ALL RESOURCES\\n  verbs: [\\\"*\\\"]       # ALL ACTIONS\\n```\\n\\n**Privilege Escalation Attack Chain:**\\n\\n**Step 1: Initial Access**\\n```bash\\n# Attacker gains access to pod with service account\\nkubectl auth can-i --list --as=system:serviceaccount:default:my-sa\\n```\\n\\n**Step 2: Enumerate Permissions**\\n```bash\\n# Check current permissions\\nkubectl auth can-i create clusterrolebindings\\nkubectl auth can-i create roles\\nkubectl auth can-i create rolebindings\\n```\\n\\n**Step 3: Escalate Privileges**\\n```yaml\\n# Create malicious ClusterRoleBinding\\napiVersion: rbac.authorization.k8s.io/v1\\nkind: ClusterRoleBinding\\nmetadata:\\n  name: escalation\\nsubjects:\\n- kind: ServiceAccount\\n  name: my-sa\\n  namespace: default\\nroleRef:\\n  kind: ClusterRole\\n  name: cluster-admin\\n  apiGroup: rbac.authorization.k8s.io\\n```\\n\\n**Step 4: Gain Cluster Admin**\\n```bash\\n# Now has cluster-admin privileges\\nkubectl get secrets --all-namespaces\\nkubectl get nodes\\nkubectl create namespace malicious\\n```\\n\\n**Real-World Impact:**\\n- **Data Exfiltration**: Access all secrets and configmaps\\n- **Lateral Movement**: Deploy malicious workloads\\n- **Persistence**: Create backdoor accounts\\n- **Denial of Service**: Delete critical resources\\n\\n**Secure RBAC Configuration:**\\n\\n**1. Principle of Least Privilege:**\\n```yaml\\napiVersion: rbac.authorization.k8s.io/v1\\nkind: Role\\nmetadata:\\n  namespace: app-namespace\\n  name: pod-reader\\nrules:\\n- apiGroups: [\\\"\\\"]\\n  resources: [\\\"pods\\\"]\\n  verbs: [\\\"get\\\", \\\"list\\\"]  # Only what's needed\\n```\\n\\n**2. Namespace Isolation:**\\n```yaml\\napiVersion: rbac.authorization.k8s.io/v1\\nkind: RoleBinding\\nmetadata:\\n  name: read-pods\\n  namespace: app-namespace  # Scoped to namespace\\nsubjects:\\n- kind: ServiceAccount\\n  name: pod-reader\\n  namespace: app-namespace\\nroleRef:\\n  kind: Role\\n  name: pod-reader\\n  apiGroup: rbac.authorization.k8s.io\\n```\\n\\n**3. Regular RBAC Auditing:**\\n```bash\\n# Audit cluster-admin bindings\\nkubectl get clusterrolebindings -o json | \\\\\\n  jq '.items[] | select(.roleRef.name==\\\"cluster-admin\\\") | .metadata.name'\\n\\n# Check for wildcard permissions\\nkubectl get clusterroles -o json | \\\\\\n  jq '.items[] | select(.rules[]?.resources[]? == \\\"*\\\")'\\n```\\n\\n**Prevention Best Practices:**\\n1. **Regular RBAC Reviews**: Audit permissions quarterly\\n2. **Automated Scanning**: Use tools like kube-score, Polaris\\n3. **Network Policies**: Implement microsegmentation\\n4. **Pod Security Standards**: Enforce security contexts\\n5. **Admission Controllers**: Validate resource creation"}]}}