{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "ransomware-incident-response-2024", "title": "Ransomware Incident Response & Recovery", "description": "Real-world ransomware incidents, attack patterns, and practical incident response strategies based on recent attacks like Colonial Pipeline, Kaseya, and JBS.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T13:00:00Z", "tags": ["ransomware", "incident-response", "recovery", "colonial-pipeline", "kaseya", "conti", "lockbit"], "passing_score_percentage": 80, "time_limit_minutes": 40, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "colonial_pipeline_response", "type": "multiple_choice", "text": "During the Colonial Pipeline ransomware attack (2021), the company shut down operations for 6 days. What was the **most critical first step** they should have taken according to incident response best practices?", "points": 2, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Pay the ransom immediately to restore operations", "is_correct": false, "feedback": "Paying ransom should never be the first step and doesn't guarantee recovery."}, {"id": "opt2", "text": "Isolate affected systems and activate incident response team", "is_correct": true, "feedback": "Correct! Containment and team activation are critical first steps in any ransomware incident."}, {"id": "opt3", "text": "<PERSON>ore from backups immediately", "is_correct": false, "feedback": "Backups should be verified clean before restoration, and containment comes first."}, {"id": "opt4", "text": "Contact law enforcement and media", "is_correct": false, "feedback": "While important, containment and assessment must happen first."}], "hint": [{"text": "Think about the NIST incident response framework - what's the first priority after detection?", "delay_seconds": 30}, {"text": "The goal is to prevent further spread while assembling the response team.", "delay_seconds": 60}], "feedback_correct": "Excellent! Containment and team activation are the foundation of effective incident response.", "feedback_incorrect": "The first priority is always containment - isolate systems and activate the incident response team.", "explanation": "**Ransomware Incident Response - Colonial Pipeline Case Study:**\\n\\n**Attack Overview:**\\n- **Date**: May 7, 2021\\n- **Attacker**: DarkSide ransomware group\\n- **Impact**: 5,500-mile pipeline shut down for 6 days\\n- **Ransom**: $4.4 million paid (partially recovered by FBI)\\n\\n**NIST Incident Response Framework:**\\n1. **Preparation**: IR plan, team, tools ready\\n2. **Detection & Analysis**: Identify the incident\\n3. **Containment, Eradication & Recovery**: Stop spread, remove threat, restore\\n4. **Post-Incident Activity**: Lessons learned, improvements\\n\\n**Critical First Steps (Containment):**\\n```bash\\n# Network isolation\\n- Disconnect affected systems from network\\n- Block lateral movement paths\\n- Preserve evidence for forensics\\n\\n# Team activation\\n- Notify incident response team\\n- Establish command center\\n- Begin documentation\\n```\\n\\n**Colonial Pipeline Response Analysis:**\\n- **Good**: Quick shutdown prevented further spread\\n- **Questionable**: Ransom payment decision\\n- **Lesson**: Better network segmentation could have limited impact\\n\\n**Modern Ransomware Tactics:**\\n- **Double Extortion**: Encrypt + threaten to leak data\\n- **Supply Chain**: Target MSPs and software vendors\\n- **Living off the Land**: Use legitimate tools\\n- **Persistence**: Multiple backdoors and access methods\\n\\n**Prevention & Response Best Practices:**\\n1. **Backup Strategy**: 3-2-1 rule with offline/immutable backups\\n2. **Network Segmentation**: Limit lateral movement\\n3. **Endpoint Detection**: Advanced EDR solutions\\n4. **Incident Response Plan**: Tested and updated regularly\\n5. **Threat Intelligence**: Monitor for indicators", "single_correct_answer": true}, {"question_id": "ransomware_forensics", "type": "short_answer", "text": "During ransomware forensics, you need to identify the initial infection vector. What Windows Event Log would you examine first to find evidence of **lateral movement** using stolen credentials?", "points": 2, "difficulty": "intermediate", "correct_answers": ["Security Event Log", "Security.evtx", "Event ID 4624", "Event ID 4625", "Windows Security Log"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Think about which Windows log records authentication events and logon attempts.", "delay_seconds": 30}, {"text": "The Security Event Log contains logon events (4624) and failed logons (4625) that show lateral movement.", "delay_seconds": 60}], "feedback_correct": "Correct! The Security Event Log contains crucial authentication events for tracking lateral movement.", "feedback_incorrect": "The Security Event Log (Security.evtx) contains logon events that reveal lateral movement patterns.", "explanation": "**Ransomware Forensics - Lateral Movement Detection:**\\n\\n**Key Windows Event IDs for Lateral Movement:**\\n- **4624**: Successful logon (Type 3 = Network, Type 10 = RDP)\\n- **4625**: Failed logon attempts (brute force indicators)\\n- **4648**: Logon using explicit credentials\\n- **4672**: Special privileges assigned to new logon\\n- **4768/4769**: Kerberos authentication (Golden/Silver ticket detection)\\n\\n**Forensic Analysis Commands:**\\n```powershell\\n# Extract logon events\\nGet-WinEvent -FilterHashtable @{LogName='Security'; ID=4624} | Where-Object {$_.Properties[8].Value -eq 3}\\n\\n# Find RDP logons\\nGet-WinEvent -FilterHashtable @{LogName='Security'; ID=4624} | Where-Object {$_.Properties[8].Value -eq 10}\\n\\n# Check for privilege escalation\\nGet-WinEvent -FilterHashtable @{LogName='Security'; ID=4672}\\n```\\n\\n**Lateral Movement Indicators:**\\n1. **Unusual Logon Patterns**: Off-hours, multiple systems\\n2. **Service Account Abuse**: Accounts logging on interactively\\n3. **Administrative Shares**: Access to C$, ADMIN$, IPC$\\n4. **Remote Execution**: PsExec, WMI, PowerShell remoting\\n5. **Credential Dumping**: LSASS access, registry hive access\\n\\n**Common Ransomware Lateral Movement:**\\n- **Credential Harvesting**: Mimikatz, LaZagne, browser passwords\\n- **Pass-the-Hash**: NTLM hash reuse\\n- **Golden Tickets**: Kerberos ticket forgery\\n- **Remote Services**: RDP, WinRM, SMB\\n- **Scheduled Tasks**: Remote task creation\\n\\n**Timeline Reconstruction:**\\n1. **Initial Compromise**: Phishing, RDP brute force, vulnerability\\n2. **Credential Access**: Dump LSASS, registry, browsers\\n3. **Discovery**: Network scanning, AD enumeration\\n4. **Lateral Movement**: Spread to high-value targets\\n5. **Persistence**: Multiple backdoors, scheduled tasks\\n6. **Encryption**: Deploy ransomware payload"}]}}