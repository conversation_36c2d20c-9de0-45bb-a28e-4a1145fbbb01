{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "wireless-security-comprehensive-2024", "title": "Wireless Security - Comprehensive Testing", "description": "Advanced wireless security testing covering WiFi exploitation, Bluetooth attacks, and real-world wireless penetration testing scenarios.", "author": "QuizFlow Security Team", "creation_date": "2024-01-27T10:00:00Z", "tags": ["wireless-security", "wifi-hacking", "bluetooth", "penetration-testing"], "passing_score_percentage": 80, "time_limit_minutes": 30, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "wpa2_krack_attack_2024", "type": "multiple_choice", "text": "During a wireless penetration test, you discover a WPA2 network vulnerable to the KRACK (Key Reinstallation Attack). What specific vulnerability does this exploit target?", "points": 4, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "4-way handshake key reinstallation", "is_correct": true, "feedback": "Correct! KRACK exploits key reinstallation in the WPA2 4-way handshake process."}, {"id": "opt2", "text": "WPS PIN brute force", "is_correct": false, "feedback": "WPS PIN attacks are different from KRACK, which targets the 4-way handshake."}, {"id": "opt3", "text": "Weak PSK password", "is_correct": false, "feedback": "KRACK doesn't target weak passwords but rather the handshake protocol itself."}, {"id": "opt4", "text": "Rogue access point", "is_correct": false, "feedback": "KRACK is a protocol vulnerability, not a rogue AP attack."}], "hint": [{"text": "KRACK stands for Key Reinstallation Attack - think about what gets reinstalled.", "delay_seconds": 30}, {"text": "Consider the WPA2 authentication process and where keys are exchanged.", "delay_seconds": 60}], "feedback_correct": "Excellent! KRACK exploits the WPA2 4-way handshake vulnerability.", "feedback_incorrect": "KRACK targets key reinstallation in the WPA2 4-way handshake process.", "explanation": "**KRACK Attack Analysis:**\\n\\n**Vulnerability Overview:**\\nKRACK (Key Reinstallation Attack) exploits a fundamental flaw in the WPA2 4-way handshake that allows attackers to decrypt and manipulate wireless traffic.\\n\\n**Attack Mechanism:**\\n1. **Handshake Manipulation**: Attacker forces key reinstallation\\n2. **Nonce Reuse**: Encryption nonce is reset to zero\\n3. **Traffic Decryption**: Previously secure traffic becomes readable\\n4. **Packet Injection**: Malicious packets can be injected\\n\\n**Technical Details:**\\n```\\nNormal 4-way Handshake:\\nAP → Client: ANonce (Message 1)\\nClient → AP: SNonce + MIC (Message 2)\\nAP → Client: GTK + MIC (Message 3)\\nClient → AP: ACK (Message 4)\\n\\nKRACK Attack:\\n1. Block Message 4 from reaching AP\\n2. AP retransmits Message 3\\n3. Client reinstalls same key\\n4. <PERSON><PERSON> counter resets to zero\\n5. Keystream reuse enables decryption\\n```\\n\\n**Real-World Impact:**\\n- **Android/Linux**: Severe - all traffic decryptable\\n- **Windows/iOS**: Limited - only some packets affected\\n- **IoT Devices**: Critical - often unpatched\\n\\n**Detection Methods:**\\n```bash\\n# Using aircrack-ng suite\\nairodump-ng wlan0mon\\naireplay-ng --deauth 10 -a [BSSID] wlan0mon\\n\\n# KRACK detection script\\npython krack-test-client.py\\n```\\n\\n**Mitigation:**\\n1. **Update firmware/drivers** to patched versions\\n2. **Use VPN** for additional encryption layer\\n3. **Monitor for unusual traffic** patterns\\n4. **Implement network segmentation**"}]}}