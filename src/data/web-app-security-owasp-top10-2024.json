{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "web-app-security-owasp-top10-2024", "title": "OWASP Top 10 2023 - Real-World Web Application Security", "description": "Comprehensive analysis of OWASP Top 10 2023 vulnerabilities with real-world exploitation scenarios, code examples, and practical defense strategies.", "author": "QuizFlow Security Team", "creation_date": "2024-01-27T10:00:00Z", "tags": ["owasp-top-10", "web-security", "application-security", "practical-scenarios", "real-world"], "passing_score_percentage": 80, "time_limit_minutes": 40, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "broken_access_control_2023", "type": "multiple_choice", "text": "You're testing a web application and discover this URL: `https://bank.com/account?user_id=12345`. When you change `user_id=67890`, you can access another user's account information. According to OWASP Top 10 2023, what vulnerability is this?", "points": 3, "difficulty": "intermediate", "single_correct_answer": true, "options": [{"id": "opt1", "text": "A01:2023 – Broken Access Control", "is_correct": true, "feedback": "Correct! This is a classic Insecure Direct Object Reference (IDOR), part of Broken Access Control."}, {"id": "opt2", "text": "A03:2023 – Injection", "is_correct": false, "feedback": "This isn't injection - no malicious code is being injected into the application."}, {"id": "opt3", "text": "A07:2023 – Identification and Authentication Failures", "is_correct": false, "feedback": "Authentication isn't the issue - the user is authenticated but accessing unauthorized data."}, {"id": "opt4", "text": "A05:2023 – Security Misconfiguration", "is_correct": false, "feedback": "This is an application logic flaw, not a configuration issue."}], "hint": [{"text": "Think about what happens when users can access resources they shouldn't be able to.", "delay_seconds": 30}, {"text": "This vulnerability involves bypassing authorization checks through parameter manipulation.", "delay_seconds": 60}], "feedback_correct": "Excellent! Broken Access Control is #1 in OWASP Top 10 2023.", "feedback_incorrect": "This is Broken Access Control - specifically an Insecure Direct Object Reference (IDOR).", "explanation": "**A01:2023 – Broken Access Control Analysis:**\\n\\n**Vulnerability Type:** Insecure Direct Object Reference (IDOR)\\n\\n**Attack Scenario:**\\n1. **Discovery**: Attacker notices user_id parameter in URL\\n2. **Enumeration**: Tests different user_id values\\n3. **Exploitation**: Accesses other users' account data\\n4. **Impact**: Unauthorized data access, privacy breach\\n\\n**Real-World Examples:**\\n- **Facebook (2018)**: Photo API exposed 6.8M users' photos\\n- **Venmo (2019)**: Transaction data accessible via user IDs\\n- **Instagram (2017)**: Private account info via API manipulation\\n\\n**Technical Details:**\\n```javascript\\n// Vulnerable code\\napp.get('/account', (req, res) => {\\n  const userId = req.query.user_id;\\n  // No authorization check!\\n  const account = db.getAccount(userId);\\n  res.json(account);\\n});\\n\\n// Secure implementation\\napp.get('/account', authenticateUser, (req, res) => {\\n  const userId = req.query.user_id;\\n  const currentUser = req.user.id;\\n  \\n  // Authorization check\\n  if (userId !== currentUser && !req.user.isAdmin) {\\n    return res.status(403).json({error: 'Forbidden'});\\n  }\\n  \\n  const account = db.getAccount(userId);\\n  res.json(account);\\n});\\n```\\n\\n**Prevention Strategies:**\\n1. **Implement proper authorization checks**\\n2. **Use indirect references (UUIDs instead of sequential IDs)**\\n3. **Apply principle of least privilege**\\n4. **Implement role-based access control (RBAC)**\\n5. **Regular security testing and code reviews**"}, {"question_id": "cryptographic_failures_2023", "type": "short_answer", "text": "A developer stores user passwords using this PHP code: `$hashedPassword = md5($password . 'salt123');`. What are the TWO main cryptographic failures in this implementation according to OWASP Top 10 2023?", "points": 4, "difficulty": "advanced", "correct_answers": ["weak hashing algorithm", "static salt", "md5 is broken", "same salt for all users", "md5 cryptographically broken", "fixed salt reuse"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Consider both the hashing algorithm choice and the salt implementation.", "delay_seconds": 30}, {"text": "Think about MD5's security status and how salts should be properly implemented.", "delay_seconds": 60}], "feedback_correct": "Correct! MD5 is cryptographically broken and static salts defeat the purpose of salting.", "feedback_incorrect": "The main issues are: 1) MD5 is cryptographically broken, 2) Static salt allows rainbow table attacks.", "explanation": "**A02:2023 – Cryptographic Failures Analysis:**\\n\\n**Critical Issues Identified:**\\n\\n**1. Weak Hashing Algorithm (MD5):**\\n- **Status**: Cryptographically broken since 2004\\n- **Vulnerabilities**: Collision attacks, rainbow tables\\n- **Speed**: Too fast for password hashing (billions/second)\\n- **Recommendation**: Use bcrypt, scrypt, or Argon2\\n\\n**2. Static Salt Implementation:**\\n- **Problem**: Same salt ('salt123') for all passwords\\n- **Impact**: Enables rainbow table attacks\\n- **Correct Approach**: Unique random salt per password\\n\\n**Attack Scenarios:**\\n\\n**MD5 Rainbow Table Attack:**\\n```bash\\n# Attacker creates rainbow table for common passwords\\necho -n 'password123salt123' | md5sum\\n# Result: f25a2fc72690b780b2a14e140ef6a9e0\\n\\n# If multiple users have same password, same hash appears\\n# Attacker can crack all instances simultaneously\\n```\\n\\n**Secure Implementation:**\\n```php\\n// Secure password hashing\\n$hashedPassword = password_hash($password, PASSWORD_ARGON2ID, [\\n    'memory_cost' => 65536,  // 64 MB\\n    'time_cost' => 4,        // 4 iterations\\n    'threads' => 3           // 3 threads\\n]);\\n\\n// Verification\\nif (password_verify($password, $hashedPassword)) {\\n    // Password is correct\\n}\\n```\\n\\n**Real-World Breaches:**\\n- **LinkedIn (2012)**: 6.5M passwords, unsalted SHA-1\\n- **Adobe (2013)**: 153M passwords, weak encryption\\n- **Yahoo (2013-2014)**: 3B accounts, MD5 with weak salts\\n\\n**Best Practices:**\\n1. **Use modern algorithms**: Argon2, bcrypt, scrypt\\n2. **Unique random salts**: Generate per password\\n3. **Proper work factors**: Adjust for current hardware\\n4. **Regular updates**: Migrate to stronger algorithms\\n5. **Never roll your own crypto**: Use proven libraries"}]}}