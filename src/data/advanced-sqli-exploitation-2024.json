{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "advanced-sqli-exploitation-2024", "title": "Advanced SQL Injection Exploitation Techniques", "description": "Master advanced SQL injection techniques including blind SQLi, time-based attacks, second-order injection, and NoSQL injection with real-world scenarios.", "author": "QuizFlow Security Team", "creation_date": "2024-01-26T10:00:00Z", "tags": ["sql-injection", "blind-sqli", "nosql", "second-order", "exploitation"], "passing_score_percentage": 85, "time_limit_minutes": 45, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "second_order_sqli_detection", "type": "multiple_choice", "text": "You're testing a web application where user input is stored in a database and later used in a different SQL query without proper sanitization. What type of SQL injection vulnerability is this?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Second-order SQL injection", "is_correct": true, "feedback": "Correct! Second-order SQLi occurs when malicious input is stored and later used in a vulnerable query."}, {"id": "opt2", "text": "Blind SQL injection", "is_correct": false, "feedback": "Blind SQLi refers to injection without visible output, not the storage/retrieval pattern."}, {"id": "opt3", "text": "Union-based SQL injection", "is_correct": false, "feedback": "Union-based injection is a technique, not a classification based on data flow."}, {"id": "opt4", "text": "Time-based SQL injection", "is_correct": false, "feedback": "Time-based injection is about using delays for information extraction."}], "hint": [{"text": "Consider the flow of data: input → storage → later retrieval and use in query.", "delay_seconds": 30}, {"text": "This type of injection involves a two-step process with data persistence.", "delay_seconds": 60}], "feedback_correct": "Excellent! Second-order SQLi is often overlooked but very dangerous.", "feedback_incorrect": "Second-order SQLi involves storing malicious input that's later used unsafely.", "explanation": "**Second-Order SQL Injection Analysis:**\\n\\n**Attack Flow:**\\n1. **Input Phase**: Attacker submits malicious SQL payload\\n2. **Storage Phase**: Application stores payload in database\\n3. **Retrieval Phase**: Application retrieves and uses stored data in SQL query\\n4. **Execution Phase**: Malicious SQL executes in different context\\n\\n**Example Scenario:**\\n```sql\\n-- Step 1: User registration with malicious username\\nINSERT INTO users (username, email) VALUES ('admin\\'--', '<EMAIL>');\\n\\n-- Step 2: Later, admin panel retrieves username for logging\\nSELECT * FROM audit_log WHERE username = 'admin'--' AND action = 'login';\\n-- Comment (--) breaks the query, potentially bypassing restrictions\\n```\\n\\n**Real-World Example:**\\n```php\\n// Vulnerable code\\n// Registration (input stored)\\n$stmt = $pdo->prepare(\\\"INSERT INTO users (username) VALUES (?)\\\");\\n$stmt->execute([$_POST['username']]);\\n\\n// Later usage (vulnerable)\\n$username = getUsernameById($userId);\\n$query = \\\"SELECT * FROM posts WHERE author = '$username'\\\";\\n$result = mysqli_query($conn, $query); // VULNERABLE!\\n```\\n\\n**Detection Techniques:**\\n1. **Code Review**: Look for stored data used in dynamic queries\\n2. **Data Flow Analysis**: Trace user input through storage to usage\\n3. **Time-Delayed Testing**: Submit payloads and test later functionality\\n\\n**Prevention:**\\n- Use parameterized queries everywhere\\n- Validate data on retrieval, not just input\\n- Implement proper output encoding\\n- Regular security code reviews"}, {"question_id": "nosql_injection_mongodb", "type": "short_answer", "text": "In a MongoDB application, you discover that user input is directly inserted into a query. What NoSQL injection payload would you use to bypass authentication if the query is: `db.users.find({username: userInput, password: passInput})`?", "points": 2, "difficulty": "advanced", "correct_answers": ["{$ne: null}", "{$ne: \"\"}", "{$ne: 1}", "{$gt: \"\"}", "{$regex: \".*\"}"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Think about MongoDB operators that always evaluate to true.", "delay_seconds": 30}, {"text": "Consider operators like $ne (not equal), $gt (greater than), or $regex.", "delay_seconds": 60}], "feedback_correct": "Correct! MongoDB operators can be used to bypass authentication logic.", "feedback_incorrect": "Use MongoDB operators like {$ne: null} to make conditions always true.", "explanation": "**NoSQL Injection in MongoDB:**\\n\\n**Vulnerable Query:**\\n```javascript\\ndb.users.find({\\n  username: userInput,\\n  password: passInput\\n});\\n```\\n\\n**Attack Payloads:**\\n\\n**1. Not Equal ($ne):**\\n```javascript\\n// Input: username={$ne: null}&password={$ne: null}\\ndb.users.find({\\n  username: {$ne: null},\\n  password: {$ne: null}\\n});\\n// Returns all users with non-null username/password\\n```\\n\\n**2. Greater Than ($gt):**\\n```javascript\\n// Input: username={$gt: \\\"\\\"}&password={$gt: \\\"\\\"}\\ndb.users.find({\\n  username: {$gt: \\\"\\\"},\\n  password: {$gt: \\\"\\\"}\\n});\\n// Returns users with non-empty strings\\n```\\n\\n**3. Regular Expression ($regex):**\\n```javascript\\n// Input: username={$regex: \\\".*\\\"}&password={$regex: \\\".*\\\"}\\ndb.users.find({\\n  username: {$regex: \\\".*\\\"},\\n  password: {$regex: \\\".*\\\"}\\n});\\n// Matches any string\\n```\\n\\n**HTTP Request Example:**\\n```http\\nPOST /login HTTP/1.1\\nContent-Type: application/json\\n\\n{\\n  \\\"username\\\": {\\\"$ne\\\": null},\\n  \\\"password\\\": {\\\"$ne\\\": null}\\n}\\n```\\n\\n**Prevention:**\\n```javascript\\n// Secure implementation\\nconst { username, password } = req.body;\\n\\n// Validate input types\\nif (typeof username !== 'string' || typeof password !== 'string') {\\n  return res.status(400).json({error: 'Invalid input'});\\n}\\n\\n// Use parameterized queries\\nconst user = await db.users.findOne({\\n  username: username,\\n  password: await bcrypt.hash(password, 10)\\n});\\n```"}]}}