{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "owasp-top-10-2023-deep-dive", "title": "OWASP Top 10 2023 - Deep Dive Analysis", "description": "Comprehensive analysis of OWASP Top 10 2023 vulnerabilities with real-world examples, exploitation techniques, and practical remediation strategies.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T23:30:00Z", "tags": ["owasp", "web-security", "vulnerabilities", "exploitation", "remediation"], "passing_score_percentage": 85, "time_limit_minutes": 60, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "broken_access_control_a01_2023", "type": "multiple_choice", "text": "OWASP A01:2023 - Broken Access Control is the most critical web application security risk. In a recent bug bounty report, a researcher found they could access other users' private documents by modifying a URL parameter. What type of access control vulnerability is this?", "points": 3, "difficulty": "intermediate", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Insecure Direct Object Reference (IDOR)", "is_correct": true, "feedback": "Correct! IDOR allows attackers to access objects by modifying URL parameters or form fields."}, {"id": "opt2", "text": "Cross-Site Request Forgery (CSRF)", "is_correct": false, "feedback": "CSRF involves tricking users into performing unwanted actions, not direct object access."}, {"id": "opt3", "text": "SQL Injection", "is_correct": false, "feedback": "SQL injection involves database manipulation, not direct URL parameter modification."}, {"id": "opt4", "text": "Cross-Site Scripting (XSS)", "is_correct": false, "feedback": "XSS involves injecting malicious scripts, not accessing objects through URL manipulation."}], "hint": [{"text": "This vulnerability involves directly referencing internal objects through user input.", "delay_seconds": 30}, {"text": "The attacker can access unauthorized data by changing identifiers in the URL.", "delay_seconds": 60}], "feedback_correct": "Excellent! IDOR is a classic example of broken access control.", "feedback_incorrect": "This is an Insecure Direct Object Reference (IDOR) vulnerability, a type of broken access control.", "explanation": "**Insecure Direct Object Reference (IDOR) Analysis:**\\n\\n**Vulnerability Description:**\\nIDOR occurs when applications expose internal object references (like database keys, filenames, or directory paths) without proper access control checks.\\n\\n**Real-World Example:**\\n```\\n# Vulnerable URL\\nhttps://example.com/user/profile?id=1234\\n\\n# Attacker modifies the ID\\nhttps://example.com/user/profile?id=1235\\nhttps://example.com/user/profile?id=1236\\n```\\n\\n**Common IDOR Scenarios:**\\n1. **Database Record Access**: `/api/users/123` → `/api/users/124`\\n2. **File Access**: `/download?file=user123.pdf` → `/download?file=user124.pdf`\\n3. **API Endpoints**: `/api/orders/456` → `/api/orders/457`\\n4. **Account Functions**: `/account/settings?user=john` → `/account/settings?user=admin`\\n\\n**Testing for IDOR:**\\n```bash\\n# Burp Suite Intruder payload\\n# Original request\\nGET /api/documents/1001 HTTP/1.1\\nAuthorization: Bearer user_token\\n\\n# Test with different IDs\\n# 1000, 1002, 1003, 999, 1100, etc.\\n```\\n\\n**Impact Assessment:**\\n- **Data Exposure**: Access to sensitive user information\\n- **Privacy Violation**: Viewing other users' private data\\n- **Compliance Issues**: GDPR, HIPAA violations\\n- **Business Logic Bypass**: Accessing premium features\\n\\n**Prevention Strategies:**\\n\\n**1. Implement Proper Access Controls:**\\n```python\\n# Secure implementation\\ndef get_user_document(user_id, document_id):\\n    # Verify user owns the document\\n    document = Document.query.filter_by(\\n        id=document_id,\\n        owner_id=user_id\\n    ).first()\\n    \\n    if not document:\\n        raise Forbidden(\\\"Access denied\\\")\\n    \\n    return document\\n```\\n\\n**2. Use Indirect References:**\\n```python\\n# Instead of direct database IDs\\n# Use UUIDs or session-based references\\ndocument_uuid = str(uuid.uuid4())\\nsession['document_refs'][document_uuid] = actual_document_id\\n```\\n\\n**3. Implement Role-Based Access Control:**\\n```python\\n@require_permission('read_document')\\ndef view_document(document_id):\\n    # Check if user has permission\\n    if not current_user.can_access_document(document_id):\\n        abort(403)\\n    return render_document(document_id)\\n```\\n\\n**Detection and Testing:**\\n- Automated scanners (Burp Suite, OWASP ZAP)\\n- Manual parameter manipulation\\n- Privilege escalation testing\\n- Cross-user data access verification"}, {"question_id": "cryptographic_failures_a02_2023", "type": "short_answer", "text": "OWASP A02:2023 - Cryptographic Failures. A web application stores user passwords using MD5 hashing without salt. What is the **primary reason** why MD5 is considered cryptographically broken for password storage?", "points": 2, "difficulty": "intermediate", "correct_answers": ["collision attacks", "hash collisions", "collision vulnerability", "collision resistance broken", "rainbow table attacks", "precomputed hash attacks"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Consider what happens when two different inputs produce the same hash output.", "delay_seconds": 30}, {"text": "Think about attacks that use precomputed hash tables or exploit hash weaknesses.", "delay_seconds": 60}], "feedback_correct": "Correct! MD5 is vulnerable to collision attacks and rainbow table attacks.", "feedback_incorrect": "MD5 is broken due to collision vulnerabilities and susceptibility to rainbow table attacks.", "explanation": "**MD5 Cryptographic Failures Analysis:**\\n\\n**Why MD5 is Broken:**\\n\\n**1. Collision Attacks:**\\n- **Definition**: Two different inputs produce the same hash\\n- **Impact**: Attackers can create malicious content with same hash\\n- **Timeline**: Practical collisions demonstrated in 2004\\n\\n**2. Rainbow Table Attacks:**\\n- **Method**: Precomputed hash-to-plaintext lookup tables\\n- **Speed**: Instant password recovery for common passwords\\n- **Mitigation**: Salting (which this example lacks)\\n\\n**Technical Demonstration:**\\n```python\\n# MD5 collision example (simplified)\\nimport hashlib\\n\\n# These two different inputs produce same MD5 hash\\ninput1 = b'\\\\x4d\\\\xc9\\\\x68\\\\xff\\\\x0e\\\\xe3\\\\x5c\\\\x20\\\\x95\\\\x72\\\\xd4\\\\x77\\\\x7b\\\\x72\\\\x15\\\\x87'\\ninput2 = b'\\\\x4d\\\\xc9\\\\x68\\\\xff\\\\x0e\\\\xe3\\\\x5c\\\\x20\\\\x95\\\\x72\\\\xd4\\\\xf7\\\\x7b\\\\x72\\\\x15\\\\x87'\\n\\nprint(hashlib.md5(input1).hexdigest())\\nprint(hashlib.md5(input2).hexdigest())\\n# Both produce: 008ee33a9d58b51cfeb425b0959121c9\\n```\\n\\n**Password Storage Vulnerabilities:**\\n```python\\n# VULNERABLE: Plain MD5\\npassword_hash = hashlib.md5(password.encode()).hexdigest()\\n\\n# STILL VULNERABLE: MD5 with salt\\nsalt = os.urandom(16)\\npassword_hash = hashlib.md5(salt + password.encode()).hexdigest()\\n```\\n\\n**Secure Alternatives:**\\n\\n**1. bcrypt (Recommended):**\\n```python\\nimport bcrypt\\n\\n# Hash password\\npassword = b\\\"user_password\\\"\\nhashed = bcrypt.hashpw(password, bcrypt.gensalt())\\n\\n# Verify password\\nif bcrypt.checkpw(password, hashed):\\n    print(\\\"Password correct\\\")\\n```\\n\\n**2. Argon2 (Modern Standard):**\\n```python\\nfrom argon2 import PasswordHasher\\n\\nph = PasswordHasher()\\nhash = ph.hash(\\\"password\\\")\\n\\n# Verify\\ntry:\\n    ph.verify(hash, \\\"password\\\")\\n    print(\\\"Password correct\\\")\\nexcept:\\n    print(\\\"Password incorrect\\\")\\n```\\n\\n**3. PBKDF2 (Acceptable):**\\n```python\\nimport hashlib\\nimport os\\n\\nsalt = os.urandom(32)\\nkey = hashlib.pbkdf2_hmac('sha256', password.encode(), salt, 100000)\\n```\\n\\n**Key Security Principles:**\\n1. **Use Strong Algorithms**: bcrypt, Argon2, PBKDF2\\n2. **Always Salt**: Prevent rainbow table attacks\\n3. **Sufficient Iterations**: Slow down brute force attacks\\n4. **Regular Updates**: Migrate to stronger algorithms over time\\n\\n**OWASP Recommendations:**\\n- Avoid: MD5, SHA1, SHA2 for passwords\\n- Use: bcrypt, Argon2id, PBKDF2\\n- Implement: Proper salt generation\\n- Consider: Hardware security modules (HSMs)"}]}}