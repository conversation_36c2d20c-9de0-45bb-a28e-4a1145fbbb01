{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "wireless-security-exploitation", "title": "Wireless Security Exploitation & Defense", "description": "Advanced wireless security exploitation techniques including WPA/WPA2 attacks, rogue access points, and wireless penetration testing methodologies.", "author": "QuizFlow Security Team", "creation_date": "2024-01-26T01:00:00Z", "tags": ["wireless", "wifi", "wpa", "penetration-testing", "rogue-ap"], "passing_score_percentage": 80, "time_limit_minutes": 40, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "wpa2_krack_attack_2017", "type": "multiple_choice", "text": "The KRACK attack (2017) exploited a vulnerability in the WPA2 protocol's 4-way handshake. What was the **primary weakness** that allowed attackers to decrypt wireless traffic?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "<PERSON><PERSON> reuse in the 4-way handshake process", "is_correct": true, "feedback": "Correct! KRACK exploited nonce reuse, allowing attackers to decrypt and replay packets."}, {"id": "opt2", "text": "Weak password hashing in the PSK derivation", "is_correct": false, "feedback": "KRACK didn't target password hashing but rather the handshake protocol itself."}, {"id": "opt3", "text": "Buffer overflow in the WPA2 implementation", "is_correct": false, "feedback": "KRACK was a protocol-level attack, not an implementation vulnerability."}, {"id": "opt4", "text": "Weak encryption in the TKIP cipher suite", "is_correct": false, "feedback": "KRACK affected both TKIP and AES-CCMP, targeting the handshake process."}], "hint": [{"text": "This attack involved forcing the reuse of cryptographic values that should only be used once.", "delay_seconds": 30}, {"text": "Think about what 'nonce' stands for and why reusing it is dangerous in cryptography.", "delay_seconds": 60}], "feedback_correct": "Excellent! Understanding KRACK is crucial for wireless security assessment.", "feedback_incorrect": "KRACK exploited nonce reuse in the WPA2 4-way handshake, allowing traffic decryption.", "explanation": "**KRACK Attack Analysis (CVE-2017-13077):**\\n\\n**Vulnerability Overview:**\\n- **Target**: WPA2 4-way handshake protocol\\n- **Impact**: Decrypt wireless traffic, inject packets\\n- **Affected**: All WPA2 implementations\\n- **Discovery**: <PERSON><PERSON> (KU Leuven)\\n\\n**Technical Details:**\\n\\n**Normal 4-Way Handshake:**\\n1. **Message 1**: AP → Client (ANonce)\\n2. **Message 2**: Client → AP (SNonce, MIC)\\n3. **Message 3**: AP → Client (ANonce, GTK, MIC)\\n4. **Message 4**: Client → AP (MIC)\\n\\n**KRACK Exploitation:**\\n```bash\\n# Attack scenario\\n1. Attacker blocks Message 4 from reaching AP\\n2. AP retransmits Message 3 (same nonce)\\n3. Client reinstalls same PTK with nonce=0\\n4. Attacker can now decrypt/inject traffic\\n```\\n\\n**Key Reinstallation Process:**\\n```\\n# Normal: Nonce increments with each packet\\nPacket 1: Nonce = 1, Key = PTK\\nPacket 2: Nonce = 2, Key = PTK\\nPacket 3: Nonce = 3, Key = PTK\\n\\n# KRACK: Nonce resets to 0\\nPacket 1: Nonce = 1, Key = PTK\\nPacket 2: Nonce = 2, Key = PTK\\n[Key Reinstallation]\\nPacket 3: Nonce = 0, Key = PTK  # Vulnerable!\\n```\\n\\n**Attack Tools:**\\n```bash\\n# KRACK test script\\ngit clone https://github.com/vanhoefm/krackattacks-scripts.git\\ncd krackattacks-scripts\\n\\n# Test for KRACK vulnerability\\n./krack-test-client.py\\n\\n# Monitor for key reinstallation\\nairodump-ng wlan0mon --channel 6 --bssid AA:BB:CC:DD:EE:FF\\n```\\n\\n**Impact Assessment:**\\n- **Traffic Decryption**: Plaintext recovery of encrypted data\\n- **Packet Injection**: Malicious packet insertion\\n- **Session Hijacking**: TCP connection takeover\\n- **Credential Theft**: HTTP/HTTPS downgrade attacks\\n\\n**Mitigation Strategies:**\\n1. **Update Devices**: Install vendor patches\\n2. **Use VPN**: Additional encryption layer\\n3. **HTTPS Everywhere**: Encrypt application traffic\\n4. **Network Monitoring**: Detect unusual traffic patterns\\n\\n**Detection Methods:**\\n```bash\\n# Monitor for duplicate nonces\\ntshark -i wlan0 -f \\\"wlan type mgt subtype beacon\\\" \\\\\\n  -T fields -e wlan.seq -e frame.time\\n\\n# Check for key reinstallation indicators\\naircrack-ng -w wordlist.txt capture.cap\\n```"}]}}