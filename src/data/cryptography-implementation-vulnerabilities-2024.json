{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "cryptography-implementation-vulnerabilities-2024", "title": "Cryptography Implementation Vulnerabilities", "description": "Real-world cryptographic implementation flaws, side-channel attacks, and secure coding practices for cryptographic systems.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T19:00:00Z", "tags": ["cryptography", "implementation-flaws", "side-channel-attacks", "secure-coding", "timing-attacks"], "passing_score_percentage": 85, "time_limit_minutes": 50, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "timing_attack_vulnerability", "type": "multiple_choice", "text": "A web application uses this password verification code: `if (userPassword.equals(storedPassword)) { return true; }`. What cryptographic vulnerability does this create?", "points": 2, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Timing attack - comparison time varies based on how many characters match", "is_correct": true, "feedback": "Correct! String comparison stops at first mismatch, creating timing differences."}, {"id": "opt2", "text": "Rainbow table attack - passwords are not hashed", "is_correct": false, "feedback": "While hashing is important, the immediate issue is the timing vulnerability."}, {"id": "opt3", "text": "SQL injection - user input is not sanitized", "is_correct": false, "feedback": "This is a string comparison issue, not SQL injection."}, {"id": "opt4", "text": "Buffer overflow - string length is not validated", "is_correct": false, "feedback": "Java strings are bounds-checked, this isn't a buffer overflow."}], "hint": [{"text": "Think about how string comparison works and what information the execution time reveals.", "delay_seconds": 30}, {"text": "The comparison stops early when characters don't match, creating measurable timing differences.", "delay_seconds": 60}], "feedback_correct": "Excellent! Timing attacks exploit execution time differences to extract information.", "feedback_incorrect": "The vulnerability is timing-based - comparison time reveals information about password correctness.", "explanation": "**Timing Attack Vulnerabilities:**\\n\\n**How Timing Attacks Work:**\\n1. **Early Termination**: String comparison stops at first mismatch\\n2. **Timing Measurement**: Attacker measures response times\\n3. **Information Leakage**: Longer times indicate more matching characters\\n4. **Iterative Attack**: Build correct password character by character\\n\\n**Vulnerable Code Pattern:**\\n```java\\n// VULNERABLE - timing attack\\nif (userPassword.equals(storedPassword)) {\\n    return true;\\n}\\n\\n// SECURE - constant time comparison\\nreturn MessageDigest.isEqual(\\n    userPassword.getBytes(),\\n    storedPassword.getBytes()\\n);\\n```\\n\\n**Real-World Examples:**\\n- **RSA Timing Attacks**: Extract private keys from RSA implementations\\n- **AES Cache Attacks**: Exploit CPU cache timing differences\\n- **HMAC Verification**: Timing attacks on MAC verification\\n\\n**Mitigation Strategies:**\\n1. **Constant-Time Comparison**: Always compare full length\\n2. **Cryptographic Hashing**: Use bcrypt, scrypt, or Argon2\\n3. **Rate Limiting**: Prevent rapid timing measurements\\n4. **Random Delays**: Add noise to timing measurements\\n\\n**Secure Implementation:**\\n```java\\n// Secure password verification\\nString hashedInput = BCrypt.hashpw(userPassword, storedSalt);\\nreturn BCrypt.checkpw(userPassword, storedHashedPassword);\\n```", "single_correct_answer": true}]}}