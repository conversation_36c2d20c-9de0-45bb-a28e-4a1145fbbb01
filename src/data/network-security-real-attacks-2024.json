{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "network-security-real-attacks-2024", "title": "Network Security - Real Attack Analysis", "description": "Analysis of real network attacks, including SolarWinds, NotPetya, and advanced persistent threats with practical detection and prevention techniques.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T22:00:00Z", "tags": ["network-security", "apt", "malware", "real-attacks", "incident-response"], "passing_score_percentage": 85, "time_limit_minutes": 50, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "solarwinds_supply_chain_attack_2020", "type": "multiple_choice", "text": "The SolarWinds supply chain attack (2020) affected 18,000+ organizations. The attackers inserted malicious code into the Orion software updates. What was the **primary technique** used to maintain persistence after initial compromise?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Installing a rootkit in the Windows kernel", "is_correct": false, "feedback": "While sophisticated, the attack used application-level persistence methods."}, {"id": "opt2", "text": "Creating legitimate-looking Windows services and scheduled tasks", "is_correct": true, "feedback": "Correct! The SUNBURST malware created services that appeared legitimate to avoid detection."}, {"id": "opt3", "text": "Modifying the Windows registry to auto-start malicious processes", "is_correct": false, "feedback": "Registry modification was used but wasn't the primary persistence mechanism."}, {"id": "opt4", "text": "Replacing system DLLs with malicious versions", "is_correct": false, "feedback": "The attack was more subtle, avoiding direct system file replacement."}], "hint": [{"text": "The attackers wanted to blend in with normal system operations and avoid detection.", "delay_seconds": 30}, {"text": "Think about what Windows mechanisms allow programs to run automatically that would look normal to administrators.", "delay_seconds": 60}], "feedback_correct": "Excellent! Understanding APT persistence techniques is crucial for defense.", "feedback_incorrect": "The SolarWinds attackers used legitimate Windows services to maintain persistence while avoiding detection.", "explanation": "**SolarWinds Supply Chain Attack Analysis (2020):**\\n\\n**Attack Overview:**\\n- **Codename**: SUNBURST/SOLORIGATE\\n- **Victims**: 18,000+ organizations including US government agencies\\n- **Duration**: March 2020 - December 2020 (9 months undetected)\\n- **Attribution**: APT29 (Cozy Bear/SVR)\\n\\n**Attack Chain:**\\n1. **Supply Chain Compromise**: Inject malicious code into SolarWinds Orion build process\\n2. **Distribution**: Malicious updates distributed to customers via normal update mechanism\\n3. **Initial Execution**: SUNBURST backdoor activates after installation\\n4. **Persistence**: Create legitimate-looking Windows services\\n5. **Command & Control**: Communicate via DNS and HTTP to attacker infrastructure\\n6. **Lateral Movement**: Deploy additional tools like TEARDROP and RAINDROP\\n\\n**Persistence Techniques:**\\n```powershell\\n# Example service creation (simplified)\\nNew-Service -Name \\\"SolarWinds.BusinessLayerHost\\\" `\\n  -BinaryPathName \\\"C:\\\\Program Files\\\\SolarWinds\\\\Orion\\\\SolarWinds.BusinessLayerHost.exe\\\" `\\n  -DisplayName \\\"SolarWinds Business Layer Host\\\" `\\n  -StartupType Automatic\\n\\n# Scheduled task creation\\nRegister-ScheduledTask -TaskName \\\"SolarWinds Maintenance\\\" `\\n  -Action (New-ScheduledTaskAction -Execute \\\"powershell.exe\\\" -Argument \\\"-File C:\\\\temp\\\\update.ps1\\\") `\\n  -Trigger (New-ScheduledTaskTrigger -Daily -At 3AM)\\n```\\n\\n**Detection Evasion:**\\n- **Legitimate Names**: Services named to look like real SolarWinds components\\n- **Dormancy Period**: 12-14 day delay before activation\\n- **Domain Generation**: DGA for C2 communication\\n- **Code Signing**: Used legitimate SolarWinds certificates\\n\\n**Indicators of Compromise (IOCs):**\\n- DNS queries to avsvmcloud[.]com\\n- HTTP requests to freescanonline[.]com\\n- Registry keys: HKEY_LOCAL_MACHINE\\\\SOFTWARE\\\\SolarWinds\\\\Orion\\\\Core\\\\BusinessLayer\\n- File hashes: 32519b85c0b422e4656de6e6c41878e95fd95026267daab4215ee59c107d6c77\\n\\n**Lessons Learned:**\\n1. **Supply Chain Security**: Verify software integrity and build processes\\n2. **Network Monitoring**: Monitor DNS and HTTP traffic for anomalies\\n3. **Behavioral Analysis**: Look for unusual service creation and network activity\\n4. **Zero Trust**: Don't trust even legitimate software updates\\n5. **Incident Response**: Have plans for supply chain compromises\\n\\n**Prevention Strategies:**\\n- Software Bill of Materials (SBOM)\\n- Code signing verification\\n- Network segmentation\\n- Behavioral monitoring\\n- Regular security audits of third-party software"}, {"question_id": "notpetya_wiper_attack_2017", "type": "short_answer", "text": "The NotPetya attack (2017) initially spread using a vulnerability in Windows SMB protocol. What was the **CVE number** of this vulnerability that was also used by WannaCry earlier that year?", "points": 2, "difficulty": "intermediate", "correct_answers": ["CVE-2017-0144", "cve-2017-0144", "2017-0144"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "This vulnerability was patched by Microsoft in March 2017, but many systems remained unpatched.", "delay_seconds": 30}, {"text": "The vulnerability was in the SMBv1 protocol and was originally discovered by the NSA (EternalBlue exploit).", "delay_seconds": 60}], "feedback_correct": "Correct! CVE-2017-0144 was the EternalBlue vulnerability used by both WannaCry and NotPetya.", "feedback_incorrect": "The vulnerability was CVE-2017-0144, known as EternalBlue, affecting Windows SMBv1.", "explanation": "**NotPetya Attack Analysis (2017):**\\n\\n**Attack Overview:**\\n- **Type**: Destructive wiper malware disguised as ransomware\\n- **Primary Target**: Ukraine (95% of infections)\\n- **Global Impact**: $10+ billion in damages\\n- **Attribution**: Russian military intelligence (GRU)\\n\\n**CVE-2017-0144 (EternalBlue) Details:**\\n- **Vulnerability**: Windows SMBv1 Remote Code Execution\\n- **CVSS Score**: 8.1 (High)\\n- **Affected Systems**: Windows Vista through Windows 10, Windows Server 2008-2016\\n- **Discovery**: NSA (leaked by Shadow Brokers)\\n- **Patch Date**: March 14, 2017 (MS17-010)\\n\\n**Technical Details:**\\n```python\\n# Simplified EternalBlue exploit flow\\n# 1. Send malformed SMB packets to trigger buffer overflow\\nsmbv1_packet = craft_malicious_smb_packet()\\nsend_packet(target_ip, 445, smbv1_packet)\\n\\n# 2. Overwrite kernel memory to gain SYSTEM privileges\\nexploit_kernel_pool_overflow()\\n\\n# 3. Execute shellcode with SYSTEM privileges\\nexecute_shellcode(notpetya_payload)\\n```\\n\\n**NotPetya Attack Chain:**\\n1. **Initial Vector**: Compromised Ukrainian accounting software (MEDoc)\\n2. **Lateral Movement**: EternalBlue (CVE-2017-0144) and credential theft\\n3. **Persistence**: Scheduled tasks and service installation\\n4. **Destruction**: Overwrite Master Boot Record (MBR) and file encryption\\n5. **Deception**: Display ransomware message (but recovery impossible)\\n\\n**Why NotPetya was Devastating:**\\n- **Wiper, not Ransomware**: Designed to destroy data, not recover it\\n- **Rapid Spread**: EternalBlue allowed network-wide infection\\n- **Credential Theft**: Used Mimikatz to steal Windows credentials\\n- **MBR Destruction**: Made systems unbootable\\n\\n**Global Impact Examples:**\\n- **Maersk**: Shipping operations halted globally\\n- **FedEx**: TNT Express subsidiary severely impacted\\n- **Hospitals**: UK and US healthcare systems affected\\n- **Manufacturing**: Production lines stopped worldwide\\n\\n**Detection Indicators:**\\n```bash\\n# Network indicators\\nnetstat -an | grep :445  # SMB connections\\nwireshark -f \\\"port 445\\\" # SMB traffic analysis\\n\\n# File system indicators\\ndir C:\\\\Windows\\\\perfc.dat  # NotPetya dropper\\ndir C:\\\\Windows\\\\dllhost.dat # Secondary payload\\n\\n# Registry indicators\\nreg query \\\"HKLM\\\\SYSTEM\\\\CurrentControlSet\\\\Services\\\\csrss\\\"\\n```\\n\\n**Lessons Learned:**\\n1. **Patch Management**: Critical vulnerabilities must be patched immediately\\n2. **Network Segmentation**: Limit lateral movement capabilities\\n3. **Backup Strategy**: Offline backups essential for wiper attacks\\n4. **SMBv1 Deprecation**: Disable legacy protocols\\n5. **Geopolitical Awareness**: Nation-state attacks can have global impact\\n\\n**Prevention:**\\n- Disable SMBv1 protocol\\n- Apply security patches promptly\\n- Network segmentation and monitoring\\n- Endpoint detection and response (EDR)\\n- Regular offline backups"}]}}