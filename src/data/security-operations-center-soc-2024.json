{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "security-operations-center-soc-2024", "title": "Security Operations Center (SOC) - Advanced Operations", "description": "Comprehensive SOC operations covering threat detection, incident triage, SIEM management, and security monitoring with practical scenarios.", "author": "QuizFlow Security Team", "creation_date": "2024-01-27T13:00:00Z", "tags": ["soc", "threat-detection", "siem", "incident-triage", "security-monitoring"], "passing_score_percentage": 85, "time_limit_minutes": 50, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "soc_brute_force_response", "type": "multiple_choice", "text": "You're a SOC analyst and notice 50+ failed login attempts from IP ************* to your domain controller within 5 minutes, followed by a successful login. What should be your immediate response?", "points": 3, "difficulty": "intermediate", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Immediately disable the compromised user account and investigate the source IP", "is_correct": true, "feedback": "Correct! This indicates a likely brute force attack followed by compromise. Immediate containment is critical."}, {"id": "opt2", "text": "Wait to see if more failed attempts occur before taking action", "is_correct": false, "feedback": "Waiting allows the attacker more time to establish persistence and move laterally."}, {"id": "opt3", "text": "Only monitor the IP address for future suspicious activity", "is_correct": false, "feedback": "The successful login after multiple failures indicates compromise has already occurred."}, {"id": "opt4", "text": "Reset the user's password and notify them via email", "is_correct": false, "feedback": "This doesn't address the immediate threat and may alert the attacker."}], "hint": [{"text": "Consider the pattern: multiple failures followed by success - what does this indicate?", "delay_seconds": 30}, {"text": "Think about immediate containment vs. investigation priorities.", "delay_seconds": 60}], "feedback_correct": "Excellent! You understand proper incident response for credential-based attacks.", "feedback_incorrect": "Multiple failed logins followed by success typically indicates a successful brute force attack requiring immediate containment.", "explanation": "**Brute Force Attack Response:**\\n\\n**Attack Pattern Analysis:**\\n- 50+ failed attempts = brute force attack\\n- Successful login after failures = compromise achieved\\n- Time window (5 minutes) = automated attack\\n\\n**Immediate Response Steps:**\\n1. **Disable compromised account** - prevent further access\\n2. **Block source IP** - prevent continued attacks\\n3. **Check for lateral movement** - review other systems accessed\\n4. **Preserve evidence** - capture logs before they rotate\\n\\n**SIEM Rule Example:**\\n```\\nindex=security sourcetype=windows_security EventCode=4625 OR EventCode=4624\\n| stats count(eval(EventCode=4625)) as failures, count(eval(EventCode=4624)) as success by src_ip, user\\n| where failures > 10 AND success > 0\\n| eval risk_score = failures * 2 + success * 10\\n```\\n\\n**Investigation Priorities:**\\n1. Timeline of compromise\\n2. Systems accessed post-compromise\\n3. Data accessed or exfiltrated\\n4. Persistence mechanisms deployed"}, {"question_id": "siem_rule_tuning", "type": "short_answer", "text": "Your SIEM rule for detecting PowerShell execution is generating 500+ alerts per day, mostly false positives from legitimate admin scripts. What Splunk search modification would help reduce false positives while maintaining detection of malicious PowerShell?", "points": 3, "difficulty": "advanced", "correct_answers": ["exclude legitimate scripts", "whitelist known good", "add process parent filter", "filter by command line arguments", "exclude scheduled tasks"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Consider what makes legitimate PowerShell different from malicious PowerShell.", "delay_seconds": 30}, {"text": "Think about parent processes, script locations, and command line patterns.", "delay_seconds": 60}], "feedback_correct": "Excellent! Proper rule tuning balances detection with operational efficiency.", "feedback_incorrect": "Focus on characteristics that distinguish legitimate from malicious PowerShell execution.", "explanation": "**SIEM Rule Tuning for PowerShell Detection:**\\n\\n**Original Rule (High False Positives):**\\n```\\nindex=windows sourcetype=WinEventLog:Microsoft-Windows-PowerShell/Operational\\n| search EventCode=4103 OR EventCode=4104\\n```\\n\\n**Tuned Rule (Reduced False Positives):**\\n```\\nindex=windows sourcetype=WinEventLog:Microsoft-Windows-PowerShell/Operational\\n| search EventCode=4103 OR EventCode=4104\\n| search NOT (\\n    (parent_process=\\\"C:\\\\\\\\Windows\\\\\\\\System32\\\\\\\\svchost.exe\\\" AND script_path=\\\"C:\\\\\\\\Windows\\\\\\\\System32\\\\\\\\*\\\")\\n    OR (user=\\\"SYSTEM\\\" AND script_name=\\\"*scheduled*\\\")\\n    OR script_path=\\\"C:\\\\\\\\Program Files\\\\\\\\*\\\"\\n    OR CommandLine=\\\"*Get-EventLog*\\\" OR CommandLine=\\\"*Get-Process*\\\"\\n)\\n| search (\\n    CommandLine=\\\"*-EncodedCommand*\\\" OR CommandLine=\\\"*-Enc*\\\"\\n    OR CommandLine=\\\"*DownloadString*\\\" OR CommandLine=\\\"*IEX*\\\"\\n    OR CommandLine=\\\"*Invoke-Expression*\\\" OR CommandLine=\\\"*bypass*\\\"\\n    OR parent_process!=\\\"powershell_ise.exe\\\"\\n)\\n```\\n\\n**Key Tuning Strategies:**\\n1. **Whitelist Known Good**: Exclude legitimate admin scripts\\n2. **Parent Process Filtering**: Focus on suspicious parent processes\\n3. **Command Line Analysis**: Look for obfuscation and suspicious patterns\\n4. **User Context**: Different rules for SYSTEM vs user accounts\\n5. **Script Location**: Trusted paths vs suspicious locations"}, {"question_id": "incident_triage_priority", "type": "multiple_choice", "text": "You receive three alerts simultaneously: (1) Malware detected on a user workstation, (2) Unusual outbound traffic from the database server, (3) Failed login attempts on a test server. How should you prioritize these incidents?", "points": 3, "difficulty": "intermediate", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Database server (2), Malware (1), Failed logins (3)", "is_correct": true, "feedback": "Correct! Database servers contain critical data and unusual outbound traffic suggests potential data exfiltration."}, {"id": "opt2", "text": "Malware (1), Database server (2), Failed logins (3)", "is_correct": false, "feedback": "While malware is serious, potential data exfiltration from a database server is typically higher priority."}, {"id": "opt3", "text": "Failed logins (3), Malware (1), Database server (2)", "is_correct": false, "feedback": "Failed logins on test servers are usually lower priority than production system compromises."}, {"id": "opt4", "text": "Handle all three simultaneously with equal priority", "is_correct": false, "feedback": "Resource constraints require prioritization based on business impact and data sensitivity."}], "hint": [{"text": "Consider the criticality of the affected systems and potential business impact.", "delay_seconds": 30}, {"text": "Think about data sensitivity and what 'unusual outbound traffic' might indicate.", "delay_seconds": 60}], "feedback_correct": "Excellent! You understand incident prioritization based on business impact and data sensitivity.", "feedback_incorrect": "Prioritize incidents based on system criticality, data sensitivity, and potential business impact.", "explanation": "**Incident Triage Prioritization:**\\n\\n**Priority 1: Database Server Unusual Outbound Traffic**\\n- **Why**: Potential data exfiltration\\n- **Business Impact**: High - sensitive data at risk\\n- **Urgency**: Immediate containment needed\\n- **Actions**: Network isolation, traffic analysis, data inventory\\n\\n**Priority 2: Malware on User Workstation**\\n- **Why**: Active compromise with lateral movement potential\\n- **Business Impact**: Medium - could spread to other systems\\n- **Urgency**: High - prevent spread\\n- **Actions**: Isolate workstation, malware analysis, check for persistence\\n\\n**Priority 3: Failed Logins on Test Server**\\n- **Why**: Brute force attempt on non-production system\\n- **Business Impact**: Low - test environment\\n- **Urgency**: Medium - monitor and block if needed\\n- **Actions**: IP blocking, password policy review\\n\\n**Prioritization Factors:**\\n1. **Data Sensitivity**: Production > Test\\n2. **System Criticality**: Database > Workstation > Test\\n3. **Attack Stage**: Data exfiltration > Initial compromise > Reconnaissance\\n4. **Business Impact**: Revenue/reputation risk assessment"}, {"question_id": "alert_correlation_analysis", "type": "short_answer", "text": "You notice these correlated events within 10 minutes: (1) VPN login from unusual location, (2) Multiple file downloads from file server, (3) Email forwarding rule created. What type of attack pattern does this suggest?", "points": 3, "difficulty": "advanced", "correct_answers": ["insider threat", "account takeover", "data exfiltration", "compromised credentials", "business email compromise"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Consider the sequence: external access → data collection → persistence mechanism.", "delay_seconds": 30}, {"text": "Think about what email forwarding rules accomplish for an attacker.", "delay_seconds": 60}], "feedback_correct": "Excellent! You recognize the classic pattern of account takeover leading to data exfiltration.", "feedback_incorrect": "This pattern suggests account compromise with systematic data collection and persistence via email forwarding.", "explanation": "**Attack Pattern Analysis: Account Takeover → Data Exfiltration**\\n\\n**Attack Timeline:**\\n1. **Initial Access**: VPN login from unusual location\\n2. **Collection**: Multiple file downloads (data gathering)\\n3. **Persistence**: Email forwarding rule (maintain access)\\n\\n**Attack Indicators:**\\n- **Geographical Anomaly**: VPN from unexpected location\\n- **Bulk Data Access**: Multiple file downloads in short timeframe\\n- **Stealth Persistence**: Email forwarding for continued access\\n\\n**Typical Attack Flow:**\\n```\\nCredential Compromise → Remote Access → Data Discovery → \\nBulk Download → Persistence Mechanism → Continued Monitoring\\n```\\n\\n**Investigation Steps:**\\n1. **Verify User**: Contact user to confirm legitimate activity\\n2. **Credential Analysis**: Check for password reuse, breaches\\n3. **Data Inventory**: Identify what files were accessed\\n4. **Email Analysis**: Review forwarding rule details\\n5. **Timeline Reconstruction**: Map complete attack sequence\\n\\n**Containment Actions:**\\n- Disable compromised account\\n- Remove email forwarding rule\\n- Block suspicious IP addresses\\n- Reset user credentials\\n- Review accessed data for sensitivity"}]}}