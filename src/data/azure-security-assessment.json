{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "azure-security-assessment", "title": "Azure Security Assessment & Misconfigurations", "description": "Real-world Azure security assessment scenarios, common misconfigurations, and practical exploitation techniques based on actual cloud security research.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T22:00:00Z", "tags": ["azure", "cloud-security", "assessment", "active-directory", "storage"], "passing_score_percentage": 80, "time_limit_minutes": 35, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "azure_ad_token_hijacking_2021", "type": "multiple_choice", "text": "In 2021, researchers discovered a technique to hijack Azure AD tokens through a misconfigured service principal. What was the **primary attack vector** that allowed token theft?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Exploiting overprivileged service principal with certificate-based authentication", "is_correct": true, "feedback": "Correct! Overprivileged service principals with certificate auth can be exploited for token theft."}, {"id": "opt2", "text": "Brute forcing Azure AD user passwords through the Graph API", "is_correct": false, "feedback": "While password attacks exist, this specific technique involved service principal exploitation."}, {"id": "opt3", "text": "SQL injection in Azure SQL Database to extract stored tokens", "is_correct": false, "feedback": "This attack vector was focused on Azure AD authentication, not database injection."}, {"id": "opt4", "text": "Cross-site scripting (XSS) in the Azure portal interface", "is_correct": false, "feedback": "The attack was through API abuse, not web application vulnerabilities."}], "hint": [{"text": "This attack involved abusing legitimate Azure AD authentication mechanisms.", "delay_seconds": 30}, {"text": "Think about how service principals authenticate and what permissions they might have.", "delay_seconds": 60}], "feedback_correct": "Excellent! Understanding service principal security is crucial for Azure environments.", "feedback_incorrect": "The attack exploited overprivileged service principals with certificate-based authentication.", "explanation": "**Azure AD Service Principal Token Hijacking:**\\n\\n**Attack Scenario:**\\n- **Target**: Overprivileged service principal\\n- **Method**: Certificate-based authentication abuse\\n- **Impact**: Full tenant access through stolen tokens\\n\\n**Technical Details:**\\n```bash\\n# Enumerate service principals\\naz ad sp list --all\\n\\n# Check service principal permissions\\naz role assignment list --assignee <service-principal-id>\\n\\n# Abuse certificate authentication\\naz login --service-principal -u <app-id> -p <cert-path> --tenant <tenant-id>\\n```\\n\\n**Common Misconfigurations:**\\n1. **Excessive Permissions**: Service principals with Global Admin rights\\n2. **Long-lived Certificates**: Certificates valid for years without rotation\\n3. **Weak Certificate Storage**: Certificates stored in accessible locations\\n4. **No Monitoring**: Lack of service principal activity monitoring\\n\\n**Attack Flow:**\\n1. **Discovery**: Identify overprivileged service principals\\n2. **Certificate Theft**: Extract certificate from compromised system\\n3. **Authentication**: Use certificate to authenticate as service principal\\n4. **Token Abuse**: Use acquired tokens for lateral movement\\n5. **Persistence**: Create additional service principals or users\\n\\n**Detection Methods:**\\n```powershell\\n# Monitor service principal sign-ins\\nGet-AzureADAuditSignInLogs | Where-Object {$_.AppDisplayName -like '*service*'}\\n\\n# Check for unusual token requests\\nGet-AzureADAuditDirectoryLogs | Where-Object {$_.Category -eq 'ApplicationManagement'}\\n```\\n\\n**Prevention Strategies:**\\n1. **Principle of Least Privilege**: Minimal required permissions\\n2. **Certificate Rotation**: Regular certificate renewal\\n3. **Conditional Access**: Restrict service principal access\\n4. **Monitoring**: Azure Sentinel for anomaly detection\\n5. **Secure Storage**: Azure Key Vault for certificate management\\n\\n**Remediation:**\\n- Audit all service principal permissions\\n- Implement certificate lifecycle management\\n- Enable Azure AD Identity Protection\\n- Use managed identities where possible"}, {"question_id": "azure_storage_account_exposure", "type": "short_answer", "text": "You discover an Azure Storage Account with a misconfigured access policy that allows anonymous read access. What Azure CLI command would you use to **disable** anonymous blob access for this storage account?", "points": 2, "difficulty": "intermediate", "correct_answers": ["az storage account update --allow-blob-public-access false", "az storage account update --name <account> --resource-group <rg> --allow-blob-public-access false", "az storage account update --allow-blob-public-access=false"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Look for the Azure CLI command that updates storage account settings.", "delay_seconds": 30}, {"text": "The parameter you need controls blob public access at the account level.", "delay_seconds": 60}], "feedback_correct": "Correct! This command disables anonymous blob access at the storage account level.", "feedback_incorrect": "Use 'az storage account update --allow-blob-public-access false' to disable anonymous access.", "explanation": "**Azure Storage Account Security:**\\n\\n**Anonymous Access Risks:**\\n- Public read access to sensitive data\\n- Data exfiltration without authentication\\n- Compliance violations (GDPR, HIPAA)\\n- Potential for data manipulation if write access enabled\\n\\n**Remediation Commands:**\\n```bash\\n# Disable anonymous blob access (account level)\\naz storage account update \\\\\\n  --name mystorageaccount \\\\\\n  --resource-group myresourcegroup \\\\\\n  --allow-blob-public-access false\\n\\n# Check current setting\\naz storage account show \\\\\\n  --name mystorageaccount \\\\\\n  --resource-group myresourcegroup \\\\\\n  --query allowBlobPublicAccess\\n\\n# List all storage accounts with public access\\naz storage account list --query \\\"[?allowBlobPublicAccess==\\`true\\`].{Name:name, ResourceGroup:resourceGroup}\\\"\\n```\\n\\n**Container-Level Security:**\\n```bash\\n# Set container to private\\naz storage container set-permission \\\\\\n  --name mycontainer \\\\\\n  --public-access off \\\\\\n  --account-name mystorageaccount\\n\\n# Check container permissions\\naz storage container show-permission \\\\\\n  --name mycontainer \\\\\\n  --account-name mystorageaccount\\n```\\n\\n**Best Practices:**\\n1. **Default Deny**: Disable public access by default\\n2. **Least Privilege**: Use SAS tokens for limited access\\n3. **Network Restrictions**: Configure firewall rules\\n4. **Monitoring**: Enable storage analytics and logging\\n5. **Encryption**: Enable encryption at rest and in transit\\n\\n**Monitoring and Auditing:**\\n```bash\\n# Enable storage logging\\naz storage logging update \\\\\\n  --services b \\\\\\n  --log rwd \\\\\\n  --retention 90 \\\\\\n  --account-name mystorageaccount\\n\\n# Check access logs\\naz storage blob list \\\\\\n  --container-name \\\"$logs\\\" \\\\\\n  --account-name mystorageaccount\\n```\\n\\n**Compliance Considerations:**\\n- Regular access reviews\\n- Data classification and labeling\\n- Backup and disaster recovery\\n- Legal hold and retention policies"}]}}