{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "advanced-network-reconnaissance-2024", "title": "Advanced Network Reconnaissance & Intelligence Gathering", "description": "Advanced network reconnaissance covering stealth scanning, OSINT, and intelligence gathering with real-world scenarios and practical tool usage.", "author": "QuizFlow Security Team", "creation_date": "2024-01-27T15:00:00Z", "tags": ["network-reconnaissance", "osint", "stealth-scanning", "intelligence-gathering"], "passing_score_percentage": 85, "time_limit_minutes": 45, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "nmap_stealth_scan_evasion", "type": "multiple_choice", "text": "You're conducting a penetration test against a target network (***********/24) that has an IDS monitoring for port scans. Which Nmap command would be most effective for stealthy reconnaissance?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "nmap -sS -T1 -f --randomize-hosts ***********/24", "is_correct": true, "feedback": "Correct! SYN scan with slow timing, fragmentation, and randomized order minimizes detection."}, {"id": "opt2", "text": "nmap -sT -T5 --min-rate 1000 ***********/24", "is_correct": false, "feedback": "TCP connect scan with aggressive timing is easily detected by IDS systems."}, {"id": "opt3", "text": "nmap -sU -p- ***********/24", "is_correct": false, "feedback": "UDP scan of all ports is very noisy and time-consuming."}, {"id": "opt4", "text": "nmap -sV -A --script vuln ***********/24", "is_correct": false, "feedback": "Version detection and vulnerability scripts generate significant traffic."}], "hint": [{"text": "Consider scan types that don't complete the TCP handshake and timing options.", "delay_seconds": 30}, {"text": "Think about fragmentation and host randomization for IDS evasion.", "delay_seconds": 60}], "feedback_correct": "Excellent! You understand stealth scanning techniques for IDS evasion.", "feedback_incorrect": "Stealth scanning requires SYN scans, slow timing, fragmentation, and randomization to avoid detection.", "explanation": "**Stealth Network Reconnaissance Techniques:**\\n\\n**Command Breakdown:**\\n```bash\\nnmap -sS -T1 -f --randomize-hosts ***********/24\\n```\\n\\n**Stealth Techniques Used:**\\n\\n**1. <PERSON><PERSON><PERSON> (-sS):**\\n- Sends SYN packets without completing handshake\\n- Doesn't create full TCP connections\\n- Harder to detect than connect scans\\n- Requires root privileges\\n\\n**2. Slow Timing (-T1):**\\n- Paranoid timing template\\n- 15-second delays between probes\\n- Mimics normal network traffic patterns\\n- Avoids triggering rate-based detection\\n\\n**3. Fragmentation (-f):**\\n- Splits TCP header across multiple IP fragments\\n- Evades simple packet inspection\\n- Some firewalls/IDS can't reassemble fragments\\n- May bypass basic filtering rules\\n\\n**4. Host Randomization (--randomize-hosts):**\\n- Scans targets in random order\\n- Prevents sequential IP scanning patterns\\n- Makes correlation more difficult\\n- Appears less like automated scanning\\n\\n**Advanced Evasion Techniques:**\\n\\n**Decoy <PERSON>ann<PERSON>:**\\n```bash\\nnmap -sS -D ********,********,ME,******** *************\\n```\\n\\n**Source Port Spoofing:**\\n```bash\\nnmap -sS --source-port 53 *************\\n```\\n\\n**Idle Scan (Zombie):**\\n```bash\\nnmap -sI zombie_host:port target_host\\n```\\n\\n**Detection Avoidance:**\\n- Use legitimate source ports (53, 80, 443)\\n- Scan during business hours\\n- Limit concurrent connections\\n- Use multiple source IPs\\n- Employ traffic shaping"}, {"question_id": "osint_subdomain_enumeration", "type": "short_answer", "text": "During OSINT reconnaissance of 'example.com', you want to find subdomains without directly querying their DNS servers. What passive technique or tool would you use to discover subdomains like 'mail.example.com' or 'dev.example.com'?", "points": 2, "difficulty": "intermediate", "correct_answers": ["certificate transparency logs", "crt.sh", "censys", "shodan", "google dorking", "wayback machine", "virustotal", "passive dns"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Think about public databases that log SSL certificates and domain information.", "delay_seconds": 30}, {"text": "Consider certificate transparency logs and search engines that index certificates.", "delay_seconds": 60}], "feedback_correct": "Excellent! Passive subdomain enumeration avoids direct DNS queries to the target.", "feedback_incorrect": "Use certificate transparency logs, search engines, or passive DNS databases for subdomain discovery.", "explanation": "**Passive Subdomain Enumeration Techniques:**\\n\\n**1. Certificate Transparency Logs:**\\n```bash\\n# crt.sh database query\\ncurl -s \\\"https://crt.sh/?q=%.example.com&output=json\\\" | jq -r '.[].name_value' | sort -u\\n\\n# Manual search\\nhttps://crt.sh/?q=example.com\\n```\\n\\n**2. Search Engine Dorking:**\\n```\\n# Google dorking\\nsite:example.com -www\\nsite:*.example.com\\n\\n# Bing search\\nsite:example.com\\n```\\n\\n**3. Passive DNS Databases:**\\n```bash\\n# Using Censys\\ncensys search \\\"example.com\\\" --index-type certificates\\n\\n# Using Shodan\\nshodan search hostname:example.com\\n```\\n\\n**4. Archive Analysis:**\\n```bash\\n# Wayback Machine\\nwaybackurls example.com | grep -E \\\"https?://[^/]*\\\\.example\\\\.com\\\" | sort -u\\n\\n# Web archive subdomain extraction\\ncurl -s \\\"http://web.archive.org/cdx/search/cdx?url=*.example.com&output=text&fl=original&collapse=urlkey\\\"\\n```\\n\\n**5. Third-party Services:**\\n```bash\\n# VirusTotal\\ncurl -s \\\"https://www.virustotal.com/vtapi/v2/domain/report?apikey=API_KEY&domain=example.com\\\"\\n\\n# SecurityTrails\\ncurl -s \\\"https://api.securitytrails.com/v1/domain/example.com/subdomains\\\" -H \\\"APIKEY: YOUR_API_KEY\\\"\\n```\\n\\n**Automated Tools:**\\n```bash\\n# Subfinder\\nsubfinder -d example.com -silent\\n\\n# Amass\\namass enum -passive -d example.com\\n\\n# Assetfinder\\nassetfinder example.com\\n```\\n\\n**Why Passive Techniques:**\\n- No direct DNS queries to target\\n- Undetectable by target monitoring\\n- Leverages public data sources\\n- Often reveals more subdomains\\n- Safe for reconnaissance phase"}]}}