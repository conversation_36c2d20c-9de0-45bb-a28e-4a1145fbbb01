{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "advanced-penetration-testing-methodology-2024", "title": "Advanced Penetration Testing Methodology", "description": "Comprehensive penetration testing methodology covering reconnaissance, exploitation, post-exploitation, and reporting phases with real-world scenarios.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T15:00:00Z", "tags": ["penetration-testing", "methodology", "reconnaissance", "exploitation", "post-exploitation", "reporting"], "passing_score_percentage": 80, "time_limit_minutes": 60, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "nmap_stealth_scanning", "type": "multiple_choice", "text": "During a penetration test, you need to scan a target network (***********/24) without triggering IDS alerts. Which Nmap command would be **most effective** for stealth reconnaissance?", "points": 2, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "nmap -sS -T1 -f --randomize-hosts ***********/24", "is_correct": true, "feedback": "Correct! SYN scan with slow timing, fragmentation, and randomized hosts minimizes detection."}, {"id": "opt2", "text": "nmap -sT -T5 ***********/24", "is_correct": false, "feedback": "TCP connect scan with aggressive timing is easily detected by IDS systems."}, {"id": "opt3", "text": "nmap -sU --top-ports 1000 ***********/24", "is_correct": false, "feedback": "UDP scan of top ports is noisy and slow, not ideal for stealth."}, {"id": "opt4", "text": "nmap -sn ***********/24", "is_correct": false, "feedback": "Ping sweep only discovers hosts but doesn't provide port information."}], "hint": [{"text": "Consider scan types that don't complete the TCP handshake and timing options that spread out the scan.", "delay_seconds": 30}, {"text": "SYN scan (-sS) with slow timing (-T1) and fragmentation (-f) helps avoid detection.", "delay_seconds": 60}], "feedback_correct": "Excellent! This combination provides comprehensive scanning while minimizing detection risk.", "feedback_incorrect": "For stealth scanning, use SYN scan with slow timing and evasion techniques.", "explanation": "**Stealth Scanning Techniques:**\\n\\n**Command Breakdown:**\\n- **-sS**: SYN scan (half-open scan) - doesn't complete TCP handshake\\n- **-T1**: Paranoid timing - very slow but stealthy\\n- **-f**: Fragment packets to evade simple packet filters\\n- **--randomize-hosts**: Scan targets in random order\\n\\n**Why This Works:**\\n1. **SYN Scan**: Sends SYN packet, receives SYN-ACK (open) or RST (closed), then sends RST\\n2. **Slow Timing**: Reduces packets per second, harder to correlate\\n3. **Fragmentation**: Splits packets to bypass basic filters\\n4. **Randomization**: Prevents pattern recognition\\n\\n**Additional Stealth Techniques:**\\n```bash\\n# Decoy scanning\\nnmap -sS -D RND:10 target\\n\\n# Source port spoofing\\nnmap -sS --source-port 53 target\\n\\n# Idle scan (zombie scan)\\nnmap -sI zombie_host target\\n\\n# Custom packet crafting\\nhping3 -S -p 80 -c 1 target\\n```\\n\\n**IDS Evasion Methods:**\\n- **Timing Delays**: Space out packets over time\\n- **Source IP Spoofing**: Use decoy addresses\\n- **Protocol Manipulation**: Fragment, encode, or obfuscate\\n- **Traffic Blending**: Hide in legitimate traffic patterns", "single_correct_answer": true}, {"question_id": "privilege_escalation_windows", "type": "short_answer", "text": "You've gained initial access to a Windows 10 machine as a low-privileged user. What PowerShell command would you run to **enumerate all services** running with SYSTEM privileges that might be vulnerable to privilege escalation?", "points": 2, "difficulty": "advanced", "correct_answers": ["Get-WmiObject win32_service | Where-Object {$_.StartName -eq 'LocalSystem'} | Select Name,PathName,StartName", "Get-Service | Where-Object {$_.StartType -ne 'Disabled'} | ForEach-Object {Get-WmiObject -Class Win32_Service -Filter \"Name='$($_.Name)'\"} | Where-Object {$_.StartName -eq 'LocalSystem'}", "wmic service get name,pathname,startname | findstr /i \"LocalSystem\"", "sc query | findstr SERVICE_NAME"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Think about Windows services that run with high privileges and how to query their properties.", "delay_seconds": 30}, {"text": "Use Get-WmiObject or Get-Service to enumerate services and filter by StartName or account.", "delay_seconds": 60}], "feedback_correct": "Correct! This command effectively enumerates high-privilege services for escalation opportunities.", "feedback_incorrect": "Use Get-WmiObject win32_service with filtering to find SYSTEM services.", "explanation": "**Windows Privilege Escalation - Service Enumeration:**\\n\\n**Key PowerShell Commands:**\\n```powershell\\n# Enumerate SYSTEM services\\nGet-WmiObject win32_service | Where-Object {$_.StartName -eq 'LocalSystem'} | Select Name,PathName,StartName\\n\\n# Check service permissions\\nGet-Acl -Path 'C:\\\\Program Files\\\\Service\\\\service.exe' | Format-List\\n\\n# Find unquoted service paths\\nGet-WmiObject win32_service | Where-Object {$_.PathName -notmatch '\"' -and $_.PathName -match ' '} | Select Name,PathName\\n```\\n\\n**Common Privilege Escalation Vectors:**\\n1. **Unquoted Service Paths**: Exploit spaces in service executable paths\\n2. **Weak Service Permissions**: Modify service configuration or executable\\n3. **DLL Hijacking**: Replace legitimate DLLs with malicious ones\\n4. **Registry Permissions**: Modify service registry keys\\n5. **Scheduled Tasks**: Exploit tasks running with high privileges\\n\\n**Service Exploitation Process:**\\n1. **Enumerate Services**: Find services running as SYSTEM/Administrator\\n2. **Check Permissions**: Verify write access to service files/registry\\n3. **Exploit Weakness**: Replace executable, modify config, or hijack DLL\\n4. **Restart Service**: Trigger execution of malicious code\\n5. **Maintain Access**: Establish persistence with elevated privileges\\n\\n**Detection & Prevention:**\\n- **Principle of Least Privilege**: Run services with minimal required permissions\\n- **Proper Quoting**: Quote all service paths with spaces\\n- **File Permissions**: Restrict write access to service directories\\n- **Regular Audits**: Monitor service configurations and permissions"}]}}