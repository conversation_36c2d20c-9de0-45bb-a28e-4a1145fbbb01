{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "aws-security-real-incidents-2024", "title": "AWS Security - Real Incident Analysis", "description": "Analysis of real AWS security incidents, misconfigurations, and practical remediation strategies based on actual breaches and security research findings.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T21:00:00Z", "tags": ["aws", "cloud-security", "real-incidents", "s3", "iam", "practical"], "passing_score_percentage": 85, "time_limit_minutes": 40, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "capital_one_breach_2019_ssrf", "type": "multiple_choice", "text": "The Capital One breach (2019) compromised 100+ million customer records through a misconfigured AWS WAF and SSRF vulnerability. What AWS service did the attacker abuse to access the sensitive data?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "AWS Lambda functions with overprivileged IAM roles", "is_correct": false, "feedback": "While <PERSON><PERSON> was involved, the key was the metadata service access."}, {"id": "opt2", "text": "EC2 Instance Metadata Service (IMDS) to retrieve IAM credentials", "is_correct": true, "feedback": "Correct! The attacker used SSRF to access IMDS and retrieve IAM credentials."}, {"id": "opt3", "text": "S3 bucket with public read permissions", "is_correct": false, "feedback": "The S3 buckets weren't public - access was gained through stolen IAM credentials."}, {"id": "opt4", "text": "RDS database with default credentials", "is_correct": false, "feedback": "The attack didn't involve direct database access but rather S3 data exfiltration."}], "hint": [{"text": "The attack involved Server-Side Request Forgery (SSRF) to access internal AWS services.", "delay_seconds": 30}, {"text": "Think about what internal AWS service provides temporary credentials to EC2 instances.", "delay_seconds": 60}], "feedback_correct": "Excellent! Understanding IMDS attacks is crucial for AWS security.", "feedback_incorrect": "The key was SSRF exploitation to access the EC2 metadata service for IAM credentials.", "explanation": "**Capital One Breach Analysis (2019):**\\n\\n**Attack Chain:**\\n1. **Initial Access**: SSRF vulnerability in web application\\n2. **Credential Theft**: Access EC2 Instance Metadata Service (IMDS)\\n3. **Privilege Escalation**: Retrieve IAM role credentials\\n4. **Data Exfiltration**: Access S3 buckets with stolen credentials\\n\\n**Technical Details:**\\n```bash\\n# SSRF payload to access IMDS\\ncurl http://169.254.169.254/latest/meta-data/iam/security-credentials/\\n\\n# Retrieve IAM role credentials\\ncurl http://169.254.169.254/latest/meta-data/iam/security-credentials/ROLE_NAME\\n\\n# Use stolen credentials to access S3\\naws s3 ls s3://bucket-name --profile stolen-creds\\n```\\n\\n**Root Causes:**\\n1. **WAF Misconfiguration**: Failed to block SSRF attacks\\n2. **Overprivileged IAM Role**: EC2 role had excessive S3 permissions\\n3. **No IMDS Protection**: IMDSv1 allowed unrestricted access\\n4. **Insufficient Monitoring**: No alerts on unusual S3 access patterns\\n\\n**AWS Security Improvements Post-Breach:**\\n- **IMDSv2**: Requires session tokens, prevents SSRF\\n- **VPC Endpoints**: Reduce IMDS exposure\\n- **CloudTrail**: Enhanced API logging\\n- **GuardDuty**: Anomaly detection\\n\\n**Prevention Strategies:**\\n```json\\n{\\n  \\\"Version\\\": \\\"2012-10-17\\\",\\n  \\\"Statement\\\": [\\n    {\\n      \\\"Effect\\\": \\\"Deny\\\",\\n      \\\"Principal\\\": \\\"*\\\",\\n      \\\"Action\\\": \\\"s3:*\\\",\\n      \\\"Resource\\\": \\\"*\\\",\\n      \\\"Condition\\\": {\\n        \\\"Bool\\\": {\\n          \\\"aws:ViaAWSService\\\": \\\"false\\\"\\n        }\\n      }\\n    }\\n  ]\\n}\\n```\\n\\n**Lessons Learned:**\\n1. **Principle of Least Privilege**: Minimal IAM permissions\\n2. **IMDS Hardening**: Use IMDSv2, disable if not needed\\n3. **Network Security**: Proper WAF configuration\\n4. **Monitoring**: CloudTrail + GuardDuty for anomaly detection\\n5. **Regular Audits**: Review IAM roles and S3 bucket policies"}]}}