{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "cloud-incident-response-2024", "title": "Cloud Incident Response & Forensics", "description": "Advanced cloud incident response covering AWS/Azure forensics, container incident analysis, and real-world cloud security incident scenarios.", "author": "QuizFlow Security Team", "creation_date": "2024-01-27T10:00:00Z", "tags": ["cloud-forensics", "incident-response", "aws-security", "azure-security"], "passing_score_percentage": 80, "time_limit_minutes": 35, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "aws_cloudtrail_analysis_2024", "type": "short_answer", "text": "During a cloud incident investigation, you discover suspicious API calls in AWS CloudTrail logs showing 'AssumeRole' events from an unusual IP address. What AWS service should you check next to understand what permissions the assumed role had?", "points": 3, "difficulty": "intermediate", "correct_answers": ["IAM", "AWS IAM", "Identity and Access Management", "IAM roles", "IAM policies"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Think about where AWS stores role definitions and permissions.", "delay_seconds": 30}, {"text": "Consider the service that manages identities, roles, and policies in AWS.", "delay_seconds": 60}], "feedback_correct": "Correct! IAM contains the role definitions and attached policies that determine permissions.", "feedback_incorrect": "Check AWS IAM to see the role's attached policies and permissions.", "explanation": "**Cloud Incident Response: AWS IAM Analysis**\\n\\n**Investigation Workflow:**\\n\\n**1. CloudTrail Analysis:**\\n```json\\n{\\n  \\\"eventTime\\\": \\\"2024-01-15T10:30:00Z\\\",\\n  \\\"eventName\\\": \\\"AssumeRole\\\",\\n  \\\"sourceIPAddress\\\": \\\"************\\\",\\n  \\\"userIdentity\\\": {\\n    \\\"type\\\": \\\"AssumedRole\\\",\\n    \\\"principalId\\\": \\\"AIDACKCEVSQ6C2EXAMPLE\\\",\\n    \\\"arn\\\": \\\"arn:aws:sts::123456789012:assumed-role/suspicious-role/session\\\"\\n  },\\n  \\\"requestParameters\\\": {\\n    \\\"roleArn\\\": \\\"arn:aws:iam::123456789012:role/suspicious-role\\\",\\n    \\\"roleSessionName\\\": \\\"session\\\"\\n  }\\n}\\n```\\n\\n**2. IAM Role Investigation:**\\n```bash\\n# Get role details\\naws iam get-role --role-name suspicious-role\\n\\n# List attached policies\\naws iam list-attached-role-policies --role-name suspicious-role\\n\\n# Get policy details\\naws iam get-policy-version --policy-arn arn:aws:iam::123456789012:policy/policy-name --version-id v1\\n```\\n\\n**3. Permission Analysis:**\\n```json\\n{\\n  \\\"Version\\\": \\\"2012-10-17\\\",\\n  \\\"Statement\\\": [\\n    {\\n      \\\"Effect\\\": \\\"Allow\\\",\\n      \\\"Action\\\": [\\n        \\\"s3:GetObject\\\",\\n        \\\"s3:ListBucket\\\",\\n        \\\"ec2:DescribeInstances\\\"\\n      ],\\n      \\\"Resource\\\": \\\"*\\\"\\n    }\\n  ]\\n}\\n```\\n\\n**4. Impact Assessment:**\\n- **Data Access**: What data could be accessed?\\n- **Service Control**: What services could be manipulated?\\n- **Privilege Escalation**: Could permissions be expanded?\\n- **Persistence**: Could backdoors be created?\\n\\n**Cloud Forensics Best Practices:**\\n\\n**1. Log Collection:**\\n```bash\\n# CloudTrail logs\\naws logs describe-log-groups\\naws logs get-log-events --log-group-name CloudTrail/logs\\n\\n# VPC Flow Logs\\naws ec2 describe-flow-logs\\n\\n# GuardDuty findings\\naws guardduty list-findings --detector-id detector-id\\n```\\n\\n**2. Timeline Reconstruction:**\\n```python\\n# Parse CloudTrail events\\nimport json\\nfrom datetime import datetime\\n\\ndef analyze_cloudtrail(log_file):\\n    events = []\\n    with open(log_file) as f:\\n        for line in f:\\n            event = json.loads(line)\\n            if event['sourceIPAddress'] == '************':\\n                events.append({\\n                    'time': event['eventTime'],\\n                    'action': event['eventName'],\\n                    'resource': event.get('resources', [])\\n                })\\n    return sorted(events, key=lambda x: x['time'])\\n```\\n\\n**3. Evidence Preservation:**\\n- **Snapshot EBS volumes** before analysis\\n- **Export CloudTrail logs** to secure location\\n- **Document chain of custody** for all evidence\\n- **Create forensic images** of affected instances"}]}}