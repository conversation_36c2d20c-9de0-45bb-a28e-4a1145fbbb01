{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "cyber-threat-modeling-2024", "title": "Cyber Threat Modeling & Risk Assessment", "description": "Advanced threat modeling covering STRIDE methodology, PASTA framework, attack tree analysis, and real-world threat assessment scenarios.", "author": "QuizFlow Security Team", "creation_date": "2024-01-27T10:00:00Z", "tags": ["threat-modeling", "stride", "pasta", "risk-assessment", "attack-trees"], "passing_score_percentage": 80, "time_limit_minutes": 45, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "stride_methodology_analysis_2024", "type": "multiple_choice", "text": "You're conducting a threat model for a banking application's login system. A threat actor could intercept authentication tokens during transmission and replay them to gain unauthorized access. According to the STRIDE methodology, what type of threat is this?", "points": 3, "difficulty": "intermediate", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Spoofing Identity", "is_correct": false, "feedback": "Spoofing involves impersonating another entity, not replaying captured tokens."}, {"id": "opt2", "text": "Tampering with Data", "is_correct": false, "feedback": "Tampering involves modifying data, not replaying existing tokens."}, {"id": "opt3", "text": "Repudiation", "is_correct": false, "feedback": "Repudiation involves denying actions, not token replay attacks."}, {"id": "opt4", "text": "Information Disclosure", "is_correct": false, "feedback": "While tokens are disclosed, the primary threat is unauthorized access through replay."}, {"id": "opt5", "text": "Denial of Service", "is_correct": false, "feedback": "DoS involves making services unavailable, not gaining unauthorized access."}, {"id": "opt6", "text": "Elevation of Privilege", "is_correct": true, "feedback": "Correct! Replaying tokens to gain unauthorized access is elevation of privilege."}], "hint": [{"text": "Consider what the attacker achieves by replaying the authentication token.", "delay_seconds": 30}, {"text": "Think about which STRIDE category covers gaining unauthorized access or permissions.", "delay_seconds": 60}], "feedback_correct": "Excellent! Token replay attacks result in elevation of privilege.", "feedback_incorrect": "Token replay attacks allow unauthorized access, which is elevation of privilege in STRIDE.", "explanation": "**STRIDE Threat Modeling Analysis:**\\n\\n**STRIDE Methodology:**\\nSTRIDE is a threat modeling framework developed by Microsoft that categorizes threats into six types:\\n\\n**S - Spoofing Identity:**\\n- **Definition**: Impersonating another user or system\\n- **Example**: Using stolen credentials to log in as another user\\n- **Mitigation**: Strong authentication, multi-factor authentication\\n\\n**T - Tampering with Data:**\\n- **Definition**: Unauthorized modification of data\\n- **Example**: Altering transaction amounts in transit\\n- **Mitigation**: Digital signatures, integrity checks\\n\\n**R - Repudiation:**\\n- **Definition**: Denying actions or transactions\\n- **Example**: User claims they didn't make a purchase\\n- **Mitigation**: Audit logs, digital signatures, timestamps\\n\\n**I - Information Disclosure:**\\n- **Definition**: Unauthorized access to sensitive information\\n- **Example**: Database breach exposing customer data\\n- **Mitigation**: Encryption, access controls, data classification\\n\\n**D - Denial of Service:**\\n- **Definition**: Making services unavailable to legitimate users\\n- **Example**: DDoS attacks overwhelming servers\\n- **Mitigation**: Rate limiting, load balancing, redundancy\\n\\n**E - Elevation of Privilege:**\\n- **Definition**: Gaining unauthorized access or higher permissions\\n- **Example**: Token replay attacks, privilege escalation\\n- **Mitigation**: Principle of least privilege, token expiration\\n\\n**Token Replay Attack Analysis:**\\n\\n**Attack Scenario:**\\n1. **Interception**: Attacker captures authentication token\\n2. **Replay**: Attacker reuses token to authenticate\\n3. **Access**: Attacker gains unauthorized system access\\n4. **Impact**: Elevation of privilege achieved\\n\\n**Why This is Elevation of Privilege:**\\n- **Unauthorized Access**: Attacker gains access they shouldn't have\\n- **Permission Escalation**: Using someone else's authentication\\n- **Privilege Abuse**: Leveraging captured credentials for access\\n\\n**Real-World Examples:**\\n\\n**Session Token Replay:**\\n```http\\n# Captured legitimate request\\nGET /account/balance HTTP/1.1\\nHost: bank.com\\nCookie: session_token=abc123xyz789\\nAuthorization: Bearer eyJhbGciOiJIUzI1NiIs...\\n\\n# Attacker replays the same request\\nGET /account/transfer HTTP/1.1\\nHost: bank.com\\nCookie: session_token=abc123xyz789\\nAuthorization: Bearer eyJhbGciOiJIUzI1NiIs...\\n```\\n\\n**Kerberos Ticket Replay:**\\n```bash\\n# Attacker captures Kerberos ticket\\nmimikatz # sekurlsa::tickets /export\\n\\n# Replays ticket for unauthorized access\\nkerberos::ptt ticket.kirbi\\n```\\n\\n**Mitigation Strategies:**\\n\\n**1. Token Expiration:**\\n```javascript\\n// Short-lived tokens\\nconst token = jwt.sign(payload, secret, { expiresIn: '15m' });\\n```\\n\\n**2. Nonce Implementation:**\\n```javascript\\n// One-time use tokens\\nconst nonce = crypto.randomBytes(16).toString('hex');\\nconst token = jwt.sign({ ...payload, nonce }, secret);\\n```\\n\\n**3. Timestamp Validation:**\\n```python\\n# Check token freshness\\nimport time\\n\\ndef validate_token_timestamp(token_time, max_age=300):  # 5 minutes\\n    current_time = time.time()\\n    return (current_time - token_time) <= max_age\\n```\\n\\n**4. IP Binding:**\\n```javascript\\n// Bind token to client IP\\nconst token = jwt.sign({\\n  user_id: user.id,\\n  client_ip: req.ip,\\n  issued_at: Date.now()\\n}, secret);\\n```\\n\\n**Threat Modeling Process:**\\n1. **Identify Assets**: What needs protection?\\n2. **Create Architecture Overview**: System components and data flows\\n3. **Decompose Application**: Break down into smaller components\\n4. **Identify Threats**: Apply STRIDE to each component\\n5. **Document Threats**: Create threat scenarios\\n6. **Rate Threats**: Assess likelihood and impact\\n7. **Plan Mitigations**: Design security controls"}, {"question_id": "attack_tree_analysis_2024", "type": "short_answer", "text": "You're creating an attack tree for a web application compromise. The root goal is 'Gain Admin Access'. You identify two main attack paths: 'Exploit SQL Injection' and 'Social Engineering Admin'. What should be the next level of nodes under 'Exploit SQL Injection'?", "points": 4, "difficulty": "advanced", "correct_answers": ["find injection point", "identify vulnerable parameter", "discover sql injection vulnerability", "locate injectable input field", "find vulnerable input", "identify injection vector"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Think about the first step an attacker needs to take to exploit SQL injection.", "delay_seconds": 30}, {"text": "Consider what must be discovered before SQL injection can be exploited.", "delay_seconds": 60}], "feedback_correct": "Correct! Finding the injection point is the first step in SQL injection attacks.", "feedback_incorrect": "The first step in SQL injection is finding/identifying the vulnerable input parameter or injection point.", "explanation": "**Attack Tree Analysis for SQL Injection:**\\n\\n**Attack Tree Structure:**\\n```\\nRoot Goal: Gain Admin Access\\n├── Exploit SQL Injection\\n│   ├── Find Injection Point\\n│   │   ├── Test Input Fields\\n│   │   ├── Analyze URL Parameters\\n│   │   └── Check HTTP Headers\\n│   ├── Identify Database Type\\n│   ├── Extract Database Schema\\n│   ├── Locate Admin Table\\n│   └── Extract Admin Credentials\\n└── Social Engineering Admin\\n    ├── Phishing Attack\\n    ├── Phone Pretexting\\n    └── Physical Access\\n```\\n\\n**Attack Tree Methodology:**\\n\\n**1. Top-Down Decomposition:**\\n- **Root Goal**: Ultimate objective (Gain Admin Access)\\n- **Sub-Goals**: Major attack paths\\n- **Attack Steps**: Specific actions required\\n- **Leaf Nodes**: Atomic attack actions\\n\\n**2. SQL Injection Attack Path Breakdown:**\\n\\n**Level 1: Find Injection Point**\\n```bash\\n# Testing for SQL injection\\n# URL parameters\\nhttps://app.com/search?q=test' OR 1=1--\\n\\n# Form fields\\nusername: admin' OR 1=1--\\npassword: anything\\n\\n# HTTP headers\\nUser-Agent: Mozilla/5.0' OR 1=1--\\n```\\n\\n**Level 2: Identify Database Type**\\n```sql\\n-- MySQL detection\\n' AND @@version--\\n\\n-- PostgreSQL detection\\n' AND version()--\\n\\n-- SQL Server detection\\n' AND @@version--\\n\\n-- Oracle detection\\n' AND banner FROM v$version--\\n```\\n\\n**Level 3: Extract Database Schema**\\n```sql\\n-- MySQL schema extraction\\n' UNION SELECT schema_name FROM information_schema.schemata--\\n\\n-- Table enumeration\\n' UNION SELECT table_name FROM information_schema.tables--\\n\\n-- Column enumeration\\n' UNION SELECT column_name FROM information_schema.columns WHERE table_name='users'--\\n```\\n\\n**Level 4: Locate Admin Table**\\n```sql\\n-- Find admin-related tables\\n' UNION SELECT table_name FROM information_schema.tables WHERE table_name LIKE '%admin%'--\\n\\n-- Find user tables\\n' UNION SELECT table_name FROM information_schema.tables WHERE table_name LIKE '%user%'--\\n```\\n\\n**Level 5: Extract Admin Credentials**\\n```sql\\n-- Extract admin data\\n' UNION SELECT username, password FROM admin_users--\\n\\n-- Extract with privileges\\n' UNION SELECT username, password FROM users WHERE role='admin'--\\n```\\n\\n**3. Attack Tree Analysis Benefits:**\\n\\n**Risk Assessment:**\\n- **Path Probability**: Likelihood of each attack path\\n- **Impact Analysis**: Damage potential of each path\\n- **Cost-Benefit**: Attacker effort vs. reward\\n\\n**Defense Planning:**\\n- **Critical Paths**: High-probability attack routes\\n- **Mitigation Points**: Where to place security controls\\n- **Defense in Depth**: Multiple control layers\\n\\n**4. Real-World Attack Tree Example:**\\n\\n**Target: E-commerce Admin Panel**\\n```\\nGain Admin Access (Root)\\n├── Technical Attacks (OR)\\n│   ├── SQL Injection (AND)\\n│   │   ├── Find Injection Point [P=0.7]\\n│   │   ├── Bypass WAF [P=0.4]\\n│   │   └── Extract Credentials [P=0.8]\\n│   ├── XSS to Session Hijack (AND)\\n│   │   ├── Find XSS Vulnerability [P=0.6]\\n│   │   ├── Craft Payload [P=0.9]\\n│   │   └── Steal Admin Session [P=0.7]\\n│   └── Brute Force (AND)\\n│       ├── Identify Admin Username [P=0.8]\\n│       ├── Bypass Rate Limiting [P=0.3]\\n│       └── Crack Password [P=0.2]\\n└── Social Engineering (OR)\\n    ├── Phishing Admin [P=0.4]\\n    ├── Phone Pretexting [P=0.3]\\n    └── Physical Access [P=0.1]\\n```\\n\\n**5. Attack Tree Calculation:**\\n\\n**AND Gates**: All conditions must be met\\n```\\nP(SQL Injection Success) = P(Find) × P(Bypass) × P(Extract)\\nP(SQL Injection Success) = 0.7 × 0.4 × 0.8 = 0.224 (22.4%)\\n```\\n\\n**OR Gates**: Any condition can succeed\\n```\\nP(Technical Attack) = 1 - (1-P(SQLi)) × (1-P(XSS)) × (1-P(Brute))\\nP(Technical Attack) = 1 - (1-0.224) × (1-0.378) × (1-0.048)\\nP(Technical Attack) = 1 - 0.776 × 0.622 × 0.952 = 0.542 (54.2%)\\n```\\n\\n**6. Mitigation Strategies:**\\n\\n**SQL Injection Prevention:**\\n```python\\n# Parameterized queries\\ncursor.execute(\\\"SELECT * FROM users WHERE username = %s\\\", (username,))\\n\\n# Input validation\\nimport re\\nif not re.match(r'^[a-zA-Z0-9_]+$', username):\\n    raise ValueError('Invalid username')\\n\\n# WAF rules\\n# Block common SQL injection patterns\\n```\\n\\n**Defense in Depth:**\\n- **Input Validation**: Prevent injection at entry\\n- **Parameterized Queries**: Safe database interaction\\n- **Least Privilege**: Limit database permissions\\n- **WAF**: Web application firewall protection\\n- **Monitoring**: Detect attack attempts"}]}}