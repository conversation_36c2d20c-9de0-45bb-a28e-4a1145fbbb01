{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "cloud-security-incidents-2024", "title": "Cloud Security Incidents & Misconfigurations", "description": "Real-world cloud security incidents, misconfigurations, and practical remediation strategies based on recent breaches and security research.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T10:00:00Z", "tags": ["cloud-security", "aws", "azure", "gcp", "misconfigurations", "incidents", "practical"], "passing_score_percentage": 75, "time_limit_minutes": 35, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "s3_bucket_exposure", "type": "multiple_choice", "text": "In 2023, a major data breach occurred when a company's AWS S3 bucket containing 3TB of customer data was exposed. What is the **most common** misconfiguration that leads to such exposures?", "points": 2, "difficulty": "intermediate", "options": [{"id": "opt1", "text": "Using weak IAM passwords", "is_correct": false, "feedback": "While weak passwords are a concern, they don't directly cause S3 bucket exposures."}, {"id": "opt2", "text": "Setting bucket policy to allow public read access (*)", "is_correct": true, "feedback": "Correct! Public read access policies are the leading cause of S3 data exposures."}, {"id": "opt3", "text": "Not enabling S3 encryption", "is_correct": false, "feedback": "Encryption protects data at rest but doesn't prevent unauthorized access."}, {"id": "opt4", "text": "Using default VPC settings", "is_correct": false, "feedback": "VPC settings don't directly affect S3 bucket permissions."}], "hint": [{"text": "Think about what allows anyone on the internet to access S3 objects without authentication.", "delay_seconds": 30}, {"text": "The issue is usually in the bucket policy or ACL settings that grant public access.", "delay_seconds": 60}], "feedback_correct": "Excellent! Public bucket policies are indeed the #1 cause of S3 data breaches.", "feedback_incorrect": "The most common issue is bucket policies that grant public read access to anyone.", "explanation": "**S3 Bucket Exposure Analysis:**\\n\\n**Root Cause - Public Access Policies:**\\n- **Bucket Policy Misconfiguration**: Setting `\"Principal\": \"*\"` with `\"Effect\": \"Allow\"`\\n- **ACL Misconfigurations**: Granting read permissions to 'Everyone' or 'Authenticated Users'\\n- **Block Public Access Disabled**: AWS Block Public Access settings turned off\\n\\n**Real-World Examples:**\\n- **Capital One (2019)**: 100M+ records exposed via misconfigured WAF and S3\\n- **Accenture (2017)**: 137GB of data exposed via public S3 buckets\\n- **Verizon (2017)**: 14M customer records exposed via partner's S3 bucket\\n\\n**Detection Methods:**\\n```bash\\n# Check bucket policy\\naws s3api get-bucket-policy --bucket bucket-name\\n\\n# Check ACLs\\naws s3api get-bucket-acl --bucket bucket-name\\n\\n# Check public access block\\naws s3api get-public-access-block --bucket bucket-name\\n```\\n\\n**Prevention Best Practices:**\\n1. **Enable Block Public Access**: Account and bucket level\\n2. **Principle of Least Privilege**: Grant minimum required permissions\\n3. **Regular Audits**: Use AWS Config, CloudTrail, and third-party tools\\n4. **Bucket Policies**: Avoid wildcard principals unless absolutely necessary\\n5. **Monitoring**: Set up CloudWatch alerts for policy changes", "single_correct_answer": true}, {"question_id": "azure_vm_compromise", "type": "short_answer", "text": "A penetration tester discovers an Azure VM with RDP exposed to the internet (0.0.0.0/0) and successfully brute-forces the administrator password. What Azure CLI command would they use to **list all storage accounts** accessible from this compromised VM?", "points": 2, "difficulty": "intermediate", "correct_answers": ["az storage account list", "az storage account list --output table", "az storage account list -o table", "az storage account list --query", "az storage account show"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "The Azure CLI command structure is 'az [service] [resource] [action]'. What service manages storage?", "delay_seconds": 30}, {"text": "Think about the Azure CLI command to list storage accounts: az storage account [action]", "delay_seconds": 60}], "feedback_correct": "Correct! This command would reveal all storage accounts the compromised VM has access to.", "feedback_incorrect": "The correct command is 'az storage account list' to enumerate accessible storage accounts.", "explanation": "**Azure VM Compromise - Post-Exploitation:**\\n\\n**Attack Chain:**\\n1. **Discovery**: Shodan/Masscan reveals RDP on port 3389\\n2. **Brute Force**: Tools like Hydra/RDPBrute against weak passwords\\n3. **Access**: Successful login with admin credentials\\n4. **Enumeration**: Azure CLI commands to discover resources\\n\\n**Key Azure CLI Commands for Attackers:**\\n```bash\\n# List storage accounts\\naz storage account list\\n\\n# List all resources\\naz resource list\\n\\n# Get current user context\\naz account show\\n\\n# List role assignments\\naz role assignment list\\n\\n# List key vaults\\naz keyvault list\\n\\n# List VMs\\naz vm list\\n```\\n\\n**Real-World Impact:**\\n- **Tesla (2018)**: Kubernetes cluster exposed, led to AWS compromise\\n- **Uber (2022)**: Social engineering + MFA fatigue led to cloud access\\n- **Lapsus$ Group**: Multiple cloud compromises via initial access\\n\\n**Prevention Measures:**\\n1. **Network Security**: Never expose RDP/SSH to 0.0.0.0/0\\n2. **Strong Authentication**: Complex passwords + MFA\\n3. **Just-in-Time Access**: Azure JIT VM access\\n4. **Monitoring**: Enable Azure Security Center alerts\\n5. **Principle of Least Privilege**: Minimal VM permissions"}]}}