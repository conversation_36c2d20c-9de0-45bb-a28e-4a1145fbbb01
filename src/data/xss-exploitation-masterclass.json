{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "xss-exploitation-masterclass", "title": "XSS Exploitation Masterclass - Advanced Techniques", "description": "Advanced Cross-Site Scripting (XSS) exploitation techniques, filter bypass methods, and real-world attack scenarios used in modern web applications.", "author": "QuizFlow Security Team", "creation_date": "2024-01-26T00:00:00Z", "tags": ["xss", "web-security", "exploitation", "filter-bypass", "javascript"], "passing_score_percentage": 85, "time_limit_minutes": 50, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "dom_xss_modern_frameworks_2023", "type": "multiple_choice", "text": "In modern single-page applications (SPAs), DOM-based XSS vulnerabilities are increasingly common. Which JavaScript framework feature is **most commonly** exploited for DOM XSS attacks?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "innerHTML property with unsanitized user input", "is_correct": true, "feedback": "Correct! innerHTML with unsanitized input is the most common DOM XSS vector in modern frameworks."}, {"id": "opt2", "text": "AJAX requests with improper CORS configuration", "is_correct": false, "feedback": "CORS misconfigurations can be problematic but don't directly cause DOM XSS."}, {"id": "opt3", "text": "WebSocket connections without proper validation", "is_correct": false, "feedback": "WebSocket issues can lead to vulnerabilities but aren't the primary DOM XSS vector."}, {"id": "opt4", "text": "Service Worker registration with malicious scripts", "is_correct": false, "feedback": "Service Worker attacks are possible but less common than innerHTML-based DOM XSS."}], "hint": [{"text": "Think about how modern frameworks dynamically update the DOM with user-controlled data.", "delay_seconds": 30}, {"text": "Consider which JavaScript property directly inserts HTML content into the page.", "delay_seconds": 60}], "feedback_correct": "Excellent! innerHTML with unsanitized input is the primary DOM XSS attack vector.", "feedback_incorrect": "The most common DOM XSS vector is innerHTML property with unsanitized user input.", "explanation": "**DOM XSS in Modern Frameworks:**\\n\\n**Vulnerability Mechanism:**\\nDOM XSS occurs when client-side JavaScript processes user input and dynamically updates the DOM without proper sanitization.\\n\\n**Common Vulnerable Patterns:**\\n\\n**1. innerHTML with User Input:**\\n```javascript\\n// VULNERABLE: Direct innerHTML assignment\\nfunction displayMessage(userInput) {\\n    document.getElementById('output').innerHTML = userInput;\\n}\\n\\n// Attack payload\\ndisplayMessage('<img src=x onerror=alert(document.cookie)>');\\n```\\n\\n**2. React dangerouslySetInnerHTML:**\\n```jsx\\n// VULNERABLE: React component\\nfunction UserContent({ userInput }) {\\n    return (\\n        <div dangerouslySetInnerHTML={{__html: userInput}} />\\n    );\\n}\\n\\n// Attack\\n<UserContent userInput=\\\"<script>alert('XSS')</script>\\\" />\\n```\\n\\n**3. Vue.js v-html Directive:**\\n```vue\\n<!-- VULNERABLE: Vue template -->\\n<template>\\n    <div v-html=\\\"userContent\\\"></div>\\n</template>\\n\\n<!-- Attack -->\\n<script>\\nexport default {\\n    data() {\\n        return {\\n            userContent: '<img src=x onerror=alert(1)>'\\n        }\\n    }\\n}\\n</script>\\n```\\n\\n**4. Angular innerHTML Binding:**\\n```typescript\\n// VULNERABLE: Angular component\\n@Component({\\n    template: '<div [innerHTML]=\\\"userInput\\\"></div>'\\n})\\nexport class UserComponent {\\n    userInput: string = '<script>alert(\\\"XSS\\\")</script>';\\n}\\n```\\n\\n**Advanced DOM XSS Vectors:**\\n\\n**1. URL Fragment Exploitation:**\\n```javascript\\n// Vulnerable code\\nfunction loadContent() {\\n    const fragment = location.hash.substring(1);\\n    document.getElementById('content').innerHTML = fragment;\\n}\\n\\n// Attack URL\\n// https://example.com#<img src=x onerror=alert(1)>\\n```\\n\\n**2. PostMessage Exploitation:**\\n```javascript\\n// Vulnerable message handler\\nwindow.addEventListener('message', function(event) {\\n    document.getElementById('output').innerHTML = event.data;\\n});\\n\\n// Attack from malicious iframe\\nparent.postMessage('<script>alert(1)</script>', '*');\\n```\\n\\n**Prevention Strategies:**\\n\\n**1. Use Safe DOM Methods:**\\n```javascript\\n// SAFE: Use textContent instead of innerHTML\\ndocument.getElementById('output').textContent = userInput;\\n\\n// SAFE: Create elements programmatically\\nconst div = document.createElement('div');\\ndiv.textContent = userInput;\\ndocument.body.appendChild(div);\\n```\\n\\n**2. Implement Content Security Policy:**\\n```html\\n<!-- Prevent inline scripts -->\\n<meta http-equiv=\\\"Content-Security-Policy\\\" \\n      content=\\\"script-src 'self'; object-src 'none';\\\">\\n```\\n\\n**3. Use Framework Security Features:**\\n```jsx\\n// React: Automatic escaping\\nfunction SafeComponent({ userInput }) {\\n    return <div>{userInput}</div>; // Automatically escaped\\n}\\n\\n// Vue: Safe text interpolation\\n<template>\\n    <div>{{ userInput }}</div> <!-- Automatically escaped -->\\n</template>\\n```\\n\\n**4. Input Sanitization:**\\n```javascript\\n// Use DOMPurify for HTML sanitization\\nconst clean = DOMPurify.sanitize(userInput);\\ndocument.getElementById('output').innerHTML = clean;\\n```"}]}}