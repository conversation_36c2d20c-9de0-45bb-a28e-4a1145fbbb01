{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "code-security-analysis", "title": "Code Security Analysis", "description": "Analyze code snippets to identify security vulnerabilities and best practices in various programming languages.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T10:00:00Z", "tags": ["code-analysis", "security", "vulnerabilities", "programming"], "passing_score_percentage": 70, "time_limit_minutes": 25, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "sql_injection_1", "type": "short_answer", "text": "What security vulnerability does this Python code contain?", "points": 3, "difficulty": "intermediate", "code_snippet": {"language": "python", "code": "import sqlite3\n\ndef get_user(username):\n    conn = sqlite3.connect('users.db')\n    cursor = conn.cursor()\n    query = f\"SELECT * FROM users WHERE username = '{username}'\"\n    cursor.execute(query)\n    return cursor.fetchone()", "filename": "user_auth.py", "highlight_lines": [6], "caption": "User authentication function"}, "correct_answers": ["SQL injection", "SQLi", "SQL injection vulnerability"], "case_sensitive": false, "explanation": "This code is vulnerable to SQL injection because it uses string formatting to build the SQL query directly from user input without proper sanitization or parameterized queries."}, {"question_id": "xss_prevention", "type": "multiple_choice", "text": "Which of the following JavaScript code snippets properly prevents XSS attacks?", "points": 2, "difficulty": "intermediate", "single_correct_answer": true, "options": [{"id": "a", "text": "document.innerHTML = userInput;", "is_correct": false, "feedback": "This directly inserts user input into the DOM without sanitization, making it vulnerable to XSS."}, {"id": "b", "text": "document.textContent = userInput;", "is_correct": true, "feedback": "Correct! textContent automatically escapes HTML, preventing XSS attacks."}, {"id": "c", "text": "$(element).html(userInput);", "is_correct": false, "feedback": "jQuery's html() method also directly inserts HTML without sanitization."}, {"id": "d", "text": "element.outerHTML = userInput;", "is_correct": false, "feedback": "outerHTML also allows direct HTML injection without sanitization."}], "explanation": "Using textContent instead of innerHTML prevents XSS by treating user input as plain text rather than HTML."}, {"question_id": "password_hashing", "type": "true_false", "text": "Is this password hashing implementation secure?", "points": 2, "difficulty": "beginner", "code_snippet": {"language": "javascript", "code": "const crypto = require('crypto');\n\nfunction hashPassword(password) {\n    const hash = crypto.createHash('md5');\n    hash.update(password);\n    return hash.digest('hex');\n}", "filename": "auth.js", "highlight_lines": [4], "caption": "Password hashing function"}, "correct_answer": false, "explanation": "No, this implementation is not secure. MD5 is cryptographically broken and should not be used for password hashing. Use bcrypt, scrypt, or Argon2 instead."}, {"question_id": "command_injection", "type": "short_answer", "text": "What type of vulnerability does this PHP code demonstrate?", "points": 3, "difficulty": "advanced", "code_snippet": {"language": "php", "code": "<?php\n$filename = $_GET['file'];\n$command = \"cat /var/logs/\" . $filename;\necho shell_exec($command);\n?>", "filename": "log_viewer.php", "highlight_lines": [2, 3, 4], "caption": "Log file viewer script"}, "correct_answers": ["Command injection", "OS command injection", "Shell injection"], "case_sensitive": false, "explanation": "This code is vulnerable to command injection because it directly concatenates user input into a shell command without validation or sanitization."}, {"question_id": "secure_random", "type": "multiple_choice", "text": "Which method should be used for generating cryptographically secure random numbers in Python?", "points": 2, "difficulty": "intermediate", "single_correct_answer": true, "code_snippet": {"language": "python", "code": "# Option A\nimport random\ntoken = random.randint(1000, 9999)\n\n# Option B\nimport secrets\ntoken = secrets.randbelow(9000) + 1000\n\n# Option C\nimport time\ntoken = int(time.time()) % 10000\n\n# Option D\nimport os\ntoken = int.from_bytes(os.urandom(2), 'big') % 9000 + 1000", "caption": "Different approaches to generating random tokens"}, "options": [{"id": "a", "text": "Option A: random.randint()", "is_correct": false, "feedback": "The random module is not cryptographically secure and should not be used for security purposes."}, {"id": "b", "text": "Option B: secrets.randbelow()", "is_correct": true, "feedback": "Correct! The secrets module is designed for cryptographically secure random number generation."}, {"id": "c", "text": "Option C: time.time()", "is_correct": false, "feedback": "Using timestamps is predictable and not secure for generating tokens."}, {"id": "d", "text": "Option D: os.urandom()", "is_correct": false, "feedback": "While os.urandom() is secure, the secrets module is the preferred high-level interface."}], "explanation": "The secrets module was specifically designed for generating cryptographically strong random numbers suitable for security purposes."}, {"question_id": "path_traversal", "type": "short_answer", "text": "What security issue does this file reading code have?", "points": 3, "difficulty": "intermediate", "code_snippet": {"language": "java", "code": "public String readFile(String filename) {\n    try {\n        Path path = Paths.get(\"/app/files/\" + filename);\n        return Files.readString(path);\n    } catch (IOException e) {\n        return \"Error reading file\";\n    }\n}", "filename": "FileReader.java", "highlight_lines": [3], "caption": "File reading utility method"}, "correct_answers": ["Path traversal", "Directory traversal", "Path traversal vulnerability"], "case_sensitive": false, "explanation": "This code is vulnerable to path traversal attacks. An attacker could use '../' sequences to access files outside the intended directory."}]}}