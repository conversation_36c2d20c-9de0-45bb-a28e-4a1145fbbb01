{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "advanced-malware-analysis-reverse-engineering-2024", "title": "Advanced Malware Analysis & Reverse Engineering", "description": "Comprehensive malware analysis techniques covering static analysis, dynamic analysis, reverse engineering, and evasion detection using industry-standard tools.", "author": "QuizFlow Security Team", "creation_date": "2024-01-25T17:00:00Z", "tags": ["malware-analysis", "reverse-engineering", "static-analysis", "dynamic-analysis", "ida-pro", "g<PERSON><PERSON>", "wireshark"], "passing_score_percentage": 85, "time_limit_minutes": 60, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "ida_pro_disassembly", "type": "multiple_choice", "text": "During malware analysis in IDA Pro, you encounter this assembly instruction: `mov eax, dword ptr [ebp+8]`. What is this instruction doing?", "points": 2, "difficulty": "advanced", "options": [{"id": "opt1", "text": "Moving the value 8 into the EAX register", "is_correct": false, "feedback": "This would be 'mov eax, 8' - the instruction is accessing memory."}, {"id": "opt2", "text": "Moving the value at memory address [EBP+8] into EAX register", "is_correct": true, "feedback": "Correct! This loads a 32-bit value from the stack frame into EAX."}, {"id": "opt3", "text": "Moving EAX register value to memory address [EBP+8]", "is_correct": false, "feedback": "This would be 'mov dword ptr [ebp+8], eax' - the direction is reversed."}, {"id": "opt4", "text": "Adding 8 to the EBP register and storing in EAX", "is_correct": false, "feedback": "This would be 'lea eax, [ebp+8]' or 'add eax, ebp, 8'."}], "hint": [{"text": "Focus on the direction of the MOV instruction and what the square brackets mean.", "delay_seconds": 30}, {"text": "Square brackets indicate memory dereferencing - reading from memory at that address.", "delay_seconds": 60}], "feedback_correct": "Excellent! Understanding x86 assembly is crucial for malware analysis.", "feedback_incorrect": "The instruction reads a 32-bit value from memory location [EBP+8] into EAX.", "explanation": "**x86 Assembly in Malware Analysis:**\\n\\n**Instruction Breakdown:**\\n- **mov**: Move/copy operation\\n- **eax**: 32-bit destination register\\n- **dword ptr**: 32-bit (double word) memory access\\n- **[ebp+8]**: Memory address calculation (base pointer + offset)\\n\\n**Stack Frame Context:**\\n```\\nEBP+12: Parameter 2\\nEBP+8:  Parameter 1  <- This instruction\\nEBP+4:  Return Address\\nEBP:    Saved EBP\\nEBP-4:  Local Variable 1\\n```\\n\\n**Common Malware Patterns:**\\n1. **API Resolution**: Dynamic loading of Windows APIs\\n2. **String Obfuscation**: XOR encoding, stack strings\\n3. **Anti-Analysis**: Debugger detection, VM evasion\\n4. **Persistence**: Registry modification, service installation\\n\\n**IDA Pro Analysis Techniques:**\\n- **Cross-references**: Find all uses of functions/data\\n- **Function signatures**: Identify library functions\\n- **String analysis**: Locate embedded strings\\n- **Control flow**: Understand program logic\\n- **Debugging**: Dynamic analysis with debugger", "single_correct_answer": true}, {"question_id": "dynamic_analysis_sandbox", "type": "short_answer", "text": "You're analyzing a suspicious executable in a sandbox environment. What Windows API function would malware typically call to **create a new process** for process injection or lateral movement?", "points": 2, "difficulty": "intermediate", "correct_answers": ["CreateProcess", "CreateProcessA", "CreateProcessW", "NtCreateProcess", "ZwCreateProcess"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Think about the primary Windows API for spawning new processes.", "delay_seconds": 30}, {"text": "The function name starts with 'Create' and is used to launch executables.", "delay_seconds": 60}], "feedback_correct": "Correct! This is the primary API for process creation in Windows.", "feedback_incorrect": "The main Windows API for creating processes is CreateProcess (or its variants).", "explanation": "**Process Creation APIs in Malware:**\\n\\n**Primary APIs:**\\n- **CreateProcess/CreateProcessA/W**: Standard process creation\\n- **CreateProcessAsUser**: Create process with specific user token\\n- **CreateProcessWithToken**: Create process with impersonation token\\n- **NtCreateProcess/ZwCreateProcess**: Native API process creation\\n\\n**Malware Usage Patterns:**\\n```c\\n// Typical malware process creation\\nCREATEPROCESS(\\n    NULL,                    // Application name\\n    \"cmd.exe /c malicious\",  // Command line\\n    NULL,                    // Process security\\n    NULL,                    // Thread security\\n    FALSE,                   // Inherit handles\\n    CREATE_SUSPENDED,        // Creation flags\\n    NULL,                    // Environment\\n    NULL,                    // Current directory\\n    &si,                     // Startup info\\n    &pi                      // Process info\\n);\\n```\\n\\n**Detection Indicators:**\\n1. **Suspicious Command Lines**: PowerShell, cmd.exe with encoded commands\\n2. **Process Injection**: CREATE_SUSPENDED flag for hollowing\\n3. **Privilege Escalation**: CreateProcessAsUser calls\\n4. **Persistence**: Creating services or scheduled tasks\\n\\n**Analysis Tools:**\\n- **Process Monitor**: Monitor process creation\\n- **API Monitor**: Hook and log API calls\\n- **Wireshark**: Network activity from spawned processes\\n- **Volatility**: Memory analysis of process relationships"}]}}