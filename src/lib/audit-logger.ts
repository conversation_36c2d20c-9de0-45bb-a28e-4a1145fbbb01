/**
 * Comprehensive Audit Logging System
 * 
 * Provides detailed logging for security events, user actions,
 * and system activities for monitoring and compliance.
 */

import { db } from '@/lib/db';
import { NextRequest } from 'next/server';

export enum AuditAction {
  // Authentication
  LOGIN_SUCCESS = 'LOGI<PERSON>_SUCCESS',
  LOGIN_FAILED = 'LOGIN_FAILED',
  LOGOUT = 'LOGOUT',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  
  // Quiz Actions
  QUIZ_STARTED = 'QUIZ_STARTED',
  QUIZ_SUBMITTED = 'QUIZ_SUBMITTED',
  QUIZ_COMPLETED = 'QUIZ_COMPLETED',
  QUIZ_ABANDONED = 'QUIZ_ABANDONED',
  
  // Admin Actions
  QUIZ_CREATED = 'QUIZ_CREATED',
  QUIZ_UPDATED = 'QUIZ_UPDATED',
  QUIZ_DELETED = 'QUIZ_DELETED',
  QUIZ_PUBLISHED = 'QUIZ_PUBLISHED',
  QUIZ_UNPUBLISHED = 'QUIZ_UNPUBLISHED',
  
  // Security Events
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  CSRF_VIOLATION = 'CSRF_VIOLATION',
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
  
  // System Events
  API_REQUEST = 'API_REQUEST',
  ERROR_OCCURRED = 'ERROR_OCCURRED',
  SYSTEM_MAINTENANCE = 'SYSTEM_MAINTENANCE'
}

export enum AuditSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

interface AuditLogEntry {
  userId?: string;
  action: AuditAction;
  resource: string;
  severity: AuditSeverity;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  timestamp?: Date;
}

interface SecurityEvent {
  type: string;
  description: string;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  indicators: string[];
  recommendedAction?: string;
}

class AuditLogger {
  private static instance: AuditLogger;
  
  private constructor() {}
  
  static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger();
    }
    return AuditLogger.instance;
  }

  /**
   * Log an audit event
   */
  async log(entry: AuditLogEntry): Promise<void> {
    try {
      await db.auditLog.create({
        data: {
          userId: entry.userId || null,
          action: entry.action,
          resource: entry.resource,
          metadata: entry.metadata ? JSON.stringify({
            ...entry.metadata,
            severity: entry.severity,
            timestamp: entry.timestamp || new Date()
          }) : JSON.stringify({ severity: entry.severity }),
          timestamp: entry.timestamp || new Date(),
          ipAddress: entry.ipAddress || null,
          userAgent: entry.userAgent || null
        }
      });

      // Log to console for development
      if (process.env.NODE_ENV === 'development') {
        console.log(`[AUDIT] ${entry.action}: ${entry.resource}`, {
          userId: entry.userId,
          severity: entry.severity,
          metadata: entry.metadata
        });
      }

      // Alert on critical events
      if (entry.severity === AuditSeverity.CRITICAL) {
        await this.handleCriticalEvent(entry);
      }

    } catch (error) {
      console.error('Failed to log audit event:', error);
      // Don't throw - audit logging should not break the application
    }
  }

  /**
   * Log quiz-related events
   */
  async logQuizEvent(
    action: AuditAction,
    userId: string,
    quizId: string,
    metadata?: Record<string, any>,
    req?: NextRequest
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource: `quiz:${quizId}`,
      severity: this.getSeverityForAction(action),
      metadata: {
        quizId,
        ...metadata
      },
      ipAddress: req ? this.getClientIP(req) : undefined,
      userAgent: req ? req.headers.get('user-agent') || undefined : undefined
    });
  }

  /**
   * Log security events
   */
  async logSecurityEvent(
    event: SecurityEvent,
    userId?: string,
    resource?: string,
    req?: NextRequest
  ): Promise<void> {
    await this.log({
      userId,
      action: AuditAction.SUSPICIOUS_ACTIVITY,
      resource: resource || 'system',
      severity: this.mapRiskToSeverity(event.riskLevel),
      metadata: {
        securityEvent: event,
        indicators: event.indicators,
        recommendedAction: event.recommendedAction
      },
      ipAddress: req ? this.getClientIP(req) : undefined,
      userAgent: req ? req.headers.get('user-agent') || undefined : undefined
    });
  }

  /**
   * Log authentication events
   */
  async logAuthEvent(
    action: AuditAction,
    userId?: string,
    success: boolean = true,
    metadata?: Record<string, any>,
    req?: NextRequest
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource: `auth:${userId || 'unknown'}`,
      severity: success ? AuditSeverity.LOW : AuditSeverity.MEDIUM,
      metadata: {
        success,
        ...metadata
      },
      ipAddress: req ? this.getClientIP(req) : undefined,
      userAgent: req ? req.headers.get('user-agent') || undefined : undefined
    });
  }

  /**
   * Log admin actions
   */
  async logAdminAction(
    action: AuditAction,
    userId: string,
    resource: string,
    metadata?: Record<string, any>,
    req?: NextRequest
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource,
      severity: AuditSeverity.HIGH, // Admin actions are always high severity
      metadata: {
        adminAction: true,
        ...metadata
      },
      ipAddress: req ? this.getClientIP(req) : undefined,
      userAgent: req ? req.headers.get('user-agent') || undefined : undefined
    });
  }

  /**
   * Get audit logs with filtering
   */
  async getAuditLogs(filters: {
    userId?: string;
    action?: AuditAction;
    resource?: string;
    severity?: AuditSeverity;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  }) {
    const where: any = {};
    
    if (filters.userId) where.userId = filters.userId;
    if (filters.action) where.action = filters.action;
    if (filters.resource) where.resource = { contains: filters.resource };
    
    if (filters.startDate || filters.endDate) {
      where.timestamp = {};
      if (filters.startDate) where.timestamp.gte = filters.startDate;
      if (filters.endDate) where.timestamp.lte = filters.endDate;
    }

    return await db.auditLog.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      take: filters.limit || 100,
      skip: filters.offset || 0,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            role: true
          }
        }
      }
    });
  }

  /**
   * Detect suspicious patterns
   */
  async detectSuspiciousActivity(userId: string): Promise<SecurityEvent[]> {
    const events: SecurityEvent[] = [];
    const now = new Date();
    const oneHour = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDay = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // Check for rapid quiz submissions
    const recentSubmissions = await db.auditLog.count({
      where: {
        userId,
        action: AuditAction.QUIZ_SUBMITTED,
        timestamp: { gte: oneHour }
      }
    });

    if (recentSubmissions > 10) {
      events.push({
        type: 'RAPID_QUIZ_SUBMISSIONS',
        description: `User submitted ${recentSubmissions} quizzes in the last hour`,
        riskLevel: 'HIGH',
        indicators: ['rapid_submissions', 'potential_automation'],
        recommendedAction: 'Temporarily restrict quiz access'
      });
    }

    // Check for failed login attempts
    const failedLogins = await db.auditLog.count({
      where: {
        userId,
        action: AuditAction.LOGIN_FAILED,
        timestamp: { gte: oneDay }
      }
    });

    if (failedLogins > 5) {
      events.push({
        type: 'MULTIPLE_FAILED_LOGINS',
        description: `${failedLogins} failed login attempts in the last 24 hours`,
        riskLevel: 'MEDIUM',
        indicators: ['brute_force_attempt', 'credential_stuffing'],
        recommendedAction: 'Implement account lockout'
      });
    }

    return events;
  }

  private getSeverityForAction(action: AuditAction): AuditSeverity {
    switch (action) {
      case AuditAction.LOGIN_FAILED:
      case AuditAction.UNAUTHORIZED_ACCESS:
      case AuditAction.CSRF_VIOLATION:
        return AuditSeverity.HIGH;
      
      case AuditAction.SUSPICIOUS_ACTIVITY:
      case AuditAction.RATE_LIMIT_EXCEEDED:
        return AuditSeverity.MEDIUM;
      
      case AuditAction.QUIZ_CREATED:
      case AuditAction.QUIZ_DELETED:
      case AuditAction.PASSWORD_CHANGE:
        return AuditSeverity.MEDIUM;
      
      default:
        return AuditSeverity.LOW;
    }
  }

  private mapRiskToSeverity(risk: string): AuditSeverity {
    switch (risk) {
      case 'CRITICAL': return AuditSeverity.CRITICAL;
      case 'HIGH': return AuditSeverity.HIGH;
      case 'MEDIUM': return AuditSeverity.MEDIUM;
      default: return AuditSeverity.LOW;
    }
  }

  private getClientIP(req: NextRequest): string {
    return req.headers.get('x-forwarded-for') || 
           req.headers.get('x-real-ip') || 
           req.headers.get('cf-connecting-ip') || 
           'unknown';
  }

  private async handleCriticalEvent(entry: AuditLogEntry): Promise<void> {
    // In production, this would send alerts to security team
    console.error('CRITICAL SECURITY EVENT:', {
      action: entry.action,
      resource: entry.resource,
      userId: entry.userId,
      metadata: entry.metadata
    });
    
    // Could integrate with:
    // - Email alerts
    // - Slack notifications
    // - Security incident management systems
    // - Automated response systems
  }
}

export const auditLogger = AuditLogger.getInstance();

// Helper functions for common audit events
export async function logQuizSubmission(
  userId: string,
  quizId: string,
  score: number,
  maxScore: number,
  timeSpent: number,
  req?: NextRequest
): Promise<void> {
  await auditLogger.logQuizEvent(
    AuditAction.QUIZ_SUBMITTED,
    userId,
    quizId,
    { score, maxScore, timeSpent, percentage: Math.round((score / maxScore) * 100) },
    req
  );
}

export async function logSuspiciousQuizActivity(
  userId: string,
  quizId: string,
  indicators: string[],
  req?: NextRequest
): Promise<void> {
  await auditLogger.logSecurityEvent(
    {
      type: 'SUSPICIOUS_QUIZ_ACTIVITY',
      description: 'Unusual patterns detected during quiz submission',
      riskLevel: 'MEDIUM',
      indicators,
      recommendedAction: 'Review quiz attempt for potential cheating'
    },
    userId,
    `quiz:${quizId}`,
    req
  );
}
