/**
 * CSRF Protection Utility
 * 
 * Provides Cross-Site Request Forgery protection for sensitive operations
 * like quiz submissions and admin actions.
 */

import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import crypto from 'crypto';

interface CSRFTokenData {
  token: string;
  userId: string;
  timestamp: number;
  action?: string;
}

class CSRFProtection {
  private static instance: CSRFProtection;
  private secret: string;
  
  private constructor() {
    // Use environment variable or generate a secret
    this.secret = process.env.CSRF_SECRET || crypto.randomBytes(32).toString('hex');
  }
  
  static getInstance(): CSRFProtection {
    if (!CSRFProtection.instance) {
      CSRFProtection.instance = new CSRFProtection();
    }
    return CSRFProtection.instance;
  }

  /**
   * Generate a CSRF token for a user
   */
  generateToken(userId: string, action?: string): string {
    const timestamp = Date.now();
    const data: CSRFTokenData = {
      token: crypto.randomBytes(16).toString('hex'),
      userId,
      timestamp,
      action
    };
    
    const payload = Buffer.from(JSON.stringify(data)).toString('base64');
    const signature = crypto
      .createHmac('sha256', this.secret)
      .update(payload)
      .digest('hex');
    
    return `${payload}.${signature}`;
  }

  /**
   * Verify a CSRF token
   */
  verifyToken(token: string, userId: string, action?: string): boolean {
    try {
      const [payload, signature] = token.split('.');
      if (!payload || !signature) return false;
      
      // Verify signature
      const expectedSignature = crypto
        .createHmac('sha256', this.secret)
        .update(payload)
        .digest('hex');
      
      if (signature !== expectedSignature) return false;
      
      // Decode and validate payload
      const data: CSRFTokenData = JSON.parse(Buffer.from(payload, 'base64').toString());
      
      // Check user ID
      if (data.userId !== userId) return false;
      
      // Check action if specified
      if (action && data.action && data.action !== action) return false;
      
      // Check token age (valid for 1 hour)
      const tokenAge = Date.now() - data.timestamp;
      if (tokenAge > 60 * 60 * 1000) return false;
      
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Validate CSRF token from request
   */
  async validateRequest(req: NextRequest, action?: string): Promise<{
    valid: boolean;
    error?: string;
    userId?: string;
  }> {
    try {
      // Get session
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return { valid: false, error: 'No valid session' };
      }

      // Get CSRF token from header or body
      let csrfToken = req.headers.get('x-csrf-token');
      
      if (!csrfToken) {
        // Try to get from request body
        try {
          const body = await req.json();
          csrfToken = body.csrfToken;
        } catch {
          // Body might not be JSON or already consumed
        }
      }

      if (!csrfToken) {
        return { valid: false, error: 'CSRF token missing' };
      }

      // Verify token
      const isValid = this.verifyToken(csrfToken, session.user.id, action);
      
      if (!isValid) {
        return { valid: false, error: 'Invalid CSRF token' };
      }

      return { valid: true, userId: session.user.id };
    } catch (error) {
      return { valid: false, error: 'CSRF validation failed' };
    }
  }

  /**
   * Check if request origin is allowed
   */
  validateOrigin(req: NextRequest): boolean {
    const origin = req.headers.get('origin');
    const referer = req.headers.get('referer');
    const host = req.headers.get('host');
    
    // Allow same-origin requests
    if (origin && host) {
      const originHost = new URL(origin).host;
      return originHost === host;
    }
    
    // Check referer as fallback
    if (referer && host) {
      const refererHost = new URL(referer).host;
      return refererHost === host;
    }
    
    // For development, allow localhost
    if (process.env.NODE_ENV === 'development') {
      return true;
    }
    
    return false;
  }

  /**
   * Comprehensive CSRF protection check
   */
  async protectRequest(req: NextRequest, action?: string): Promise<{
    protected: boolean;
    error?: string;
    userId?: string;
  }> {
    // Check origin first
    if (!this.validateOrigin(req)) {
      return { protected: false, error: 'Invalid origin' };
    }

    // Validate CSRF token
    const tokenValidation = await this.validateRequest(req, action);
    
    return {
      protected: tokenValidation.valid,
      error: tokenValidation.error,
      userId: tokenValidation.userId
    };
  }
}

export const csrfProtection = CSRFProtection.getInstance();

// Helper functions
export async function generateCSRFToken(userId: string, action?: string): Promise<string> {
  return csrfProtection.generateToken(userId, action);
}

export async function validateCSRF(req: NextRequest, action?: string): Promise<void> {
  const result = await csrfProtection.protectRequest(req, action);
  
  if (!result.protected) {
    throw new Error(`CSRF protection failed: ${result.error}`);
  }
}

export async function getCSRFTokenForUser(userId: string, action?: string): Promise<string> {
  return csrfProtection.generateToken(userId, action);
}

// Middleware helper for CSRF protection
export function createCSRFMiddleware(action?: string) {
  return async (req: NextRequest) => {
    const result = await csrfProtection.protectRequest(req, action);
    
    if (!result.protected) {
      return new Response(
        JSON.stringify({ error: `CSRF protection failed: ${result.error}` }),
        { 
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    return null; // Continue processing
  };
}

// API endpoint to get CSRF token
export async function getCSRFTokenEndpoint(req: NextRequest): Promise<Response> {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const token = await generateCSRFToken(session.user.id);
    
    return new Response(
      JSON.stringify({ csrfToken: token }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: 'Failed to generate CSRF token' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
