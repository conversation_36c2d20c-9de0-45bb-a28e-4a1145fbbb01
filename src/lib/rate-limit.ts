/**
 * Rate Limiting Utility
 * 
 * Provides rate limiting functionality for quiz attempts and other actions
 * to prevent abuse and ensure fair usage.
 */

import { db } from '@/lib/db';

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxAttempts: number; // Maximum attempts in the window
  blockDurationMs?: number; // How long to block after limit exceeded
}

interface RateLimitResult {
  success: boolean;
  remaining: number;
  resetTime: Date;
  blocked: boolean;
  blockUntil?: Date;
}

class RateLimiter {
  private static instance: RateLimiter;
  
  private constructor() {}
  
  static getInstance(): RateLimiter {
    if (!RateLimiter.instance) {
      RateLimiter.instance = new RateLimiter();
    }
    return RateLimiter.instance;
  }

  /**
   * Check if an action is rate limited
   */
  async checkRateLimit(
    userId: string,
    action: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const now = new Date();
    const windowStart = new Date(now.getTime() - config.windowMs);
    
    try {
      // Count recent attempts
      const recentAttempts = await db.auditLog.count({
        where: {
          userId,
          action,
          timestamp: {
            gte: windowStart
          }
        }
      });

      // Check if user is currently blocked
      const blockRecord = await db.auditLog.findFirst({
        where: {
          userId,
          action: `${action}_BLOCKED`,
          timestamp: {
            gte: new Date(now.getTime() - (config.blockDurationMs || 3600000)) // Default 1 hour
          }
        },
        orderBy: {
          timestamp: 'desc'
        }
      });

      if (blockRecord && config.blockDurationMs) {
        const blockUntil = new Date(blockRecord.timestamp.getTime() + config.blockDurationMs);
        if (now < blockUntil) {
          return {
            success: false,
            remaining: 0,
            resetTime: new Date(windowStart.getTime() + config.windowMs),
            blocked: true,
            blockUntil
          };
        }
      }

      const remaining = Math.max(0, config.maxAttempts - recentAttempts);
      const success = recentAttempts < config.maxAttempts;

      // If limit exceeded and blocking is enabled, create block record
      if (!success && config.blockDurationMs) {
        await db.auditLog.create({
          data: {
            userId,
            action: `${action}_BLOCKED`,
            resource: `rate_limit:${action}`,
            metadata: JSON.stringify({
              attempts: recentAttempts,
              limit: config.maxAttempts,
              windowMs: config.windowMs,
              blockDurationMs: config.blockDurationMs
            }),
            timestamp: now
          }
        });
      }

      return {
        success,
        remaining,
        resetTime: new Date(windowStart.getTime() + config.windowMs),
        blocked: false
      };

    } catch (error) {
      console.error('Rate limit check failed:', error);
      // Fail open - allow the action if rate limiting fails
      return {
        success: true,
        remaining: config.maxAttempts,
        resetTime: new Date(windowStart.getTime() + config.windowMs),
        blocked: false
      };
    }
  }

  /**
   * Record an action for rate limiting
   */
  async recordAction(
    userId: string,
    action: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      await db.auditLog.create({
        data: {
          userId,
          action,
          resource: `rate_limit:${action}`,
          metadata: metadata ? JSON.stringify(metadata) : null,
          timestamp: new Date()
        }
      });
    } catch (error) {
      console.error('Failed to record action for rate limiting:', error);
    }
  }

  /**
   * Get rate limit status for a user and action
   */
  async getRateLimitStatus(
    userId: string,
    action: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    return this.checkRateLimit(userId, action, config);
  }

  /**
   * Clear rate limit for a user (admin function)
   */
  async clearRateLimit(userId: string, action: string): Promise<void> {
    try {
      await db.auditLog.deleteMany({
        where: {
          userId,
          action: {
            in: [action, `${action}_BLOCKED`]
          }
        }
      });
    } catch (error) {
      console.error('Failed to clear rate limit:', error);
    }
  }
}

// Predefined rate limit configurations
export const RATE_LIMITS = {
  QUIZ_ATTEMPT: {
    windowMs: 24 * 60 * 60 * 1000, // 24 hours
    maxAttempts: 5, // 5 quiz attempts per day
    blockDurationMs: 60 * 60 * 1000 // 1 hour block
  },
  QUIZ_SUBMISSION: {
    windowMs: 60 * 1000, // 1 minute
    maxAttempts: 1, // 1 submission per minute
    blockDurationMs: 5 * 60 * 1000 // 5 minute block
  },
  LOGIN_ATTEMPT: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxAttempts: 5, // 5 login attempts per 15 minutes
    blockDurationMs: 30 * 60 * 1000 // 30 minute block
  },
  API_REQUEST: {
    windowMs: 60 * 1000, // 1 minute
    maxAttempts: 100, // 100 requests per minute
    blockDurationMs: 10 * 60 * 1000 // 10 minute block
  }
} as const;

export const rateLimiter = RateLimiter.getInstance();

// Helper functions for common use cases
export async function checkQuizAttemptLimit(userId: string): Promise<RateLimitResult> {
  return rateLimiter.checkRateLimit(userId, 'QUIZ_ATTEMPT', RATE_LIMITS.QUIZ_ATTEMPT);
}

export async function checkQuizSubmissionLimit(userId: string): Promise<RateLimitResult> {
  return rateLimiter.checkRateLimit(userId, 'QUIZ_SUBMISSION', RATE_LIMITS.QUIZ_SUBMISSION);
}

export async function recordQuizAttempt(userId: string, quizId: string): Promise<void> {
  return rateLimiter.recordAction(userId, 'QUIZ_ATTEMPT', { quizId });
}

export async function recordQuizSubmission(userId: string, quizId: string, score: number): Promise<void> {
  return rateLimiter.recordAction(userId, 'QUIZ_SUBMISSION', { quizId, score });
}
