/**
 * Session-Based Quiz Management
 * 
 * Provides secure session management for quiz attempts,
 * preventing session hijacking and ensuring quiz integrity.
 */

import { db } from '@/lib/db';
import crypto from 'crypto';
import { auditLogger, AuditAction } from '@/lib/audit-logger';

interface QuizSessionData {
  id: string;
  userId: string;
  quizId: string;
  sessionToken: string;
  startedAt: Date;
  expiresAt: Date;
  completedAt?: Date;
  isValid: boolean;
  metadata?: Record<string, any>;
}

interface SessionValidationResult {
  valid: boolean;
  session?: QuizSessionData;
  error?: string;
}

class QuizSessionManager {
  private static instance: QuizSessionManager;
  
  private constructor() {}
  
  static getInstance(): QuizSessionManager {
    if (!QuizSessionManager.instance) {
      QuizSessionManager.instance = new QuizSessionManager();
    }
    return QuizSessionManager.instance;
  }

  /**
   * Create a new quiz session
   */
  async createSession(
    userId: string,
    quizId: string,
    timeLimit?: number // in minutes
  ): Promise<QuizSessionData> {
    // Check if user already has an active session for this quiz
    const existingSession = await this.getActiveSession(userId, quizId);
    if (existingSession) {
      // Return existing session if still valid
      if (existingSession.isValid && new Date() < existingSession.expiresAt) {
        return existingSession;
      }
      // Invalidate expired session
      await this.invalidateSession(existingSession.sessionToken);
    }

    // Generate secure session token
    const sessionToken = this.generateSessionToken();
    
    // Calculate expiration time (default 2 hours or quiz time limit + 30 minutes)
    const defaultExpirationMinutes = 120;
    const expirationMinutes = timeLimit ? timeLimit + 30 : defaultExpirationMinutes;
    const expiresAt = new Date(Date.now() + expirationMinutes * 60 * 1000);

    // Create session in database
    const session = await db.quizSession.create({
      data: {
        userId,
        quizId,
        sessionToken,
        startedAt: new Date(),
        expiresAt
      }
    });

    // Log session creation
    await auditLogger.logQuizEvent(
      AuditAction.QUIZ_STARTED,
      userId,
      quizId,
      { sessionToken, expiresAt }
    );

    return {
      id: session.id,
      userId: session.userId,
      quizId: session.quizId,
      sessionToken: session.sessionToken,
      startedAt: session.startedAt,
      expiresAt: session.expiresAt,
      completedAt: session.completedAt || undefined,
      isValid: true
    };
  }

  /**
   * Validate a quiz session
   */
  async validateSession(sessionToken: string, userId: string, quizId: string): Promise<SessionValidationResult> {
    try {
      const session = await db.quizSession.findUnique({
        where: { sessionToken }
      });

      if (!session) {
        return { valid: false, error: 'Session not found' };
      }

      // Check if session belongs to the user
      if (session.userId !== userId) {
        await auditLogger.logSecurityEvent(
          {
            type: 'SESSION_HIJACK_ATTEMPT',
            description: 'User attempted to use another user\'s quiz session',
            riskLevel: 'HIGH',
            indicators: ['session_hijacking', 'unauthorized_access']
          },
          userId,
          `quiz_session:${sessionToken}`
        );
        return { valid: false, error: 'Session does not belong to user' };
      }

      // Check if session is for the correct quiz
      if (session.quizId !== quizId) {
        return { valid: false, error: 'Session is for a different quiz' };
      }

      // Check if session has expired
      if (new Date() > session.expiresAt) {
        await this.invalidateSession(sessionToken);
        return { valid: false, error: 'Session has expired' };
      }

      // Check if session is already completed
      if (session.completedAt) {
        return { valid: false, error: 'Session already completed' };
      }

      return {
        valid: true,
        session: {
          id: session.id,
          userId: session.userId,
          quizId: session.quizId,
          sessionToken: session.sessionToken,
          startedAt: session.startedAt,
          expiresAt: session.expiresAt,
          completedAt: session.completedAt || undefined,
          isValid: true
        }
      };

    } catch (error) {
      console.error('Session validation error:', error);
      return { valid: false, error: 'Session validation failed' };
    }
  }

  /**
   * Complete a quiz session
   */
  async completeSession(sessionToken: string, userId: string): Promise<boolean> {
    try {
      const validation = await this.validateSession(sessionToken, userId, '');
      if (!validation.valid || !validation.session) {
        return false;
      }

      await db.quizSession.update({
        where: { sessionToken },
        data: { completedAt: new Date() }
      });

      // Log session completion
      await auditLogger.logQuizEvent(
        AuditAction.QUIZ_COMPLETED,
        userId,
        validation.session.quizId,
        { sessionToken, completedAt: new Date() }
      );

      return true;
    } catch (error) {
      console.error('Session completion error:', error);
      return false;
    }
  }

  /**
   * Invalidate a session
   */
  async invalidateSession(sessionToken: string): Promise<void> {
    try {
      await db.quizSession.delete({
        where: { sessionToken }
      });
    } catch (error) {
      console.error('Session invalidation error:', error);
    }
  }

  /**
   * Get active session for user and quiz
   */
  async getActiveSession(userId: string, quizId: string): Promise<QuizSessionData | null> {
    try {
      const session = await db.quizSession.findFirst({
        where: {
          userId,
          quizId,
          completedAt: null,
          expiresAt: { gt: new Date() }
        },
        orderBy: { startedAt: 'desc' }
      });

      if (!session) return null;

      return {
        id: session.id,
        userId: session.userId,
        quizId: session.quizId,
        sessionToken: session.sessionToken,
        startedAt: session.startedAt,
        expiresAt: session.expiresAt,
        completedAt: session.completedAt || undefined,
        isValid: true
      };
    } catch (error) {
      console.error('Get active session error:', error);
      return null;
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      const result = await db.quizSession.deleteMany({
        where: {
          expiresAt: { lt: new Date() }
        }
      });
      return result.count;
    } catch (error) {
      console.error('Session cleanup error:', error);
      return 0;
    }
  }

  /**
   * Get session statistics for monitoring
   */
  async getSessionStats(): Promise<{
    activeSessions: number;
    expiredSessions: number;
    completedSessions: number;
  }> {
    try {
      const now = new Date();
      
      const [activeSessions, expiredSessions, completedSessions] = await Promise.all([
        db.quizSession.count({
          where: {
            completedAt: null,
            expiresAt: { gt: now }
          }
        }),
        db.quizSession.count({
          where: {
            completedAt: null,
            expiresAt: { lte: now }
          }
        }),
        db.quizSession.count({
          where: {
            completedAt: { not: null }
          }
        })
      ]);

      return { activeSessions, expiredSessions, completedSessions };
    } catch (error) {
      console.error('Session stats error:', error);
      return { activeSessions: 0, expiredSessions: 0, completedSessions: 0 };
    }
  }

  private generateSessionToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }
}

export const quizSessionManager = QuizSessionManager.getInstance();

// Helper functions
export async function createQuizSession(
  userId: string,
  quizId: string,
  timeLimit?: number
): Promise<QuizSessionData> {
  return quizSessionManager.createSession(userId, quizId, timeLimit);
}

export async function validateQuizSession(
  sessionToken: string,
  userId: string,
  quizId: string
): Promise<SessionValidationResult> {
  return quizSessionManager.validateSession(sessionToken, userId, quizId);
}

export async function completeQuizSession(
  sessionToken: string,
  userId: string
): Promise<boolean> {
  return quizSessionManager.completeSession(sessionToken, userId);
}

export async function getActiveQuizSession(
  userId: string,
  quizId: string
): Promise<QuizSessionData | null> {
  return quizSessionManager.getActiveSession(userId, quizId);
}
