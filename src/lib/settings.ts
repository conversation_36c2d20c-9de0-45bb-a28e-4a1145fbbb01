import { db } from "@/lib/db";

export interface SystemSetting {
  id: string;
  key: string;
  value: any;
  type: "boolean" | "string" | "number" | "object" | "array";
  category: string;
  title: string;
  description?: string;
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface SettingDefinition {
  key: string;
  defaultValue: any;
  type: "boolean" | "string" | "number" | "object" | "array";
  category: string;
  title: string;
  description?: string;
  isPublic?: boolean;
}

// Default system settings
export const DEFAULT_SETTINGS: SettingDefinition[] = [
  // Feature Flags
  {
    key: "features.questionPools.enabled",
    defaultValue: false,
    type: "boolean",
    category: "features",
    title: "Question Pools",
    description: "Enable dynamic question pools and selection rules for creating randomized quizzes",
    isPublic: false,
  },
  {
    key: "features.learningPaths.enabled",
    defaultValue: true,
    type: "boolean",
    category: "features",
    title: "Learning Paths",
    description: "Enable structured learning paths with sequential quiz progression",
    isPublic: true,
  },
  {
    key: "features.analytics.enabled",
    defaultValue: true,
    type: "boolean",
    category: "features",
    title: "Advanced Analytics",
    description: "Enable detailed analytics and reporting features",
    isPublic: false,
  },
  {
    key: "features.gamification.enabled",
    defaultValue: true,
    type: "boolean",
    category: "features",
    title: "Gamification",
    description: "Enable badges, points, streaks, and other gamification features",
    isPublic: true,
  },
  
  // Security Settings
  {
    key: "security.maxQuizAttempts",
    defaultValue: 3,
    type: "number",
    category: "security",
    title: "Maximum Quiz Attempts",
    description: "Maximum number of attempts allowed per quiz per user (0 = unlimited)",
    isPublic: true,
  },
  {
    key: "security.requireEmailVerification",
    defaultValue: false,
    type: "boolean",
    category: "security",
    title: "Require Email Verification",
    description: "Require users to verify their email before taking quizzes",
    isPublic: true,
  },
  
  // UI Settings
  {
    key: "ui.defaultTheme",
    defaultValue: "light",
    type: "string",
    category: "ui",
    title: "Default Theme",
    description: "Default theme for new users (light, dark, auto)",
    isPublic: true,
  },
  {
    key: "ui.showQuizDifficulty",
    defaultValue: true,
    type: "boolean",
    category: "ui",
    title: "Show Quiz Difficulty",
    description: "Display difficulty levels on quiz cards and pages",
    isPublic: true,
  },
  
  // Content Settings
  {
    key: "content.defaultTimeLimit",
    defaultValue: 30,
    type: "number",
    category: "content",
    title: "Default Quiz Time Limit",
    description: "Default time limit in minutes for new quizzes (0 = no limit)",
    isPublic: true,
  },
  {
    key: "content.defaultPassingScore",
    defaultValue: 70,
    type: "number",
    category: "content",
    title: "Default Passing Score",
    description: "Default passing score percentage for new quizzes",
    isPublic: true,
  },
];

/**
 * Settings Service Class
 */
export class SettingsService {
  /**
   * Initialize default settings in the database
   */
  static async initializeDefaults(): Promise<void> {
    for (const setting of DEFAULT_SETTINGS) {
      await db.systemSettings.upsert({
        where: { key: setting.key },
        update: {
          // Only update metadata, not the value
          title: setting.title,
          description: setting.description,
          type: setting.type,
          category: setting.category,
          isPublic: setting.isPublic || false,
        },
        create: {
          key: setting.key,
          value: setting.defaultValue,
          type: setting.type,
          category: setting.category,
          title: setting.title,
          description: setting.description,
          isPublic: setting.isPublic || false,
        },
      });
    }
  }

  /**
   * Get a setting value by key
   */
  static async get<T = any>(key: string, defaultValue?: T): Promise<T> {
    try {
      const setting = await db.systemSettings.findUnique({
        where: { key },
      });

      if (setting) {
        return setting.value as T;
      }

      // If not found, check if it's in our defaults
      const defaultSetting = DEFAULT_SETTINGS.find(s => s.key === key);
      if (defaultSetting) {
        return defaultSetting.defaultValue as T;
      }

      return defaultValue as T;
    } catch (error) {
      console.error(`Error getting setting ${key}:`, error);
      return defaultValue as T;
    }
  }

  /**
   * Set a setting value
   */
  static async set(key: string, value: any): Promise<void> {
    const defaultSetting = DEFAULT_SETTINGS.find(s => s.key === key);
    
    if (!defaultSetting) {
      throw new Error(`Unknown setting key: ${key}`);
    }

    await db.systemSettings.upsert({
      where: { key },
      update: { value },
      create: {
        key,
        value,
        type: defaultSetting.type,
        category: defaultSetting.category,
        title: defaultSetting.title,
        description: defaultSetting.description,
        isPublic: defaultSetting.isPublic || false,
      },
    });
  }

  /**
   * Get all settings by category
   */
  static async getByCategory(category: string, includePrivate: boolean = false): Promise<SystemSetting[]> {
    const where: any = { category };
    
    if (!includePrivate) {
      where.isPublic = true;
    }

    return await db.systemSettings.findMany({
      where,
      orderBy: { key: 'asc' },
    });
  }

  /**
   * Get all settings (admin only)
   */
  static async getAll(): Promise<SystemSetting[]> {
    return await db.systemSettings.findMany({
      orderBy: [{ category: 'asc' }, { key: 'asc' }],
    });
  }

  /**
   * Check if a feature is enabled
   */
  static async isFeatureEnabled(featureName: string): Promise<boolean> {
    return await this.get(`features.${featureName}.enabled`, false);
  }

  /**
   * Reset a setting to its default value
   */
  static async reset(key: string): Promise<void> {
    const defaultSetting = DEFAULT_SETTINGS.find(s => s.key === key);
    
    if (!defaultSetting) {
      throw new Error(`Unknown setting key: ${key}`);
    }

    await this.set(key, defaultSetting.defaultValue);
  }

  /**
   * Reset all settings to defaults
   */
  static async resetAll(): Promise<void> {
    for (const setting of DEFAULT_SETTINGS) {
      await this.set(setting.key, setting.defaultValue);
    }
  }
}

// Convenience functions for common feature checks
export const isQuestionPoolsEnabled = () => SettingsService.isFeatureEnabled("questionPools");
export const isLearningPathsEnabled = () => SettingsService.isFeatureEnabled("learningPaths");
export const isAnalyticsEnabled = () => SettingsService.isFeatureEnabled("analytics");
export const isGamificationEnabled = () => SettingsService.isFeatureEnabled("gamification");
