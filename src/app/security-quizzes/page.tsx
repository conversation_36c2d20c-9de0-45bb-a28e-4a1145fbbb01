import Link from "next/link";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default async function SecurityQuizzesPage() {
  const session = await getServerSession(authOptions);

  // Get security-related quizzes
  const securityQuizzes = await db.quiz.findMany({
    where: {
      isPublished: true,
      OR: [
        { tags: { hasSome: ["security", "cybersecurity", "hacking", "penetration testing", "vulnerability"] } },
        { category: { name: { in: ["Web Application Security", "Network Security", "Cryptography & Encryption", "Cloud Security", "Malware Analysis"] } } },
      ],
    },
    include: {
      creator: {
        select: {
          name: true,
        },
      },
      category: {
        select: {
          name: true,
        },
      },
      difficulty: {
        select: {
          name: true,
        },
      },
      _count: {
        select: {
          questions: true,
        },
      },
    },
    orderBy: {
      updatedAt: "desc",
    },
    take: 9,
  });

  const categories = [
    {
      name: "Web Application Security",
      description: "SQL injection, XSS, CSRF, and web vulnerability assessment",
      icon: "🌐",
      color: "bg-blue-100 text-blue-800",
    },
    {
      name: "Network Security",
      description: "Firewalls, intrusion detection, network protocols, and monitoring",
      icon: "🔒",
      color: "bg-green-100 text-green-800",
    },
    {
      name: "Cryptography & Encryption",
      description: "Encryption algorithms, digital signatures, and cryptographic protocols",
      icon: "🔐",
      color: "bg-purple-100 text-purple-800",
    },
    {
      name: "Cloud Security",
      description: "AWS, Azure, GCP security, container security, and cloud compliance",
      icon: "☁️",
      color: "bg-cyan-100 text-cyan-800",
    },
    {
      name: "Malware Analysis",
      description: "Reverse engineering, dynamic analysis, and threat intelligence",
      icon: "🦠",
      color: "bg-red-100 text-red-800",
    },
    {
      name: "Incident Response",
      description: "Digital forensics, incident handling, and security operations",
      icon: "🚨",
      color: "bg-orange-100 text-orange-800",
    },
  ];

  return (
    <div className="flex flex-col min-h-screen">
      {/* Navigation */}
      <header className="border-b">
        <div className="container mx-auto px-4 flex h-16 items-center justify-between">
          <div className="flex items-center gap-6">
            <Link href="/" className="text-xl font-bold">
              QuizFlow
            </Link>
            <nav className="hidden md:flex gap-6">
              <Link
                href="/landing"
                className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
              >
                Home
              </Link>
              <Link
                href="/explore"
                className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
              >
                Explore
              </Link>
              <Link
                href="/security-quizzes"
                className="text-sm font-medium text-primary"
              >
                Security Quizzes
              </Link>
            </nav>
          </div>
          <div className="flex items-center gap-4">
            {session ? (
              <Button asChild>
                <Link href="/dashboard">Dashboard</Link>
              </Button>
            ) : (
              <div className="flex items-center gap-4">
                <Button variant="outline" asChild>
                  <Link href="/auth/login">Sign In</Link>
                </Button>
                <Button asChild>
                  <Link href="/auth/register">Sign Up</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </header>

      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-20 md:py-32 bg-gradient-to-br from-red-50 via-background to-blue-50 dark:from-red-950/20 dark:to-blue-950/20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <div className="mb-8">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-red-100 dark:bg-red-900/20 rounded-full mb-6">
                  <svg className="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
              </div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                <span className="bg-gradient-to-r from-red-600 to-blue-600 bg-clip-text text-transparent">
                  Cybersecurity Training
                </span>
              </h1>
              <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto">
                Master cybersecurity with hands-on quizzes, real-world scenarios, and practical challenges.
                From beginner to advanced, build the skills that matter.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" asChild>
                  <Link href="#featured-quizzes">Start Learning</Link>
                </Button>
                <Button size="lg" variant="outline" asChild>
                  <Link href="/explore">Browse All Quizzes</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Categories Section */}
        <section className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Security Domains</h2>
              <p className="text-lg text-muted-foreground">
                Comprehensive coverage of cybersecurity topics and specializations
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categories.map((category, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader>
                    <div className="flex items-center gap-3 mb-2">
                      <span className="text-2xl">{category.icon}</span>
                      <Badge className={category.color}>{category.name}</Badge>
                    </div>
                    <CardTitle className="text-lg">{category.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-sm">
                      {category.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Why Our Security Training?</h2>
              <p className="text-lg text-muted-foreground">
                Real-world focused cybersecurity education with practical applications
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                  </svg>
                </div>
                <h3 className="font-semibold mb-2">CVE-Based Scenarios</h3>
                <p className="text-sm text-muted-foreground">Learn from real vulnerabilities and security incidents</p>
              </div>

              <div className="text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-4">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <h3 className="font-semibold mb-2">Hands-On Tools</h3>
                <p className="text-sm text-muted-foreground">Practice with industry-standard security tools</p>
              </div>

              <div className="text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-4">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="font-semibold mb-2">Progressive Difficulty</h3>
                <p className="text-sm text-muted-foreground">From beginner to advanced skill levels</p>
              </div>

              <div className="text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg mb-4">
                  <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className="font-semibold mb-2">Detailed Analytics</h3>
                <p className="text-sm text-muted-foreground">Track your security knowledge progress</p>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Quizzes Section */}
        <section id="featured-quizzes" className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Featured Security Quizzes</h2>
              <p className="text-lg text-muted-foreground">
                Start your cybersecurity journey with these curated quizzes
              </p>
            </div>

            {securityQuizzes.length === 0 ? (
              <div className="text-center py-12">
                <h3 className="text-lg font-medium mb-2">No security quizzes available</h3>
                <p className="text-muted-foreground mb-6">
                  Security quizzes are being prepared. Check back soon!
                </p>
                <Button asChild>
                  <Link href="/explore">Browse All Quizzes</Link>
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {securityQuizzes.map((quiz: any) => (
                  <Card key={quiz.id} className="h-full flex flex-col hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex justify-between items-start mb-2">
                        <Badge variant="secondary" className="text-xs">
                          {quiz.category?.name || "Security"}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {quiz.difficulty?.name || "Intermediate"}
                        </Badge>
                      </div>
                      <CardTitle className="line-clamp-2 text-lg">{quiz.title}</CardTitle>
                      <CardDescription className="text-sm">
                        By {quiz.creator?.name || "Anonymous"}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-1">
                      <p className="text-sm text-muted-foreground line-clamp-3 mb-4">
                        {quiz.description || "Enhance your cybersecurity knowledge with this comprehensive quiz."}
                      </p>
                      <div className="flex justify-between items-center text-sm text-muted-foreground mb-4">
                        <span>{quiz._count?.questions || 0} questions</span>
                        <span>{quiz.timeLimit ? `${quiz.timeLimit} min` : "No limit"}</span>
                      </div>
                      <div className="flex flex-wrap gap-1 mb-4">
                        {quiz.tags.slice(0, 3).map((tag: string, index: number) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <Button className="w-full" asChild>
                        <Link href={`/quiz/${quiz.id}`}>Start Quiz</Link>
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            <div className="text-center mt-12">
              <Button variant="outline" size="lg" asChild>
                <Link href="/explore">View All Security Quizzes</Link>
              </Button>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-red-600 to-blue-600 text-white">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Secure Your Future?</h2>
              <p className="text-xl mb-8 opacity-90">
                Join the cybersecurity professionals mastering their skills with QuizFlow
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" variant="secondary" asChild>
                  <Link href="/explore">Start Learning Now</Link>
                </Button>
                <Button size="lg" variant="outline" className="bg-transparent border-white text-white hover:bg-white hover:text-red-600" asChild>
                  <Link href="/auth/register">Create Free Account</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="py-8 border-t">
        <div className="container mx-auto px-4 text-center">
          <p className="text-muted-foreground">
            &copy; {new Date().getFullYear()} QuizFlow. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
