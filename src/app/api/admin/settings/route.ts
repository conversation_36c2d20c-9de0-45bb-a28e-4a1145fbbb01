import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { SettingsService } from "@/lib/settings";

// Get all settings (admin only)
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "admin") {
      return NextResponse.json(
        { message: "Unauthorized: Admin access required" },
        { status: 401 }
      );
    }

    const settings = await SettingsService.getAll();
    
    return NextResponse.json(settings);
  } catch (error) {
    console.error("Error fetching settings:", error);
    return NextResponse.json(
      { message: "An error occurred while fetching settings" },
      { status: 500 }
    );
  }
}

// Update multiple settings (admin only)
export async function PATCH(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "admin") {
      return NextResponse.json(
        { message: "Unauthorized: Admin access required" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { settings } = body;

    if (!settings || !Array.isArray(settings)) {
      return NextResponse.json(
        { message: "Invalid request: settings array required" },
        { status: 400 }
      );
    }

    // Update each setting
    const results = [];
    for (const setting of settings) {
      try {
        await SettingsService.set(setting.key, setting.value);
        results.push({ key: setting.key, success: true });
      } catch (error) {
        console.error(`Error updating setting ${setting.key}:`, error);
        results.push({ 
          key: setting.key, 
          success: false, 
          error: error instanceof Error ? error.message : "Unknown error" 
        });
      }
    }

    return NextResponse.json({ results });
  } catch (error) {
    console.error("Error updating settings:", error);
    return NextResponse.json(
      { message: "An error occurred while updating settings" },
      { status: 500 }
    );
  }
}

// Initialize default settings
export async function POST() {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "admin") {
      return NextResponse.json(
        { message: "Unauthorized: Admin access required" },
        { status: 401 }
      );
    }

    await SettingsService.initializeDefaults();
    
    return NextResponse.json({ 
      message: "Default settings initialized successfully" 
    });
  } catch (error) {
    console.error("Error initializing settings:", error);
    return NextResponse.json(
      { message: "An error occurred while initializing settings" },
      { status: 500 }
    );
  }
}
