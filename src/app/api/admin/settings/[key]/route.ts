import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { SettingsService } from "@/lib/settings";

// Get a specific setting
export async function GET(
  req: Request,
  { params }: { params: Promise<{ key: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { key } = await params;

    if (!session || session.user.role !== "admin") {
      return NextResponse.json(
        { message: "Unauthorized: Admin access required" },
        { status: 401 }
      );
    }

    const value = await SettingsService.get(key);
    
    return NextResponse.json({ key, value });
  } catch (error) {
    console.error(`Error fetching setting ${(await params).key}:`, error);
    return NextResponse.json(
      { message: "An error occurred while fetching the setting" },
      { status: 500 }
    );
  }
}

// Update a specific setting
export async function PATCH(
  req: Request,
  { params }: { params: Promise<{ key: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { key } = await params;

    if (!session || session.user.role !== "admin") {
      return NextResponse.json(
        { message: "Unauthorized: Admin access required" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { value } = body;

    if (value === undefined) {
      return NextResponse.json(
        { message: "Invalid request: value is required" },
        { status: 400 }
      );
    }

    await SettingsService.set(key, value);
    
    return NextResponse.json({ 
      key, 
      value, 
      message: "Setting updated successfully" 
    });
  } catch (error) {
    console.error(`Error updating setting ${(await params).key}:`, error);
    return NextResponse.json(
      { message: error instanceof Error ? error.message : "An error occurred while updating the setting" },
      { status: 500 }
    );
  }
}

// Reset a setting to default
export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ key: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { key } = await params;

    if (!session || session.user.role !== "admin") {
      return NextResponse.json(
        { message: "Unauthorized: Admin access required" },
        { status: 401 }
      );
    }

    await SettingsService.reset(key);
    const newValue = await SettingsService.get(key);
    
    return NextResponse.json({ 
      key, 
      value: newValue, 
      message: "Setting reset to default successfully" 
    });
  } catch (error) {
    console.error(`Error resetting setting ${(await params).key}:`, error);
    return NextResponse.json(
      { message: error instanceof Error ? error.message : "An error occurred while resetting the setting" },
      { status: 500 }
    );
  }
}
