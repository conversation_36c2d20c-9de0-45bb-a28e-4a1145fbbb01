import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    const { searchParams } = new URL(req.url);

    // Extract search parameters
    const query = searchParams.get("q") || "";
    const category = searchParams.get("category");
    const difficulty = searchParams.get("difficulty");
    const tags = searchParams.get("tags")?.split(",").filter(Boolean) || [];
    const realWorldOnly = searchParams.get("realWorld") === "true";
    const cveOnly = searchParams.get("cve") === "true";
    const tools = searchParams.get("tools")?.split(",").filter(Boolean) || [];
    const minDuration = searchParams.get("minDuration") ? parseInt(searchParams.get("minDuration")!) : undefined;
    const maxDuration = searchParams.get("maxDuration") ? parseInt(searchParams.get("maxDuration")!) : undefined;
    const sortBy = searchParams.get("sortBy") || "updatedAt";
    const sortOrder = searchParams.get("sortOrder") || "desc";
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");

    // Build where clause
    const whereClause: any = {
      isPublished: true,
    };

    // Text search across title, description, and tags
    if (query) {
      whereClause.OR = [
        { title: { contains: query, mode: "insensitive" } },
        { description: { contains: query, mode: "insensitive" } },
        { tags: { hasSome: [query] } },
        { author: { contains: query, mode: "insensitive" } }
      ];
    }

    // Category filter
    if (category) {
      whereClause.category = {
        OR: [
          { slug: category },
          { name: { contains: category, mode: "insensitive" } }
        ]
      };
    }

    // Difficulty filter
    if (difficulty) {
      whereClause.difficulty = {
        OR: [
          { name: { contains: difficulty, mode: "insensitive" } },
          { level: parseInt(difficulty) || undefined }
        ]
      };
    }

    // Tags filter
    if (tags.length > 0) {
      whereClause.tags = { hasSome: tags };
    }

    // Real-world scenarios filter
    if (realWorldOnly) {
      whereClause.realWorldScenario = true;
    }

    // CVE-based filter
    if (cveOnly) {
      whereClause.questions = {
        some: {
          cveReference: { not: null }
        }
      };
    }

    // Tools filter
    if (tools.length > 0) {
      whereClause.questions = {
        some: {
          toolsRequired: { hasSome: tools }
        }
      };
    }

    // Duration filters
    if (minDuration !== undefined || maxDuration !== undefined) {
      whereClause.timeLimit = {};
      if (minDuration !== undefined) {
        whereClause.timeLimit.gte = minDuration;
      }
      if (maxDuration !== undefined) {
        whereClause.timeLimit.lte = maxDuration;
      }
    }

    // Build order by clause
    const orderBy: any = {};
    switch (sortBy) {
      case "title":
        orderBy.title = sortOrder;
        break;
      case "difficulty":
        orderBy.difficulty = { level: sortOrder };
        break;
      case "duration":
        orderBy.timeLimit = sortOrder;
        break;
      case "popularity":
        orderBy._count = { responses: sortOrder };
        break;
      case "createdAt":
        orderBy.createdAt = sortOrder;
        break;
      default:
        orderBy.updatedAt = sortOrder;
    }

    // Execute query with pagination
    const [quizzes, totalCount] = await Promise.all([
      db.quiz.findMany({
        where: whereClause,
        include: {
          category: true,
          difficulty: true,
          creator: {
            select: {
              name: true,
              email: true,
            },
          },
          _count: {
            select: {
              questions: true,
              responses: true,
            },
          },
          questions: {
            select: {
              id: true,
              type: true,
              cveReference: true,
              toolsRequired: true,
              realWorldScenario: true,
            },
          },
        },
        orderBy,
        skip: (page - 1) * limit,
        take: limit,
      }),
      db.quiz.count({ where: whereClause }),
    ]);

    // Calculate additional metadata for each quiz
    const enrichedQuizzes = quizzes.map((quiz) => {
      const questionTypes = [...new Set(quiz.questions.map(q => q.type))];
      const cveReferences = quiz.questions
        .map(q => q.cveReference)
        .filter(Boolean);
      const toolsUsed = [...new Set(
        quiz.questions.flatMap(q => q.toolsRequired || [])
      )];
      const realWorldQuestions = quiz.questions.filter(q => q.realWorldScenario).length;

      return {
        ...quiz,
        metadata: {
          questionTypes,
          cveReferences,
          toolsUsed,
          realWorldQuestions,
          totalQuestions: quiz._count.questions,
          totalResponses: quiz._count.responses,
          averageRating: null, // TODO: Implement rating system
        },
        questions: undefined, // Remove detailed questions from response
      };
    });

    // Calculate facets for filtering UI
    const facets = await calculateFacets(whereClause);

    return NextResponse.json({
      quizzes: enrichedQuizzes,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1,
      },
      facets,
      searchParams: {
        query,
        category,
        difficulty,
        tags,
        realWorldOnly,
        cveOnly,
        tools,
        minDuration,
        maxDuration,
        sortBy,
        sortOrder,
      },
    });
  } catch (error) {
    console.error("Error searching quizzes:", error);
    return NextResponse.json(
      { message: "An error occurred while searching quizzes" },
      { status: 500 }
    );
  }
}

async function calculateFacets(baseWhereClause: any) {
  try {
    // Get all published quizzes for facet calculation
    const allQuizzes = await db.quiz.findMany({
      where: { isPublished: true },
      include: {
        category: true,
        difficulty: true,
        questions: {
          select: {
            toolsRequired: true,
            cveReference: true,
            realWorldScenario: true,
          },
        },
      },
    });

    // Calculate category facets
    const categoryFacets = allQuizzes.reduce((acc: any, quiz) => {
      if (quiz.category) {
        const key = quiz.category.slug;
        acc[key] = (acc[key] || 0) + 1;
      }
      return acc;
    }, {});

    // Calculate difficulty facets
    const difficultyFacets = allQuizzes.reduce((acc: any, quiz) => {
      if (quiz.difficulty) {
        const key = quiz.difficulty.name;
        acc[key] = (acc[key] || 0) + 1;
      }
      return acc;
    }, {});

    // Calculate tool facets
    const toolFacets = allQuizzes.reduce((acc: any, quiz) => {
      const tools = quiz.questions.flatMap(q => q.toolsRequired || []);
      tools.forEach(tool => {
        acc[tool] = (acc[tool] || 0) + 1;
      });
      return acc;
    }, {});

    // Calculate tag facets
    const tagFacets = allQuizzes.reduce((acc: any, quiz) => {
      quiz.tags.forEach(tag => {
        acc[tag] = (acc[tag] || 0) + 1;
      });
      return acc;
    }, {});

    // Calculate CVE count
    const cveCount = allQuizzes.filter(quiz =>
      quiz.questions.some(q => q.cveReference)
    ).length;

    // Calculate real-world scenario count (check questions for real-world scenarios)
    const realWorldCount = allQuizzes.filter(quiz =>
      quiz.questions.some(q => q.realWorldScenario)
    ).length;

    return {
      categories: Object.entries(categoryFacets)
        .map(([key, count]) => ({ key, count }))
        .sort((a, b) => (b.count as number) - (a.count as number)),
      difficulties: Object.entries(difficultyFacets)
        .map(([key, count]) => ({ key, count }))
        .sort((a, b) => (b.count as number) - (a.count as number)),
      tools: Object.entries(toolFacets)
        .map(([key, count]) => ({ key, count }))
        .sort((a, b) => (b.count as number) - (a.count as number))
        .slice(0, 20), // Top 20 tools
      tags: Object.entries(tagFacets)
        .map(([key, count]) => ({ key, count }))
        .sort((a, b) => (b.count as number) - (a.count as number))
        .slice(0, 30), // Top 30 tags
      specialFilters: {
        cveCount,
        realWorldCount,
      },
    };
  } catch (error) {
    console.error("Error calculating facets:", error);
    return {
      categories: [],
      difficulties: [],
      tools: [],
      tags: [],
      specialFilters: { cveCount: 0, realWorldCount: 0 },
    };
  }
}
