import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { checkQuizSubmissionLimit, recordQuizSubmission } from '@/lib/rate-limit';
import { validateCSRF } from '@/lib/csrf';
import { logQuizSubmission, logSuspiciousQuizActivity, auditLogger, AuditAction } from '@/lib/audit-logger';
import { validateQuizSession, completeQuizSession } from '@/lib/quiz-session';

// SECURITY: Server-side quiz submission and validation
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: quizId } = await params;
    const { answers, timeSpent, sessionToken, csrfToken } = await req.json();

    // SECURITY: Validate CSRF token for quiz submission
    try {
      await validateCSRF(req, 'QUIZ_SUBMISSION');
    } catch (error) {
      return NextResponse.json(
        { error: 'CSRF validation failed. Please refresh the page and try again.' },
        { status: 403 }
      );
    }

    // SECURITY: Validate quiz session
    if (sessionToken) {
      const sessionValidation = await validateQuizSession(sessionToken, session.user.id, quizId);
      if (!sessionValidation.valid) {
        return NextResponse.json(
          { error: `Session validation failed: ${sessionValidation.error}` },
          { status: 403 }
        );
      }
    }

    // Validate input
    if (!answers || typeof answers !== 'object') {
      return NextResponse.json(
        { error: 'Invalid answers format' },
        { status: 400 }
      );
    }

    if (!timeSpent || timeSpent < 1) {
      return NextResponse.json(
        { error: 'Invalid time spent' },
        { status: 400 }
      );
    }

    // SECURITY: Check rate limiting for quiz submissions
    const rateLimitResult = await checkQuizSubmissionLimit(session.user.id);
    if (!rateLimitResult.success) {
      const errorMessage = rateLimitResult.blocked
        ? `You are temporarily blocked from submitting quizzes. Try again after ${rateLimitResult.blockUntil?.toLocaleTimeString()}`
        : `Rate limit exceeded. You can submit ${rateLimitResult.remaining} more quiz(es). Limit resets at ${rateLimitResult.resetTime.toLocaleTimeString()}`;

      return NextResponse.json(
        {
          error: errorMessage,
          rateLimitInfo: {
            remaining: rateLimitResult.remaining,
            resetTime: rateLimitResult.resetTime,
            blocked: rateLimitResult.blocked,
            blockUntil: rateLimitResult.blockUntil
          }
        },
        { status: 429 }
      );
    }

    // Check daily attempt limit
    const dailyAttempts = await db.userResponse.count({
      where: {
        userId: session.user.id,
        quizId: quizId,
        submittedAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      }
    });

    if (dailyAttempts >= 3) {
      return NextResponse.json(
        { error: 'Daily attempt limit reached (3 attempts per day)' },
        { status: 429 }
      );
    }

    // Get quiz with all questions and correct answers (SERVER-SIDE ONLY)
    const quiz = await getQuizWithAnswers(quizId);
    if (!quiz) {
      return NextResponse.json(
        { error: 'Quiz not found' },
        { status: 404 }
      );
    }

    if (!quiz.isPublished) {
      return NextResponse.json(
        { error: 'Quiz is not published' },
        { status: 403 }
      );
    }

    // Validate and score answers server-side
    const scoringResult = await validateAndScoreAnswers(quiz, answers, timeSpent, session.user.id);

    // Store the quiz attempt
    const userResponse = await db.userResponse.create({
      data: {
        userId: session.user.id,
        quizId: quizId,
        answers: JSON.stringify(answers),
        score: scoringResult.score,
        maxScore: scoringResult.maxScore,
        timeSpent: timeSpent,
        submittedAt: new Date(),
        passed: scoringResult.score >= (quiz.passingScore || 0),
        metadata: JSON.stringify({
          questionResults: scoringResult.questionResults,
          suspiciousActivity: scoringResult.suspiciousActivity,
          userAgent: req.headers.get('user-agent'),
          ipAddress: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
        })
      }
    });

    // SECURITY: Record submission for rate limiting
    await recordQuizSubmission(session.user.id, quizId, scoringResult.score);

    // SECURITY: Comprehensive audit logging
    await logQuizSubmission(
      session.user.id,
      quizId,
      scoringResult.score,
      scoringResult.maxScore,
      timeSpent,
      req
    );

    // Log suspicious activity if detected
    if (scoringResult.suspiciousActivity.length > 0) {
      await logSuspiciousQuizActivity(
        session.user.id,
        quizId,
        scoringResult.suspiciousActivity,
        req
      );
    }

    // SECURITY: Complete quiz session if session token provided
    if (sessionToken) {
      await completeQuizSession(sessionToken, session.user.id);
    }

    // Return results without exposing correct answers
    return NextResponse.json({
      success: true,
      attemptId: userResponse.id,
      score: scoringResult.score,
      maxScore: scoringResult.maxScore,
      percentage: Math.round((scoringResult.score / scoringResult.maxScore) * 100),
      passed: scoringResult.score >= (quiz.passingScore || 0),
      passingScore: quiz.passingScore || 0,
      timeSpent: timeSpent,
      submittedAt: userResponse.submittedAt,
      // Only return question results without correct answers
      questionResults: scoringResult.questionResults.map(result => ({
        questionId: result.questionId,
        correct: result.correct,
        points: result.points,
        // Don't expose correct answers to client
      }))
    });

  } catch (error) {
    console.error('Error submitting quiz:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// SECURITY: Get quiz with answers - SERVER-SIDE ONLY
async function getQuizWithAnswers(quizId: string) {
  return await db.quiz.findUnique({
    where: { id: quizId },
    include: {
      questions: true, // Include all question data including answers
      questionPools: {
        include: {
          questions: true
        }
      },
      selectionRules: true
    }
  });
}

// SECURITY: Server-side answer validation and scoring
async function validateAndScoreAnswers(quiz: any, userAnswers: any, timeSpent: number, userId: string) {
  let totalScore = 0;
  let maxScore = 0;
  const questionResults: any[] = [];
  const suspiciousActivity: string[] = [];

  // Detect suspicious timing patterns
  if (timeSpent < 30) {
    suspiciousActivity.push('EXTREMELY_FAST_COMPLETION');
  }

  const avgTimePerQuestion = timeSpent / quiz.questions.length;
  if (avgTimePerQuestion < 5) {
    suspiciousActivity.push('VERY_FAST_PER_QUESTION');
  }

  // Process each question
  for (const question of quiz.questions) {
    maxScore += question.points;
    const userAnswer = userAnswers[question.questionId];

    if (userAnswer === undefined || userAnswer === null) {
      questionResults.push({
        questionId: question.questionId,
        correct: false,
        points: 0,
        reason: 'NO_ANSWER'
      });
      continue;
    }

    const isCorrect = validateSingleAnswer(question, userAnswer);
    const points = isCorrect ? question.points : 0;
    totalScore += points;

    questionResults.push({
      questionId: question.questionId,
      correct: isCorrect,
      points: points,
      userAnswer: userAnswer, // Store for audit but don't return to client
      correctAnswer: getCorrectAnswerForAudit(question) // For audit only
    });
  }

  // Additional suspicious activity detection
  const correctCount = questionResults.filter(r => r.correct).length;
  const correctPercentage = (correctCount / quiz.questions.length) * 100;

  if (correctPercentage === 100 && timeSpent < 60) {
    suspiciousActivity.push('PERFECT_SCORE_TOO_FAST');
  }

  return {
    score: totalScore,
    maxScore,
    questionResults,
    suspiciousActivity
  };
}

// SECURITY: Validate individual answer
function validateSingleAnswer(question: any, userAnswer: any): boolean {
  try {
    switch (question.type) {
      case 'multiple_choice':
        return validateMultipleChoiceAnswer(question, userAnswer);

      case 'true_false':
        return validateTrueFalseAnswer(question, userAnswer);

      case 'short_answer':
        return validateShortAnswer(question, userAnswer);

      case 'matching':
        return validateMatchingAnswer(question, userAnswer);

      case 'fill_in_the_blank':
        return validateFillInTheBlankAnswer(question, userAnswer);

      case 'essay':
        // Essay questions require manual grading
        return false;

      default:
        console.warn(`Unknown question type: ${question.type}`);
        return false;
    }
  } catch (error) {
    console.error(`Error validating answer for question ${question.questionId}:`, error);
    return false;
  }
}

function validateMultipleChoiceAnswer(question: any, userAnswer: any): boolean {
  const options = typeof question.options === 'string'
    ? JSON.parse(question.options)
    : question.options;

  if (!Array.isArray(options)) return false;

  const correctOptions = options.filter((opt: any) => opt.is_correct);

  if (question.singleCorrectAnswer || correctOptions.length === 1) {
    // Single correct answer
    return correctOptions.some((opt: any) => opt.id === userAnswer);
  } else {
    // Multiple correct answers
    const correctIds = correctOptions.map((opt: any) => opt.id);
    const userIds = Array.isArray(userAnswer) ? userAnswer : [userAnswer];
    return correctIds.length === userIds.length &&
           correctIds.every((id: string) => userIds.includes(id));
  }
}

function validateTrueFalseAnswer(question: any, userAnswer: any): boolean {
  const correctAnswer = question.correctAnswer !== undefined
    ? question.correctAnswer
    : question.correct_answer;
  return correctAnswer === userAnswer;
}

function validateShortAnswer(question: any, userAnswer: any): boolean {
  const correctAnswers = question.correctAnswers || question.correct_answers || [];
  if (!Array.isArray(correctAnswers)) return false;

  const userText = userAnswer.toString().toLowerCase().trim();
  const caseSensitive = question.caseSensitive || false;
  const exactMatch = question.exactMatch || false;

  return correctAnswers.some((correct: string) => {
    const correctText = caseSensitive ? correct.trim() : correct.toLowerCase().trim();
    const compareText = caseSensitive ? userText : userText.toLowerCase();

    if (exactMatch) {
      return correctText === compareText;
    } else {
      return correctText.includes(compareText) || compareText.includes(correctText);
    }
  });
}

function validateMatchingAnswer(question: any, userAnswer: any): boolean {
  const correctPairs = typeof question.correctPairs === 'string'
    ? JSON.parse(question.correctPairs)
    : question.correctPairs;

  if (!Array.isArray(correctPairs) || !Array.isArray(userAnswer)) return false;

  return correctPairs.every((correctPair: any) =>
    userAnswer.some((userPair: any) =>
      userPair.stemId === correctPair.stemId && userPair.optionId === correctPair.optionId
    )
  );
}

function validateFillInTheBlankAnswer(question: any, userAnswer: any): boolean {
  const blanks = typeof question.blanks === 'string'
    ? JSON.parse(question.blanks)
    : question.blanks;

  if (!Array.isArray(blanks) || !Array.isArray(userAnswer)) return false;

  return blanks.every((blank: any, index: number) => {
    const userBlankAnswer = userAnswer[index];
    if (!userBlankAnswer) return false;

    return blank.correctAnswers.some((correct: string) =>
      correct.toLowerCase().trim() === userBlankAnswer.toLowerCase().trim()
    );
  });
}

// Get correct answer for audit purposes only (never sent to client)
function getCorrectAnswerForAudit(question: any): any {
  switch (question.type) {
    case 'multiple_choice':
      const options = typeof question.options === 'string'
        ? JSON.parse(question.options)
        : question.options;
      return options?.filter((opt: any) => opt.is_correct).map((opt: any) => opt.id) || [];

    case 'true_false':
      return question.correctAnswer !== undefined ? question.correctAnswer : question.correct_answer;

    case 'short_answer':
      return question.correctAnswers || question.correct_answers || [];

    default:
      return null;
  }
}
