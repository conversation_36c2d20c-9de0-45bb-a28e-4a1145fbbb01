import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { createQuizSession } from '@/lib/quiz-session';
import { checkQuizAttemptLimit, recordQuizAttempt } from '@/lib/rate-limit';
import { auditLogger, AuditAction } from '@/lib/audit-logger';

// Start a new quiz session
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: quizId } = await params;

    // Check rate limiting for quiz attempts
    const rateLimitResult = await checkQuizAttemptLimit(session.user.id);
    if (!rateLimitResult.success) {
      const errorMessage = rateLimitResult.blocked 
        ? `You are temporarily blocked from starting quizzes. Try again after ${rateLimitResult.blockUntil?.toLocaleTimeString()}`
        : `Daily quiz limit reached. You can attempt ${rateLimitResult.remaining} more quiz(es). Limit resets at ${rateLimitResult.resetTime.toLocaleTimeString()}`;
      
      return NextResponse.json(
        { 
          error: errorMessage,
          rateLimitInfo: {
            remaining: rateLimitResult.remaining,
            resetTime: rateLimitResult.resetTime,
            blocked: rateLimitResult.blocked,
            blockUntil: rateLimitResult.blockUntil
          }
        },
        { status: 429 }
      );
    }

    // Get quiz details
    const quiz = await db.quiz.findUnique({
      where: { id: quizId },
      select: {
        id: true,
        title: true,
        isPublished: true,
        content: true
      }
    });

    if (!quiz) {
      return NextResponse.json(
        { error: 'Quiz not found' },
        { status: 404 }
      );
    }

    if (!quiz.isPublished) {
      return NextResponse.json(
        { error: 'Quiz is not published' },
        { status: 403 }
      );
    }

    // Extract time limit from quiz content
    const quizContent = quiz.content as any;
    const timeLimit = quizContent?.settings?.time_limit_minutes;

    // Create quiz session
    const quizSession = await createQuizSession(
      session.user.id,
      quizId,
      timeLimit
    );

    // Record quiz attempt for rate limiting
    await recordQuizAttempt(session.user.id, quizId);

    // Log quiz start
    await auditLogger.logQuizEvent(
      AuditAction.QUIZ_STARTED,
      session.user.id,
      quizId,
      {
        sessionToken: quizSession.sessionToken,
        timeLimit,
        expiresAt: quizSession.expiresAt
      },
      req
    );

    return NextResponse.json({
      success: true,
      session: {
        sessionToken: quizSession.sessionToken,
        startedAt: quizSession.startedAt,
        expiresAt: quizSession.expiresAt,
        timeLimit
      },
      quiz: {
        id: quiz.id,
        title: quiz.title
      }
    });

  } catch (error) {
    console.error('Error starting quiz session:', error);
    
    // Log error for monitoring
    if (error instanceof Error) {
      await auditLogger.log({
        action: AuditAction.ERROR_OCCURRED,
        resource: `quiz_start:${(await params).id}`,
        severity: 'HIGH' as any,
        metadata: {
          error: error.message,
          stack: error.stack
        }
      });
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
