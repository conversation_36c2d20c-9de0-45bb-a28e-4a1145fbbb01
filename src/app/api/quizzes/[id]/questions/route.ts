import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// Get all questions for a quiz
export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id: quizId } = await params;

    // Verify access
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found" },
        { status: 404 }
      );
    }

    // If quiz is not published, only the creator can view it
    if (!quiz.isPublished && (!session || quiz.creatorId !== session.user.id)) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const questions = await db.question.findMany({
      where: {
        quizId: quizId,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    return NextResponse.json(questions);
  } catch (error) {
    console.error("Error fetching questions:", error);
    return NextResponse.json(
      { message: "An error occurred while fetching questions" },
      { status: 500 }
    );
  }
}

// Add a new question to a quiz
export async function POST(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  console.log("🚀 POST /api/quizzes/[id]/questions - Starting request");

  try {
    const session = await getServerSession(authOptions);
    console.log("👤 Session:", session ? `${session.user.email} (${session.user.role})` : "No session");

    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (session.user.role !== "admin") {
      return NextResponse.json(
        { message: "Forbidden: Only administrators can modify quizzes" },
        { status: 403 }
      );
    }

    const { id: quizId } = await params;
    const body = await req.json();

    console.log("Adding question to quiz:", quizId);
    console.log("Question data received:", JSON.stringify(body, null, 2));

    // Verify quiz exists and admin can edit any quiz
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
        // Remove ownership restriction for admin users
        // creatorId: session.user.id,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found" },
        { status: 404 }
      );
    }

    // Process the question data
    let questionData = { ...body };

    // Handle text field - keep as string for simple cases, convert to JSON for complex cases
    if (typeof questionData.text === "string") {
      // Keep as string - this is the most common case
      // No conversion needed
    } else if (questionData.text && typeof questionData.text === "object") {
      // Convert object to JSON string for storage
      questionData.text = JSON.stringify(questionData.text);
    } else {
      // Fallback for undefined/null text
      questionData.text = questionData.text || "";
    }

    // Handle type-specific data transformation
    if (questionData.type === "multiple_choice") {
      // Remove frontend-specific fields that aren't in the database schema
      const { single_correct_answer, scoring_method, ...cleanData } = questionData;
      questionData = cleanData;

      // Add the single_correct_answer and scoring_method to the options JSON if they exist
      if (questionData.options && (single_correct_answer !== undefined || scoring_method !== undefined)) {
        const optionsData = typeof questionData.options === "string"
          ? JSON.parse(questionData.options)
          : questionData.options;

        if (single_correct_answer !== undefined) {
          optionsData.single_correct_answer = single_correct_answer;
        }
        if (scoring_method !== undefined) {
          optionsData.scoring_method = scoring_method;
        }

        questionData.options = optionsData;
      }
    }

    // Convert other JSON fields
    for (const field of ["options", "correctAnswer", "correctAnswers", "stems", "correctPairs", "textTemplate", "blanks", "hint"]) {
      if (questionData[field] && typeof questionData[field] !== "string") {
        questionData[field] = JSON.stringify(questionData[field]);
      }
    }

    // Ensure questionId is set if not provided
    if (!questionData.questionId) {
      questionData.questionId = `question_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Add the question
    console.log("Final question data for database:", JSON.stringify(questionData, null, 2));

    const question = await db.question.create({
      data: {
        ...questionData,
        quizId: quizId,
      },
    });

    console.log("Question created successfully:", question.id);

    return NextResponse.json(question, { status: 201 });
  } catch (error) {
    console.error("Error adding question:", error);
    return NextResponse.json(
      { message: "An error occurred while adding the question" },
      { status: 500 }
    );
  }
}
