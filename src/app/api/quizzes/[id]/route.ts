import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// Get a specific quiz
export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id: quizId } = await params;

    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
      },
      include: {
        questions: {
          select: {
            id: true,
            questionId: true,
            type: true,
            text: true,
            points: true,
            feedbackCorrect: true,
            feedbackIncorrect: true,
            explanation: true,
            media: true,
            codeSnippet: true,
            hint: true,
            dependsOn: true,
            createdAt: true,
            updatedAt: true,
            // SECURITY: Include options but will sanitize them to remove is_correct flags
            options: true,
            correctAnswer: false,
            correctAnswers: false,
            stems: false,
            correctPairs: false,
            textTemplate: false,
            blanks: false,
            caseSensitive: false,
            trimWhitespace: false,
            exactMatch: false,
            minWordCount: false,
            maxWordCount: false,
            guidelines: false,
          }
        },
        questionPools: {
          include: {
            questions: {
              select: {
                id: true,
                questionId: true,
                type: true,
                text: true,
                points: true,
                feedbackCorrect: true,
                feedbackIncorrect: true,
                explanation: true,
                media: true,
                codeSnippet: true,
                hint: true,
                dependsOn: true,
                createdAt: true,
                updatedAt: true,
                // SECURITY: Include options but will sanitize them
                options: true,
                correctAnswer: false,
                correctAnswers: false,
                stems: false,
                correctPairs: false,
                textTemplate: false,
                blanks: false,
                caseSensitive: false,
                trimWhitespace: false,
                exactMatch: false,
                minWordCount: false,
                maxWordCount: false,
                guidelines: false,
              }
            },
          },
        },
        selectionRules: true,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found" },
        { status: 404 }
      );
    }

    // If quiz is not published, only the creator can view it
    if (!quiz.isPublished && (!session || quiz.creatorId !== session.user.id)) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // SECURITY: Sanitize questions to remove any answer data that might be in JSON fields
    if (quiz) {
      const sanitizedQuiz = {
        ...quiz,
        questions: quiz.questions.map(question => sanitizeQuestionForClient(question)),
        questionPools: quiz.questionPools.map(pool => ({
          ...pool,
          questions: pool.questions.map(question => sanitizeQuestionForClient(question))
        }))
      };
      return NextResponse.json(sanitizedQuiz);
    }

    return NextResponse.json(quiz);
  } catch (error) {
    console.error("Error fetching quiz:", error);
    return NextResponse.json(
      { message: "An error occurred while fetching the quiz" },
      { status: 500 }
    );
  }
}

// SECURITY: Function to sanitize question data for client consumption
function sanitizeQuestionForClient(question: any) {
  const sanitized = { ...question };

  // For multiple choice questions, we need to show options but without is_correct flags
  if (question.type === 'multiple_choice' && question.options) {
    try {
      const options = typeof question.options === 'string'
        ? JSON.parse(question.options)
        : question.options;

      if (Array.isArray(options)) {
        sanitized.options = options.map((opt: any) => ({
          id: opt.id,
          text: opt.text,
          feedback: opt.feedback,
          // SECURITY: Never include is_correct flag
        }));
      }
    } catch (error) {
      console.error('Error parsing options for question:', question.questionId, error);
      sanitized.options = [];
    }
  }

  // For matching questions, show stems and options but not correct pairs
  if (question.type === 'matching') {
    try {
      if (question.stems) {
        const stems = typeof question.stems === 'string'
          ? JSON.parse(question.stems)
          : question.stems;
        sanitized.stems = stems;
      }

      if (question.options) {
        const options = typeof question.options === 'string'
          ? JSON.parse(question.options)
          : question.options;
        sanitized.options = options;
      }

      // SECURITY: Never include correctPairs
      delete sanitized.correctPairs;
    } catch (error) {
      console.error('Error parsing matching question data:', question.questionId, error);
    }
  }

  // For fill-in-the-blank, show template but not blanks with answers
  if (question.type === 'fill_in_the_blank' && question.textTemplate) {
    sanitized.textTemplate = question.textTemplate;
    // SECURITY: Never include blanks with correct answers
    delete sanitized.blanks;
  }

  return sanitized;
}

// Update a quiz
export async function PATCH(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (session.user.role !== "admin") {
      return NextResponse.json(
        { message: "Forbidden: Only administrators can modify quizzes" },
        { status: 403 }
      );
    }

    const { id: quizId } = await params;
    const body = await req.json();

    // Verify quiz exists and admin can edit any quiz
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
        // Remove ownership restriction for admin users
        // creatorId: session.user.id,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found" },
        { status: 404 }
      );
    }

    // Update the quiz
    const updatedQuiz = await db.quiz.update({
      where: {
        id: quizId,
      },
      data: body,
    });

    return NextResponse.json(updatedQuiz);
  } catch (error) {
    console.error("Error updating quiz:", error);
    return NextResponse.json(
      { message: "An error occurred while updating the quiz" },
      { status: 500 }
    );
  }
}

// Delete a quiz
export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (session.user.role !== "admin") {
      return NextResponse.json(
        { message: "Forbidden: Only administrators can delete quizzes" },
        { status: 403 }
      );
    }

    const { id: quizId } = await params;

    // Verify quiz exists and admin can delete any quiz
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
        // Remove ownership restriction for admin users
        // creatorId: session.user.id,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found" },
        { status: 404 }
      );
    }

    // Delete the quiz
    await db.quiz.delete({
      where: {
        id: quizId,
      },
    });

    return NextResponse.json(
      { message: "Quiz deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting quiz:", error);
    return NextResponse.json(
      { message: "An error occurred while deleting the quiz" },
      { status: 500 }
    );
  }
}
