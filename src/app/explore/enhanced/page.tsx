"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Search, Filter, Clock, Users, Target, Shield, Code, Bug, ChevronLeft, ChevronRight } from "lucide-react";

interface Quiz {
  id: string;
  title: string;
  description: string;
  tags: string[];
  timeLimit: number;
  category: { name: string; slug: string } | null;
  difficulty: { name: string; level: number; color: string } | null;
  creator: { name: string };
  metadata: {
    questionTypes: string[];
    cveReferences: string[];
    toolsUsed: string[];
    realWorldQuestions: number;
    totalQuestions: number;
    totalResponses: number;
  };
}

interface SearchResponse {
  quizzes: Quiz[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  facets: {
    categories: Array<{ key: string; count: number }>;
    difficulties: Array<{ key: string; count: number }>;
    tools: Array<{ key: string; count: number }>;
    tags: Array<{ key: string; count: number }>;
    specialFilters: {
      cveCount: number;
      realWorldCount: number;
    };
  };
}

export default function EnhancedExplorePage() {
  const { data: session } = useSession();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedDifficulty, setSelectedDifficulty] = useState("");
  const [selectedTools, setSelectedTools] = useState<string[]>([]);
  const [realWorldOnly, setRealWorldOnly] = useState(false);
  const [cveOnly, setCveOnly] = useState(false);
  const [sortBy, setSortBy] = useState("updatedAt");
  const [currentPage, setCurrentPage] = useState(1);
  const [searchResults, setSearchResults] = useState<SearchResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    performSearch();
  }, [searchQuery, selectedCategory, selectedDifficulty, selectedTools, realWorldOnly, cveOnly, sortBy, currentPage]);

  const performSearch = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        q: searchQuery,
        page: currentPage.toString(),
        limit: "12",
        sortBy,
        sortOrder: "desc",
      });

      if (selectedCategory) params.append("category", selectedCategory);
      if (selectedDifficulty) params.append("difficulty", selectedDifficulty);
      if (selectedTools.length > 0) params.append("tools", selectedTools.join(","));
      if (realWorldOnly) params.append("realWorld", "true");
      if (cveOnly) params.append("cve", "true");

      const response = await fetch(`/api/quizzes/search?${params}`);
      const data = await response.json();
      setSearchResults(data);
    } catch (error) {
      console.error("Search error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleToolToggle = (tool: string) => {
    setSelectedTools(prev =>
      prev.includes(tool)
        ? prev.filter(t => t !== tool)
        : [...prev, tool]
    );
  };

  const clearFilters = () => {
    setSearchQuery("");
    setSelectedCategory("");
    setSelectedDifficulty("");
    setSelectedTools([]);
    setRealWorldOnly(false);
    setCveOnly(false);
    setCurrentPage(1);
  };

  const getDifficultyColor = (difficulty: Quiz['difficulty']) => {
    if (!difficulty) return "bg-gray-500";
    return difficulty.color || "#6b7280";
  };

  return (
    <div className="flex flex-col min-h-screen">
      {/* Navigation */}
      <header className="border-b">
        <div className="container mx-auto px-4 flex h-16 items-center justify-between">
          <div className="flex items-center gap-6">
            <Link href="/" className="text-xl font-bold">
              QuizFlow
            </Link>
          </div>
          <div className="flex items-center gap-4">
            {session ? (
              <Button asChild>
                <Link href="/dashboard">Dashboard</Link>
              </Button>
            ) : (
              <div className="flex items-center gap-4">
                <Button variant="outline" asChild>
                  <Link href="/auth/login">Sign In</Link>
                </Button>
                <Button asChild>
                  <Link href="/auth/register">Sign Up</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </header>

      <main className="flex-1 py-8">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-4">Explore Cybersecurity Quizzes</h1>
            <p className="text-muted-foreground">
              Discover interactive quizzes based on real-world scenarios, CVEs, and industry best practices.
            </p>
          </div>

          {/* Search and Filters */}
          <div className="mb-8 space-y-4">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search quizzes, CVEs, tools, or topics..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Quick Filters */}
            <div className="flex flex-wrap gap-4 items-center">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                Filters
              </Button>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Sort by..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="updatedAt">Recently Updated</SelectItem>
                  <SelectItem value="createdAt">Recently Created</SelectItem>
                  <SelectItem value="popularity">Most Popular</SelectItem>
                  <SelectItem value="difficulty">Difficulty</SelectItem>
                  <SelectItem value="title">Title A-Z</SelectItem>
                </SelectContent>
              </Select>

              {searchResults && (
                <div className="text-sm text-muted-foreground">
                  {searchResults.pagination.totalCount} quizzes found
                </div>
              )}
            </div>
          </div>

          <div className="flex gap-8">
            {/* Sidebar Filters */}
            {showFilters && searchResults && (
              <div className="w-80 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Filters</CardTitle>
                    <Button variant="ghost" size="sm" onClick={clearFilters}>
                      Clear All
                    </Button>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Special Filters */}
                    <div>
                      <h4 className="font-medium mb-3">Special Categories</h4>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="realWorld"
                            checked={realWorldOnly}
                            onCheckedChange={(checked) => setRealWorldOnly(checked === true)}
                          />
                          <Label htmlFor="realWorld" className="flex items-center gap-2">
                            <Shield className="h-4 w-4" />
                            Real-World Scenarios ({searchResults.facets.specialFilters.realWorldCount})
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="cve"
                            checked={cveOnly}
                            onCheckedChange={(checked) => setCveOnly(checked === true)}
                          />
                          <Label htmlFor="cve" className="flex items-center gap-2">
                            <Bug className="h-4 w-4" />
                            CVE-Based ({searchResults.facets.specialFilters.cveCount})
                          </Label>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* Categories */}
                    <div>
                      <h4 className="font-medium mb-3">Categories</h4>
                      <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                        <SelectTrigger>
                          <SelectValue placeholder="All categories" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">All categories</SelectItem>
                          {searchResults.facets.categories.map((cat) => (
                            <SelectItem key={cat.key} value={cat.key}>
                              {cat.key.replace('-', ' ')} ({cat.count})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Difficulty */}
                    <div>
                      <h4 className="font-medium mb-3">Difficulty</h4>
                      <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
                        <SelectTrigger>
                          <SelectValue placeholder="All difficulties" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">All difficulties</SelectItem>
                          {searchResults.facets.difficulties.map((diff) => (
                            <SelectItem key={diff.key} value={diff.key}>
                              {diff.key} ({diff.count})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Top Tools */}
                    <div>
                      <h4 className="font-medium mb-3">Tools & Technologies</h4>
                      <div className="space-y-2 max-h-48 overflow-y-auto">
                        {searchResults.facets.tools.slice(0, 10).map((tool) => (
                          <div key={tool.key} className="flex items-center space-x-2">
                            <Checkbox
                              id={tool.key}
                              checked={selectedTools.includes(tool.key)}
                              onCheckedChange={() => handleToolToggle(tool.key)}
                            />
                            <Label htmlFor={tool.key} className="text-sm">
                              {tool.key} ({tool.count})
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Quiz Grid */}
            <div className="flex-1">
              {loading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(6)].map((_, i) => (
                    <Card key={i} className="animate-pulse">
                      <CardHeader>
                        <div className="h-6 bg-muted rounded w-3/4"></div>
                        <div className="h-4 bg-muted rounded w-full"></div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="h-4 bg-muted rounded w-1/2"></div>
                          <div className="h-10 bg-muted rounded"></div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : searchResults && searchResults.quizzes.length > 0 ? (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    {searchResults.quizzes.map((quiz) => (
                      <Card key={quiz.id} className="hover:shadow-lg transition-shadow">
                        <CardHeader>
                          <div className="flex justify-between items-start mb-2">
                            <CardTitle className="text-lg line-clamp-2">{quiz.title}</CardTitle>
                            {quiz.difficulty && (
                              <Badge
                                style={{ backgroundColor: getDifficultyColor(quiz.difficulty) }}
                                className="text-white"
                              >
                                {quiz.difficulty.name}
                              </Badge>
                            )}
                          </div>
                          <CardDescription className="line-clamp-2">
                            {quiz.description}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            {/* Category and Stats */}
                            <div className="flex justify-between items-center text-sm">
                              {quiz.category && (
                                <Badge variant="outline">
                                  {quiz.category.name}
                                </Badge>
                              )}
                              <div className="flex items-center gap-4 text-muted-foreground">
                                <span className="flex items-center gap-1">
                                  <Target className="h-3 w-3" />
                                  {quiz.metadata.totalQuestions}
                                </span>
                                <span className="flex items-center gap-1">
                                  <Users className="h-3 w-3" />
                                  {quiz.metadata.totalResponses}
                                </span>
                                {quiz.timeLimit && (
                                  <span className="flex items-center gap-1">
                                    <Clock className="h-3 w-3" />
                                    {quiz.timeLimit}m
                                  </span>
                                )}
                              </div>
                            </div>

                            {/* Special Indicators */}
                            <div className="flex flex-wrap gap-2">
                              {quiz.metadata.realWorldQuestions > 0 && (
                                <Badge variant="secondary" className="text-xs">
                                  <Shield className="h-3 w-3 mr-1" />
                                  Real-World
                                </Badge>
                              )}
                              {quiz.metadata.cveReferences.length > 0 && (
                                <Badge variant="secondary" className="text-xs">
                                  <Bug className="h-3 w-3 mr-1" />
                                  CVE-Based
                                </Badge>
                              )}
                              {quiz.metadata.toolsUsed.length > 0 && (
                                <Badge variant="secondary" className="text-xs">
                                  <Code className="h-3 w-3 mr-1" />
                                  {quiz.metadata.toolsUsed.length} Tools
                                </Badge>
                              )}
                            </div>

                            {/* Tags */}
                            <div className="flex flex-wrap gap-1">
                              {quiz.tags.slice(0, 3).map((tag) => (
                                <Badge key={tag} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                              {quiz.tags.length > 3 && (
                                <Badge variant="outline" className="text-xs">
                                  +{quiz.tags.length - 3}
                                </Badge>
                              )}
                            </div>

                            <Button asChild className="w-full">
                              <Link href={`/quiz/${quiz.id}`}>
                                Start Quiz
                              </Link>
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {/* Pagination */}
                  {searchResults.pagination.totalPages > 1 && (
                    <div className="flex justify-center gap-2">
                      <Button
                        variant="outline"
                        disabled={!searchResults.pagination.hasPrev}
                        onClick={() => setCurrentPage(currentPage - 1)}
                      >
                        <ChevronLeft className="h-4 w-4 mr-1" />
                        Previous
                      </Button>
                      <span className="flex items-center px-4">
                        Page {searchResults.pagination.page} of {searchResults.pagination.totalPages}
                      </span>
                      <Button
                        variant="outline"
                        disabled={!searchResults.pagination.hasNext}
                        onClick={() => setCurrentPage(currentPage + 1)}
                      >
                        Next
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </Button>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-12">
                  <h3 className="text-lg font-medium mb-2">No quizzes found</h3>
                  <p className="text-muted-foreground mb-4">
                    Try adjusting your search criteria or filters.
                  </p>
                  <Button onClick={clearFilters}>Clear Filters</Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="py-8 border-t">
        <div className="container mx-auto px-4 text-center">
          <p className="text-muted-foreground">
            &copy; {new Date().getFullYear()} QuizFlow. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
