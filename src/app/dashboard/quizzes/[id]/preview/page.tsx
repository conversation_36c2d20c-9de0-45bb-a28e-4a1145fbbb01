import { redirect, notFound } from "next/navigation";
import Link from "next/link";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, Users, Target, Tag } from "lucide-react";
import QuizPreviewClient from "@/components/quiz/preview/QuizPreviewClient";

// Safe JSON parsing function with error handling
function safeJsonParse(value: any, context: string): any {
  if (typeof value !== 'string') {
    return value; // Already parsed or not a string
  }

  try {
    return JSON.parse(value);
  } catch (error) {
    console.error(`Failed to parse JSON for ${context}:`, error);
    console.error(`Value was:`, value);

    // Return a safe default based on context
    if (context.includes('options')) {
      return [];
    } else if (context.includes('correctAnswers') || context.includes('correctAnswer')) {
      return [];
    } else if (context.includes('stems') || context.includes('correctPairs')) {
      return [];
    } else if (context.includes('blanks')) {
      return [];
    } else if (context.includes('hint')) {
      return [];
    } else if (context.includes('text')) {
      return { default: 'Question text' };
    } else {
      return null;
    }
  }
}

interface QuizPreviewPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function QuizPreviewPage({ params }: QuizPreviewPageProps) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/auth/login");
  }

  const { id } = await params;

  // Get the quiz with all related data
  const quiz = await db.quiz.findUnique({
    where: {
      id,
    },
    include: {
      creator: {
        select: {
          name: true,
          email: true,
        },
      },
      questions: {
        orderBy: {
          createdAt: 'asc'
        }
      },
      questionPools: {
        include: {
          questions: true,
        },
      },
      selectionRules: true,
    },
  });

  if (!quiz) {
    notFound();
  }

  // Check if user has permission to preview this quiz
  // Allow if user is the creator or if user is admin
  const canPreview = quiz.creatorId === session.user.id || session.user.role === 'admin';

  if (!canPreview) {
    redirect("/dashboard/quizzes");
  }

  // Convert quiz data to QFJSON format for preview
  const qfjsonQuiz = {
    quiz: {
      metadata: {
        format_version: "1.0",
        quiz_id: quiz.quizId,
        title: quiz.title,
        description: quiz.description || "",
        author: quiz.creator.name || "Unknown",
        creation_date: quiz.createdAt.toISOString(),
        tags: quiz.tags ?
          (typeof quiz.tags === 'string' ? quiz.tags.split(',').map(tag => tag.trim()) : []) : [],
        passing_score_percentage: quiz.passingScore || 70,
        time_limit_minutes: quiz.timeLimit || 0,
        markup_format: "plain_text" as const,
        locale: "en-US",
      },
      questions: quiz.questions.map((question, index) => {
        // Parse question text safely
        const parsedText = safeJsonParse(question.text, `question ${question.questionId} text`);
        const questionText = typeof parsedText === 'string'
          ? parsedText
          : parsedText?.default || 'Question text';

        const baseQuestion = {
          question_id: question.questionId || question.id,
          type: question.type,
          text: questionText,
          points: question.points || 1,
          explanation: question.explanation ? safeJsonParse(question.explanation, `question ${question.questionId} explanation`) : undefined,
          hint: question.hint ? safeJsonParse(question.hint, `question ${question.questionId} hint`) : undefined,
        };

        // Handle different question types
        if (question.type === 'multiple_choice') {
          const options = safeJsonParse(question.options, `question ${question.questionId} options`);
          const correctAnswer = safeJsonParse(question.correctAnswer, `question ${question.questionId} correctAnswer`);

          // Handle both single and multiple correct answers
          const correctAnswerArray = Array.isArray(correctAnswer) ? correctAnswer : [correctAnswer];

          return {
            ...baseQuestion,
            type: "multiple_choice" as const,
            single_correct_answer: correctAnswerArray.length === 1,
            options: options.map((option: any, optionIndex: number) => {
              // Handle both string options and object options
              if (typeof option === 'string') {
                return {
                  id: `option_${optionIndex}`,
                  text: option,
                  is_correct: correctAnswerArray.includes(optionIndex),
                };
              } else {
                return {
                  id: option.id || `option_${optionIndex}`,
                  text: option.text || option,
                  is_correct: option.is_correct || correctAnswerArray.includes(option.id || optionIndex),
                };
              }
            }),
            scoring_method: "all_or_nothing" as const,
          };
        } else if (question.type === 'true_false') {
          const correctAnswer = safeJsonParse(question.correctAnswer, `question ${question.questionId} correctAnswer`);

          return {
            ...baseQuestion,
            type: "true_false" as const,
            correct_answer: correctAnswer !== null ? correctAnswer : true,
          };
        } else if (question.type === 'short_answer') {
          const correctAnswer = safeJsonParse(question.correctAnswer, `question ${question.questionId} correctAnswer`);
          const correctAnswers = Array.isArray(correctAnswer) ? correctAnswer : [correctAnswer || ''];

          return {
            ...baseQuestion,
            type: "short_answer" as const,
            correct_answers: correctAnswers,
            case_sensitive: false,
            trim_whitespace: true,
            exact_match: false,
          };
        }

        // Default fallback
        return baseQuestion;
      }).filter(Boolean),
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Quiz Preview</h1>
          <p className="text-muted-foreground mt-2">
            Preview how your quiz will appear to users
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            asChild
          >
            <Link href={`/dashboard/quizzes/${quiz.id}/edit`}>
              Back to Edit
            </Link>
          </Button>
          {quiz.isPublished && (
            <Button
              variant="outline"
              asChild
            >
              <Link href={`/quiz/${quiz.id}`}>
                View Live Quiz
              </Link>
            </Button>
          )}
        </div>
      </div>

      {/* Quiz Information Card */}
      <Card className="mb-8">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-2xl">{quiz.title}</CardTitle>
              <CardDescription className="mt-2 text-base">
                {quiz.description}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={quiz.isPublished ? "default" : "secondary"}>
                {quiz.isPublished ? "Published" : "Draft"}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                {quiz.questions.length} Question{quiz.questions.length !== 1 ? 's' : ''}
              </span>
            </div>
            {quiz.timeLimit && (
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{quiz.timeLimit} minutes</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{quiz.passingScore}% to pass</span>
            </div>
            {quiz.tags && (
              <div className="flex items-center gap-2">
                <Tag className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  {typeof quiz.tags === 'string'
                    ? `${quiz.tags.split(',').length} tag${quiz.tags.split(',').length !== 1 ? 's' : ''}`
                    : 'Tags available'
                  }
                </span>
              </div>
            )}
          </div>

          {quiz.tags && typeof quiz.tags === 'string' && (
            <div className="mt-4">
              <div className="flex flex-wrap gap-2">
                {quiz.tags.split(',').map((tag, index) => (
                  <Badge key={index} variant="outline">
                    {tag.trim()}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Preview Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
        <div className="flex items-start gap-3">
          <div className="bg-blue-100 rounded-full p-1">
            <svg className="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h3 className="font-medium text-blue-900">Preview Mode</h3>
            <p className="text-blue-700 text-sm mt-1">
              This is how your quiz will appear to users. Your answers won't be saved, and no scores will be recorded.
            </p>
          </div>
        </div>
      </div>

      {/* Quiz Content */}
      {quiz.questions.length > 0 ? (
        <QuizPreviewClient quiz={qfjsonQuiz} />
      ) : (
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <div className="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Questions Added</h3>
              <p className="text-gray-500 mb-6">
                Add some questions to your quiz to see the preview.
              </p>
              <Button asChild>
                <Link href={`/dashboard/quizzes/${quiz.id}/edit?tab=questions`}>
                  Add Questions
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
