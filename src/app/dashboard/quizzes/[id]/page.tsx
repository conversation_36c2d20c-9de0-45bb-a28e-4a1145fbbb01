import { redirect, notFound } from "next/navigation";
import <PERSON> from "next/link";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, Users, Target, Tag, Edit, Eye, BarChart3, Play } from "lucide-react";
import { formatDate } from "@/lib/utils";

interface QuizPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function QuizPage({ params }: QuizPageProps) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/auth/login");
  }

  const { id } = await params;

  // Get the quiz with all related data
  const quiz = await db.quiz.findUnique({
    where: {
      id,
    },
    include: {
      creator: {
        select: {
          name: true,
          email: true,
        },
      },
      category: {
        select: {
          name: true,
        },
      },
      difficulty: {
        select: {
          name: true,
        },
      },
      questions: {
        orderBy: {
          createdAt: 'asc'
        }
      },
      questionPools: {
        include: {
          questions: true,
        },
      },
      selectionRules: true,
      _count: {
        select: {
          questions: true,
          responses: true,
        },
      },
    },
  });

  if (!quiz) {
    notFound();
  }

  // Check if user has permission to view this quiz
  const canEdit = session.user.role === "admin" || quiz.creatorId === session.user.id;
  const canView = quiz.isPublished || canEdit;

  if (!canView) {
    notFound();
  }

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <div className="flex items-center gap-4 mb-2">
            <h1 className="text-3xl font-bold">{quiz.title}</h1>
            <Badge variant={quiz.isPublished ? "default" : "secondary"}>
              {quiz.isPublished ? "Published" : "Draft"}
            </Badge>
          </div>
          <p className="text-muted-foreground">
            Created by {quiz.creator?.name || 'Unknown'} • Updated {formatDate(quiz.updatedAt)}
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Button variant="outline" asChild>
            <Link href="/dashboard/quizzes">
              Back to Quizzes
            </Link>
          </Button>
        </div>
      </div>

      {/* Quiz Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="bg-blue-100 p-3 rounded-lg">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Questions</p>
                <p className="text-2xl font-bold">{quiz._count.questions}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="bg-green-100 p-3 rounded-lg">
                <Target className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Passing Score</p>
                <p className="text-2xl font-bold">{quiz.passingScore || 70}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="bg-purple-100 p-3 rounded-lg">
                <Clock className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Time Limit</p>
                <p className="text-2xl font-bold">
                  {quiz.timeLimit ? `${quiz.timeLimit}m` : "None"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="bg-orange-100 p-3 rounded-lg">
                <BarChart3 className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Responses</p>
                <p className="text-2xl font-bold">{quiz._count.responses}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quiz Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Description */}
          <Card>
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                {quiz.description || "No description provided."}
              </p>
            </CardContent>
          </Card>

          {/* Questions Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Questions ({quiz._count.questions})</CardTitle>
              <CardDescription>
                Preview of questions in this quiz
              </CardDescription>
            </CardHeader>
            <CardContent>
              {quiz.questions.length > 0 ? (
                <div className="space-y-4">
                  {quiz.questions.slice(0, 3).map((question, index) => (
                    <div key={question.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <span className="text-sm font-medium text-muted-foreground">
                          Question {index + 1}
                        </span>
                        <Badge variant="outline">{question.type}</Badge>
                      </div>
                      <p className="font-medium">
                        {typeof question.text === "string"
                          ? question.text
                          : "Question content"}
                      </p>
                      <p className="text-sm text-muted-foreground mt-2">
                        {question.points} points
                      </p>
                    </div>
                  ))}
                  {quiz.questions.length > 3 && (
                    <p className="text-sm text-muted-foreground text-center py-4">
                      And {quiz.questions.length - 3} more questions...
                    </p>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No questions added yet.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {canEdit && (
                <>
                  <Button className="w-full" asChild>
                    <Link href={`/dashboard/quizzes/${quiz.id}/edit`}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Quiz
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full" asChild>
                    <Link href={`/dashboard/quizzes/${quiz.id}/preview`}>
                      <Eye className="h-4 w-4 mr-2" />
                      Preview Quiz
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full" asChild>
                    <Link href={`/dashboard/quizzes/${quiz.id}/analytics`}>
                      <BarChart3 className="h-4 w-4 mr-2" />
                      View Analytics
                    </Link>
                  </Button>
                </>
              )}
              {quiz.isPublished && (
                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/quiz/${quiz.id}`}>
                    <Play className="h-4 w-4 mr-2" />
                    Take Quiz
                  </Link>
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Quiz Info */}
          <Card>
            <CardHeader>
              <CardTitle>Quiz Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium">Category</p>
                <p className="text-sm text-muted-foreground">
                  {quiz.category?.name || "Uncategorized"}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium">Difficulty</p>
                <p className="text-sm text-muted-foreground">
                  {quiz.difficulty?.name || "Not specified"}
                </p>
              </div>
              {quiz.tags && quiz.tags.length > 0 && (
                <div>
                  <p className="text-sm font-medium mb-2">Tags</p>
                  <div className="flex flex-wrap gap-2">
                    {quiz.tags.map((tag: string, index: number) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        <Tag className="h-3 w-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              <div>
                <p className="text-sm font-medium">Created</p>
                <p className="text-sm text-muted-foreground">
                  {formatDate(quiz.createdAt)}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
