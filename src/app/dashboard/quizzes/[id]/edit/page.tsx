import { notFound } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import QuizEditor from "@/components/quiz/editor/QuizEditor";

interface QuizEditPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function QuizEditPage({ params }: QuizEditPageProps) {
  const session = await getServerSession(authOptions);

  if (!session) {
    console.log('No session found, redirecting to login');
    notFound();
  }

  // Check if user is admin
  if (session.user.role !== "admin") {
    console.log(`User role ${session.user.role} is not admin, access denied`);
    notFound();
  }

  const { id } = await params;
  console.log(`Attempting to edit quiz: ${id} for user: ${session.user.id} (${session.user.email})`);

  // First, check if quiz exists at all
  const quizExists = await db.quiz.findUnique({
    where: { id },
    select: { id: true, title: true, creatorId: true, isPublished: true }
  });

  if (!quizExists) {
    console.log(`Quiz ${id} not found in database`);
    notFound();
  }

  console.log(`Quiz found: ${quizExists.title}, Creator: ${quizExists.creatorId}, Current User: ${session.user.id}`);

  // For admin users, allow editing any quiz (not just their own)
  const quiz = await db.quiz.findUnique({
    where: {
      id,
      // Remove the creatorId restriction for admin users
      // creatorId: session.user.id,
    },
    include: {
      questions: {
        orderBy: {
          createdAt: 'asc'
        }
      },
      questionPools: {
        include: {
          questions: true,
        },
      },
      selectionRules: true,
    },
  });

  if (!quiz) {
    console.log(`Quiz ${id} not found or access denied`);
    notFound();
  }

  console.log(`Successfully loaded quiz for editing: ${quiz.title} with ${quiz.questions.length} questions`);

  return (
    <div className="container mx-auto py-8 px-4">
      <QuizEditor quiz={quiz} />
    </div>
  );
}
