import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { SettingsService } from "@/lib/settings";
import AdminSettingsClient from "@/components/admin/AdminSettingsClient";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default async function AdminSettingsPage() {
  const session = await getServerSession(authOptions);

  if (!session || session.user.role !== 'admin') {
    redirect('/dashboard');
  }

  // Initialize default settings if they don't exist
  await SettingsService.initializeDefaults();

  // Get all settings
  const settings = await SettingsService.getAll();

  // Group settings by category
  const settingsByCategory = settings.reduce((acc, setting) => {
    if (!acc[setting.category]) {
      acc[setting.category] = [];
    }
    acc[setting.category].push(setting);
    return acc;
  }, {} as Record<string, typeof settings>);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">System Settings</h1>
            <p className="text-muted-foreground">Configure QuizFlow features and behavior</p>
          </div>
          <Button asChild variant="outline">
            <Link href="/dashboard/admin">← Back to Admin</Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        {/* Feature Flags Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🚀 Feature Flags
            </CardTitle>
            <CardDescription>
              Enable or disable major platform features
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <div className="flex items-start gap-3">
                <div className="text-yellow-600 mt-0.5">⚠️</div>
                <div>
                  <h4 className="font-medium text-yellow-800">Important Notice</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    Disabling features may affect existing functionality. Some changes require a server restart to take full effect.
                  </p>
                </div>
              </div>
            </div>
            
            <AdminSettingsClient 
              initialSettings={settingsByCategory} 
              category="features"
            />
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🔒 Security Settings
            </CardTitle>
            <CardDescription>
              Configure security and access control
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AdminSettingsClient 
              initialSettings={settingsByCategory} 
              category="security"
            />
          </CardContent>
        </Card>

        {/* UI Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🎨 User Interface
            </CardTitle>
            <CardDescription>
              Customize the user interface and experience
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AdminSettingsClient 
              initialSettings={settingsByCategory} 
              category="ui"
            />
          </CardContent>
        </Card>

        {/* Content Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📝 Content Settings
            </CardTitle>
            <CardDescription>
              Configure default values for quizzes and content
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AdminSettingsClient 
              initialSettings={settingsByCategory} 
              category="content"
            />
          </CardContent>
        </Card>

        {/* System Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              ℹ️ System Information
            </CardTitle>
            <CardDescription>
              Current system status and information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium">Total Settings</p>
                <p className="text-muted-foreground">{settings.length} configured</p>
              </div>
              <div>
                <p className="font-medium">Categories</p>
                <p className="text-muted-foreground">{Object.keys(settingsByCategory).length} categories</p>
              </div>
              <div>
                <p className="font-medium">Last Updated</p>
                <p className="text-muted-foreground">
                  {settings.length > 0 
                    ? new Date(Math.max(...settings.map(s => new Date(s.updatedAt).getTime()))).toLocaleString()
                    : 'Never'
                  }
                </p>
              </div>
              <div>
                <p className="font-medium">Environment</p>
                <p className="text-muted-foreground">{process.env.NODE_ENV || 'development'}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
