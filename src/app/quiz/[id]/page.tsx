import { notFound } from "next/navigation";
import Link from "next/link";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { Button } from "@/components/ui/button";

import { QuizFlowJSON } from "@/types/qfjson";
import QuizClient from "@/components/quiz/QuizClient";

// Safe JSON parsing function with error handling
function safeJsonParse(value: any, context: string): any {
  if (typeof value !== 'string') {
    return value; // Already parsed or not a string
  }

  try {
    return JSON.parse(value);
  } catch (error) {
    console.error(`Failed to parse JSON for ${context}:`, error);
    console.error(`Value was:`, value);

    // Return a safe default based on context
    if (context.includes('options')) {
      return [];
    } else if (context.includes('correctAnswers')) {
      return [];
    } else if (context.includes('stems') || context.includes('correctPairs')) {
      return [];
    } else if (context.includes('blanks')) {
      return [];
    } else if (context.includes('hint')) {
      return [];
    } else {
      return null;
    }
  }
}

interface QuizPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function QuizPage({ params }: QuizPageProps) {
  const session = await getServerSession(authOptions);

  const { id } = await params;

  // Get the quiz
  const quiz = await db.quiz.findUnique({
    where: {
      id,
    },
    include: {
      creator: {
        select: {
          name: true,
        },
      },
      questions: true,
      questionPools: {
        include: {
          questions: true,
        },
      },
      selectionRules: true,
    },
  });

  if (!quiz || (!quiz.isPublished && quiz.creatorId !== session?.user.id)) {
    notFound();
  }

  // Convert the database quiz to QFJSON format
  const qfjson: QuizFlowJSON = {
    quiz: {
      $schema: "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
      metadata: {
        format_version: quiz.formatVersion,
        quiz_id: quiz.quizId,
        title: quiz.title,
        description: quiz.description || undefined,
        author: quiz.creator?.name || quiz.author || undefined,
        creation_date: quiz.creationDate.toISOString(),
        tags: quiz.tags,
        passing_score_percentage: quiz.passingScore || undefined,
        time_limit_minutes: quiz.timeLimit || undefined,
        markup_format: quiz.markupFormat as "markdown" | "html" | "plain_text",
        locale: quiz.locale,
      },
      questions: quiz.questions.map((q: any) => ({
        ...q,
        // Parse JSON fields back to objects with error handling
        options: q.options ? safeJsonParse(q.options, `question ${q.questionId} options`) : undefined,
        correctAnswer: q.correctAnswer ? safeJsonParse(q.correctAnswer, `question ${q.questionId} correctAnswer`) : undefined,
        correctAnswers: q.correctAnswers ? safeJsonParse(q.correctAnswers, `question ${q.questionId} correctAnswers`) : undefined,
        stems: q.stems ? safeJsonParse(q.stems, `question ${q.questionId} stems`) : undefined,
        correctPairs: q.correctPairs ? safeJsonParse(q.correctPairs, `question ${q.questionId} correctPairs`) : undefined,
        textTemplate: q.textTemplate || undefined, // textTemplate should remain as string
        blanks: q.blanks ? safeJsonParse(q.blanks, `question ${q.questionId} blanks`) : undefined,
        hint: q.hint ? safeJsonParse(q.hint, `question ${q.questionId} hint`) : undefined,
        // Remove database-specific fields
        id: undefined,
        quizId: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        questionPoolId: undefined,
        difficultyId: undefined,
        difficulty: undefined,
        topicTags: undefined,
        skillLevel: undefined,
        estimatedTime: undefined,
        realWorldScenario: undefined,
        cveReference: undefined,
        toolsRequired: undefined,
        sourceReference: undefined,
        lastValidated: undefined,
        validatedBy: undefined,
      })) as any,
      question_pools: quiz.questionPools.length > 0
        ? quiz.questionPools.map((pool: any) => ({
            pool_id: pool.poolId,
            title: pool.title || undefined,
            description: pool.description || undefined,
            questions: pool.questions.map((q: any) => ({
              ...q,
              // Parse JSON fields back to objects with error handling
              options: q.options ? safeJsonParse(q.options, `pool question ${q.questionId} options`) : undefined,
              correctAnswer: q.correctAnswer ? safeJsonParse(q.correctAnswer, `pool question ${q.questionId} correctAnswer`) : undefined,
              correctAnswers: q.correctAnswers ? safeJsonParse(q.correctAnswers, `pool question ${q.questionId} correctAnswers`) : undefined,
              stems: q.stems ? safeJsonParse(q.stems, `pool question ${q.questionId} stems`) : undefined,
              correctPairs: q.correctPairs ? safeJsonParse(q.correctPairs, `pool question ${q.questionId} correctPairs`) : undefined,
              textTemplate: q.textTemplate || undefined, // textTemplate should remain as string
              blanks: q.blanks ? safeJsonParse(q.blanks, `pool question ${q.questionId} blanks`) : undefined,
              hint: q.hint ? safeJsonParse(q.hint, `pool question ${q.questionId} hint`) : undefined,
              // Remove database-specific fields
              id: undefined,
              quizId: undefined,
              createdAt: undefined,
              updatedAt: undefined,
              questionPoolId: undefined,
              difficultyId: undefined,
              difficulty: undefined,
              topicTags: undefined,
              skillLevel: undefined,
              estimatedTime: undefined,
              realWorldScenario: undefined,
              cveReference: undefined,
              toolsRequired: undefined,
              sourceReference: undefined,
              lastValidated: undefined,
              validatedBy: undefined,
            })),
          }))
        : undefined,
      selection_rules: quiz.selectionRules.length > 0
        ? quiz.selectionRules.map((rule: any) => ({
            pool_id: rule.poolId,
            select_count: rule.selectCount,
            randomize: rule.randomize,
            shuffle_order: rule.shuffleOrder,
          }))
        : undefined,
    },
  };

  return (
    <div className="flex flex-col min-h-screen">
      {/* Navigation */}
      <header className="border-b">
        <div className="container mx-auto px-4 flex h-16 items-center justify-between">
          <div className="flex items-center gap-6">
            <Link href="/" className="text-xl font-bold">
              QuizFlow
            </Link>
          </div>
          <div className="flex items-center gap-4">
            {session ? (
              <Button asChild>
                <Link href="/dashboard">Dashboard</Link>
              </Button>
            ) : (
              <div className="flex items-center gap-4">
                <Button variant="outline" asChild>
                  <Link href="/auth/login">Sign In</Link>
                </Button>
                <Button asChild>
                  <Link href="/auth/register">Sign Up</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </header>

      <main className="flex-1 py-8">
        <div className="container mx-auto px-4">
          <div className="mb-8">
            <Link href="/explore" className="text-primary hover:underline flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10 19l-7-7m0 0l7-7m-7 7h18"
                />
              </svg>
              Back to Explore
            </Link>
          </div>

          <QuizClient
            quiz={qfjson}
            showAnswerButton={true}
            answerDisplayConfig={{
              showCorrectAnswer: quiz.showCorrectAnswer,
              showUserAnswer: quiz.showUserAnswer,
              showExplanationAfterAnswer: quiz.showExplanationAfterAnswer,
              highlightCorrectAnswer: quiz.highlightCorrectAnswer,
              immediateAnswerFeedback: quiz.immediateAnswerFeedback,
            }}
          />
        </div>
      </main>

      {/* Footer */}
      <footer className="py-8 border-t">
        <div className="container mx-auto px-4 text-center">
          <p className="text-muted-foreground">
            &copy; {new Date().getFullYear()} QuizFlow. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
