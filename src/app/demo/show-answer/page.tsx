"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Question<PERSON>enderer from "@/components/quiz/QuestionRenderer";
import { Question } from "@/types/qfjson";

const demoQuestions: Question[] = [
  {
    question_id: "demo1",
    type: "multiple_choice",
    text: "What is the primary purpose of a firewall in network security?",
    points: 1,
    single_correct_answer: true,
    options: [
      {
        id: "opt1",
        text: "To encrypt all network traffic",
        is_correct: false,
      },
      {
        id: "opt2",
        text: "To control and monitor network traffic based on security rules",
        is_correct: true,
      },
      {
        id: "opt3",
        text: "To provide user authentication",
        is_correct: false,
      },
      {
        id: "opt4",
        text: "To scan for malware",
        is_correct: false,
      }
    ],
    hint: [
      {
        text: "Think about what a physical firewall does in a building.",
        delay_seconds: 0
      },
      {
        text: "Consider the concept of 'allow' and 'deny' rules.",
        delay_seconds: 15
      }
    ],
    explanation: "Firewalls are fundamental network security devices that examine incoming and outgoing network traffic and decide whether to allow or block specific traffic based on a defined set of security rules.",
    feedback_correct: "Excellent! You understand the core function of firewalls.",
    feedback_incorrect: "Not quite. Review the primary function of firewalls in controlling network access."
  } as any,
  {
    question_id: "demo2",
    type: "true_false",
    text: "SQL injection attacks can only target web applications.",
    points: 1,
    correct_answer: false,
    hint: [
      {
        text: "Think about other applications that use databases.",
        delay_seconds: 0
      },
      {
        text: "Consider mobile apps, desktop applications, and APIs.",
        delay_seconds: 20
      }
    ],
    explanation: "SQL injection attacks can target any application that interacts with a SQL database, including web applications, mobile apps, desktop applications, and APIs.",
    feedback_correct: "Correct! SQL injection can affect many types of applications.",
    feedback_incorrect: "Incorrect. SQL injection can target any application that uses SQL databases, not just web applications."
  } as any,
  {
    question_id: "demo3",
    type: "short_answer",
    text: "What does 'XSS' stand for in web security?",
    points: 1,
    correct_answers: ["Cross-Site Scripting", "cross-site scripting"],
    case_sensitive: false,
    trim_whitespace: true,
    hint: [
      {
        text: "It's a type of injection attack that affects web browsers.",
        delay_seconds: 0
      },
      {
        text: "The 'X' represents 'Cross' to avoid confusion with CSS.",
        delay_seconds: 25
      }
    ],
    explanation: "XSS stands for Cross-Site Scripting. The 'X' is used instead of 'C' to avoid confusion with Cascading Style Sheets (CSS).",
    feedback_correct: "Perfect! XSS is indeed Cross-Site Scripting.",
    feedback_incorrect: "The answer is 'Cross-Site Scripting' - a common web vulnerability."
  } as any
];

export default function ShowAnswerDemoPage() {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [showAnswerEnabled, setShowAnswerEnabled] = useState(true);

  const currentQuestion = demoQuestions[currentQuestionIndex];

  const handleAnswerChange = (questionId: string, answer: any) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleNext = () => {
    if (currentQuestionIndex < demoQuestions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleShowAnswer = () => {
    console.log("Show answer clicked for question:", currentQuestion.question_id);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Show Answer Button Demo</CardTitle>
            <CardDescription>
              This demo showcases the configurable "Show Answer" button feature with enhanced hints and explanations.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 mb-4">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={showAnswerEnabled}
                  onChange={(e) => setShowAnswerEnabled(e.target.checked)}
                  className="rounded"
                />
                Enable Show Answer Button
              </label>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Demo Question {currentQuestionIndex + 1} of {demoQuestions.length}</CardTitle>
                <CardDescription>
                  Try the hints, show answer button, and see explanations
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <QuestionRenderer
              question={currentQuestion}
              answer={answers[currentQuestion.question_id]}
              onAnswerChange={(answer) => handleAnswerChange(currentQuestion.question_id, answer)}
              showAnswerButton={showAnswerEnabled}
              showAnswer={showAnswerEnabled}
              showExplanation={true}
            />
          </CardContent>
          <div className="flex justify-between p-6">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentQuestionIndex === 0}
            >
              Previous
            </Button>
            <Button
              onClick={handleNext}
              disabled={currentQuestionIndex === demoQuestions.length - 1}
            >
              Next
            </Button>
          </div>
        </Card>

        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Features Demonstrated</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2">
              <li><strong>Progressive Hints:</strong> Hints appear immediately and after delays</li>
              <li><strong>Show Answer Button:</strong> Configurable button to reveal correct answers</li>
              <li><strong>Answer Display:</strong> Formatted display of correct answers for each question type</li>
              <li><strong>Explanations:</strong> Detailed explanations shown alongside questions</li>
              <li><strong>Interactive Elements:</strong> Clickable hint buttons and answer revelation</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
