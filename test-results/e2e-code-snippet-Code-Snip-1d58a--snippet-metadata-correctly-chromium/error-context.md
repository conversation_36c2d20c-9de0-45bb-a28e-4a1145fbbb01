# Test info

- Name: Code Snippet Functionality >> should display code snippet metadata correctly
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/code-snippet.spec.ts:95:7

# Error details

```
TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator('[data-testid="question-renderer"], .question-container') to be visible

    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/code-snippet.spec.ts:101:16
```

# Page snapshot

```yaml
- banner:
  - link "QuizFlow":
    - /url: /
  - link "Sign In":
    - /url: /auth/login
  - link "Sign Up":
    - /url: /auth/register
- main:
  - link "Back to Explore":
    - /url: /explore
    - img
    - text: Back to Explore
  - heading "Code Security Analysis" [level=3]
  - paragraph: Question 1 of 6
  - paragraph: Time Remaining
  - paragraph: 24:50
  - paragraph: What security vulnerability does this Python code contain?
  - heading "user_auth.py" [level=3]
  - text: Python
  - button "Copy code"
  - paragraph: User authentication function
  - code: "1 import sqlite3 2 3 def get_user(username): 4 conn = sqlite3.connect('users.db') 5 cursor = conn.cursor() 6 query = f\"SELECT * FROM users WHERE username = '{username}'\" 7 cursor.execute(query) 8 return cursor.fetchone()"
  - text: Line 6 highlighted
  - textbox "Type your answer here..."
  - button "🔍 Show Answer"
  - button "Previous" [disabled]
  - button "Next"
- contentinfo:
  - paragraph: © 2025 QuizFlow. All rights reserved.
- alert
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Code Snippet Functionality', () => {
   4 |   test('should display code snippets with syntax highlighting', async ({ page }) => {
   5 |     // Navigate to the code security quiz
   6 |     await page.goto('/quiz/683660ad0c42de016c5eb070');
   7 |     await page.waitForLoadState('networkidle');
   8 |
   9 |     // Wait for the quiz to load
   10 |     await page.waitForSelector('[data-testid="question-renderer"], .question-container', { timeout: 10000 });
   11 |
   12 |     // Look for code snippet elements
   13 |     const codeSnippet = page.locator('.react-syntax-highlighter, pre code, .code-snippet');
   14 |     
   15 |     // Check if code snippet is visible
   16 |     if (await codeSnippet.first().isVisible()) {
   17 |       await expect(codeSnippet.first()).toBeVisible();
   18 |       console.log('✅ Code snippet found and visible');
   19 |
   20 |       // Check for syntax highlighting (should have colored elements)
   21 |       const highlightedElements = page.locator('.react-syntax-highlighter .token, .hljs-keyword, .hljs-string');
   22 |       if (await highlightedElements.first().isVisible()) {
   23 |         await expect(highlightedElements.first()).toBeVisible();
   24 |         console.log('✅ Syntax highlighting detected');
   25 |       }
   26 |
   27 |       // Check for copy button
   28 |       const copyButton = page.locator('button[title="Copy code"], button:has-text("Copy")');
   29 |       if (await copyButton.first().isVisible()) {
   30 |         await expect(copyButton.first()).toBeVisible();
   31 |         console.log('✅ Copy button found');
   32 |       }
   33 |
   34 |       // Check for language badge
   35 |       const languageBadge = page.locator('.badge, .language-badge');
   36 |       if (await languageBadge.first().isVisible()) {
   37 |         await expect(languageBadge.first()).toBeVisible();
   38 |         console.log('✅ Language badge found');
   39 |       }
   40 |
   41 |       // Check for filename display
   42 |       const filename = page.locator('text="user_auth.py", text=".py", text=".js"');
   43 |       if (await filename.first().isVisible()) {
   44 |         await expect(filename.first()).toBeVisible();
   45 |         console.log('✅ Filename display found');
   46 |       }
   47 |     } else {
   48 |       console.log('ℹ️  No code snippet found on this question, checking next question...');
   49 |       
   50 |       // Try to navigate to next question if available
   51 |       const nextButton = page.locator('button:has-text("Next"), button:has-text("Continue")');
   52 |       if (await nextButton.isVisible()) {
   53 |         await nextButton.click();
   54 |         await page.waitForTimeout(1000);
   55 |         
   56 |         // Check again for code snippet
   57 |         const nextCodeSnippet = page.locator('.react-syntax-highlighter, pre code, .code-snippet');
   58 |         if (await nextCodeSnippet.first().isVisible()) {
   59 |           await expect(nextCodeSnippet.first()).toBeVisible();
   60 |           console.log('✅ Code snippet found on next question');
   61 |         }
   62 |       }
   63 |     }
   64 |   });
   65 |
   66 |   test('should handle code snippet copy functionality', async ({ page }) => {
   67 |     // Navigate to the code security quiz
   68 |     await page.goto('/quiz/683660ad0c42de016c5eb070');
   69 |     await page.waitForLoadState('networkidle');
   70 |
   71 |     // Wait for the quiz to load
   72 |     await page.waitForSelector('[data-testid="question-renderer"], .question-container', { timeout: 10000 });
   73 |
   74 |     // Look for copy button
   75 |     const copyButton = page.locator('button[title="Copy code"], button:has-text("Copy")');
   76 |     
   77 |     if (await copyButton.first().isVisible()) {
   78 |       // Grant clipboard permissions
   79 |       await page.context().grantPermissions(['clipboard-read', 'clipboard-write']);
   80 |       
   81 |       // Click copy button
   82 |       await copyButton.first().click();
   83 |       
   84 |       // Check for success message (toast or similar)
   85 |       const successMessage = page.locator('text="copied", text="Copied", .toast, .notification');
   86 |       if (await successMessage.first().isVisible({ timeout: 3000 })) {
   87 |         await expect(successMessage.first()).toBeVisible();
   88 |         console.log('✅ Copy success message displayed');
   89 |       }
   90 |     } else {
   91 |       console.log('ℹ️  No copy button found, skipping copy test');
   92 |     }
   93 |   });
   94 |
   95 |   test('should display code snippet metadata correctly', async ({ page }) => {
   96 |     // Navigate to the code security quiz
   97 |     await page.goto('/quiz/683660ad0c42de016c5eb070');
   98 |     await page.waitForLoadState('networkidle');
   99 |
  100 |     // Wait for the quiz to load
> 101 |     await page.waitForSelector('[data-testid="question-renderer"], .question-container', { timeout: 10000 });
      |                ^ TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
  102 |
  103 |     // Check for code snippet card structure
  104 |     const codeCard = page.locator('.code-snippet, [class*="Card"]').filter({ has: page.locator('pre, code') });
  105 |     
  106 |     if (await codeCard.first().isVisible()) {
  107 |       // Check for header with filename and language
  108 |       const cardHeader = codeCard.first().locator('[class*="CardHeader"], .card-header');
  109 |       if (await cardHeader.isVisible()) {
  110 |         await expect(cardHeader).toBeVisible();
  111 |         console.log('✅ Code snippet header found');
  112 |       }
  113 |
  114 |       // Check for language badge
  115 |       const languageBadge = codeCard.first().locator('.badge, [class*="Badge"]');
  116 |       if (await languageBadge.first().isVisible()) {
  117 |         await expect(languageBadge.first()).toBeVisible();
  118 |         console.log('✅ Language badge in code snippet found');
  119 |       }
  120 |
  121 |       // Check for caption if present
  122 |       const caption = codeCard.first().locator('.caption, [class*="caption"], .text-muted-foreground');
  123 |       if (await caption.first().isVisible()) {
  124 |         await expect(caption.first()).toBeVisible();
  125 |         console.log('✅ Code snippet caption found');
  126 |       }
  127 |     } else {
  128 |       console.log('ℹ️  No code snippet card found');
  129 |     }
  130 |   });
  131 |
  132 |   test('should handle questions without code snippets', async ({ page }) => {
  133 |     // Navigate to the code security quiz
  134 |     await page.goto('/quiz/683660ad0c42de016c5eb070');
  135 |     await page.waitForLoadState('networkidle');
  136 |
  137 |     // Wait for the quiz to load
  138 |     await page.waitForSelector('[data-testid="question-renderer"], .question-container', { timeout: 10000 });
  139 |
  140 |     // Navigate through questions to find one without code snippet
  141 |     let foundQuestionWithoutCode = false;
  142 |     let attempts = 0;
  143 |     const maxAttempts = 6; // We have 6 questions
  144 |
  145 |     while (!foundQuestionWithoutCode && attempts < maxAttempts) {
  146 |       const codeSnippet = page.locator('.react-syntax-highlighter, pre code, .code-snippet');
  147 |       
  148 |       if (!(await codeSnippet.first().isVisible())) {
  149 |         foundQuestionWithoutCode = true;
  150 |         console.log('✅ Found question without code snippet');
  151 |         
  152 |         // Verify the question still renders properly
  153 |         const questionText = page.locator('[data-testid="question-text"], .question-text, .prose');
  154 |         await expect(questionText.first()).toBeVisible();
  155 |         console.log('✅ Question without code snippet renders correctly');
  156 |         
  157 |         break;
  158 |       }
  159 |
  160 |       // Try to go to next question
  161 |       const nextButton = page.locator('button:has-text("Next"), button:has-text("Continue")');
  162 |       if (await nextButton.isVisible()) {
  163 |         await nextButton.click();
  164 |         await page.waitForTimeout(1000);
  165 |         attempts++;
  166 |       } else {
  167 |         break;
  168 |       }
  169 |     }
  170 |
  171 |     if (!foundQuestionWithoutCode) {
  172 |       console.log('ℹ️  All questions appear to have code snippets');
  173 |     }
  174 |   });
  175 | });
  176 |
```