# Test info

- Name: Accessibility Tests >> should handle high contrast mode
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/accessibility.spec.ts:224:7

# Error details

```
Error: expect(received).toBe<PERSON><PERSON><PERSON><PERSON><PERSON>(expected)

Expected: > 0
Received:   0
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/accessibility.spec.ts:243:19
```

# Page snapshot

```yaml
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Home":
      - /url: /landing
    - link "Explore":
      - /url: /explore
    - link "Security Quizzes":
      - /url: /security-quizzes
  - link "Dashboard":
    - /url: /dashboard
- main:
  - img
  - heading "Cybersecurity Training" [level=1]
  - paragraph: Master cybersecurity with hands-on quizzes, real-world scenarios, and practical challenges. From beginner to advanced, build the skills that matter.
  - link "Start Learning":
    - /url: "#featured-quizzes"
  - link "Browse All Quizzes":
    - /url: /explore
  - heading "Security Domains" [level=2]
  - paragraph: Comprehensive coverage of cybersecurity topics and specializations
  - text: 🌐 Web Application Security
  - heading "Web Application Security" [level=3]
  - paragraph: SQL injection, XSS, CSRF, and web vulnerability assessment
  - text: 🔒 Network Security
  - heading "Network Security" [level=3]
  - paragraph: Firewalls, intrusion detection, network protocols, and monitoring
  - text: 🔐 Cryptography & Encryption
  - heading "Cryptography & Encryption" [level=3]
  - paragraph: Encryption algorithms, digital signatures, and cryptographic protocols
  - text: ☁️ Cloud Security
  - heading "Cloud Security" [level=3]
  - paragraph: AWS, Azure, GCP security, container security, and cloud compliance
  - text: 🦠 Malware Analysis
  - heading "Malware Analysis" [level=3]
  - paragraph: Reverse engineering, dynamic analysis, and threat intelligence
  - text: 🚨 Incident Response
  - heading "Incident Response" [level=3]
  - paragraph: Digital forensics, incident handling, and security operations
  - heading "Why Our Security Training?" [level=2]
  - paragraph: Real-world focused cybersecurity education with practical applications
  - img
  - heading "CVE-Based Scenarios" [level=3]
  - paragraph: Learn from real vulnerabilities and security incidents
  - img
  - heading "Hands-On Tools" [level=3]
  - paragraph: Practice with industry-standard security tools
  - img
  - heading "Progressive Difficulty" [level=3]
  - paragraph: From beginner to advanced skill levels
  - img
  - heading "Detailed Analytics" [level=3]
  - paragraph: Track your security knowledge progress
  - heading "Featured Security Quizzes" [level=2]
  - paragraph: Start your cybersecurity journey with these curated quizzes
  - text: Security Intermediate
  - heading "Code Security Analysis" [level=3]
  - paragraph: By Anonymous
  - paragraph: Analyze code snippets to identify security vulnerabilities and best practices in various programming languages.
  - text: 6 questions 25 min code-analysis security vulnerabilities
  - link "Start Quiz":
    - /url: /quiz/683660ad0c42de016c5eb070
  - link "View All Security Quizzes":
    - /url: /explore
  - heading "Ready to Secure Your Future?" [level=2]
  - paragraph: Join the cybersecurity professionals mastering their skills with QuizFlow
  - link "Start Learning Now":
    - /url: /explore
  - link "Create Free Account":
    - /url: /auth/register
- contentinfo:
  - paragraph: © 2025 QuizFlow. All rights reserved.
- alert
```

# Test source

```ts
  143 |     await page.keyboard.press('Tab');
  144 |
  145 |     // Check if focused element has visible focus indicator
  146 |     const focusedElement = page.locator(':focus');
  147 |     const styles = await focusedElement.evaluate(el => {
  148 |       const computed = window.getComputedStyle(el);
  149 |       return {
  150 |         outline: computed.outline,
  151 |         outlineWidth: computed.outlineWidth,
  152 |         boxShadow: computed.boxShadow
  153 |       };
  154 |     });
  155 |
  156 |     // Should have some form of focus indicator
  157 |     const hasFocusIndicator = styles.outline !== 'none' ||
  158 |                              styles.outlineWidth !== '0px' ||
  159 |                              styles.boxShadow !== 'none';
  160 |     expect(hasFocusIndicator).toBe(true);
  161 |   });
  162 |
  163 |   test('should have proper color contrast', async ({ page }) => {
  164 |     await page.goto('/security-quizzes');
  165 |
  166 |     // Check text elements for color contrast
  167 |     const textElements = page.locator('p, h1, h2, h3, h4, h5, h6, span, div').first();
  168 |
  169 |     const colors = await textElements.evaluate(el => {
  170 |       const computed = window.getComputedStyle(el);
  171 |       return {
  172 |         color: computed.color,
  173 |         backgroundColor: computed.backgroundColor
  174 |       };
  175 |     });
  176 |
  177 |     // Basic check - should have defined colors
  178 |     expect(colors.color).toBeTruthy();
  179 |     expect(colors.backgroundColor).toBeTruthy();
  180 |   });
  181 |
  182 |   test('should have proper ARIA attributes', async ({ page }) => {
  183 |     await page.goto('/security-quizzes');
  184 |
  185 |     // Check for proper ARIA usage
  186 |     const ariaElements = page.locator('[aria-label], [aria-describedby], [aria-expanded], [role]');
  187 |     const ariaCount = await ariaElements.count();
  188 |
  189 |     if (ariaCount > 0) {
  190 |       // Check first few ARIA elements
  191 |       for (let i = 0; i < Math.min(ariaCount, 5); i++) {
  192 |         const element = ariaElements.nth(i);
  193 |         const ariaLabel = await element.getAttribute('aria-label');
  194 |         const role = await element.getAttribute('role');
  195 |
  196 |         // ARIA attributes should have meaningful values
  197 |         if (ariaLabel) {
  198 |           expect(ariaLabel.trim()).toBeTruthy();
  199 |         }
  200 |         if (role) {
  201 |           expect(role.trim()).toBeTruthy();
  202 |         }
  203 |       }
  204 |     }
  205 |   });
  206 |
  207 |   test('should support screen reader navigation', async ({ page }) => {
  208 |     await page.goto('/security-quizzes');
  209 |
  210 |     // Check for landmark elements
  211 |     const landmarks = page.locator('main, nav, header, footer, aside, section[aria-label]');
  212 |     const landmarkCount = await landmarks.count();
  213 |
  214 |     // Should have at least main content area
  215 |     expect(landmarkCount).toBeGreaterThan(0);
  216 |
  217 |     // Check for skip links
  218 |     const skipLinks = page.locator('a[href="#main"], a[href="#content"], .skip-link');
  219 |     if (await skipLinks.count() > 0) {
  220 |       await expect(skipLinks.first()).toBeVisible();
  221 |     }
  222 |   });
  223 |
  224 |   test('should handle high contrast mode', async ({ page }) => {
  225 |     // Simulate high contrast mode
  226 |     await page.addStyleTag({
  227 |       content: `
  228 |         @media (prefers-contrast: high) {
  229 |           * {
  230 |             background: white !important;
  231 |             color: black !important;
  232 |           }
  233 |         }
  234 |       `
  235 |     });
  236 |
  237 |     await page.goto('/security-quizzes');
  238 |
  239 |     // Should still be functional and readable
  240 |     await expect(page.locator('h1')).toBeVisible();
  241 |     const quizElements = page.locator('.quiz-card, .quiz-item');
  242 |     const count = await quizElements.count();
> 243 |     expect(count).toBeGreaterThan(0);
      |                   ^ Error: expect(received).toBeGreaterThan(expected)
  244 |   });
  245 |
  246 |   test('should support reduced motion preferences', async ({ page }) => {
  247 |     // Simulate reduced motion preference
  248 |     await page.addStyleTag({
  249 |       content: `
  250 |         @media (prefers-reduced-motion: reduce) {
  251 |           * {
  252 |             animation-duration: 0.01ms !important;
  253 |             animation-iteration-count: 1 !important;
  254 |             transition-duration: 0.01ms !important;
  255 |           }
  256 |         }
  257 |       `
  258 |     });
  259 |
  260 |     await page.goto('/security-quizzes');
  261 |
  262 |     // Should still function without animations
  263 |     await expect(page.locator('h1')).toBeVisible();
  264 |     const quizElements2 = page.locator('.quiz-card, .quiz-item');
  265 |     const count2 = await quizElements2.count();
  266 |     expect(count2).toBeGreaterThan(0);
  267 |   });
  268 |
  269 |   test('should have proper form validation messages', async ({ page }) => {
  270 |     await page.goto('/auth/login');
  271 |
  272 |     // Try to submit empty form
  273 |     await page.click('button[type="submit"]');
  274 |
  275 |     // Check for validation messages
  276 |     const validationMessages = page.locator('[aria-invalid="true"], .error-message, .validation-error');
  277 |     if (await validationMessages.count() > 0) {
  278 |       // Validation messages should be accessible
  279 |       const firstMessage = validationMessages.first();
  280 |       const text = await firstMessage.textContent();
  281 |       expect(text?.trim()).toBeTruthy();
  282 |     }
  283 |   });
  284 |
  285 |   test('should support zoom up to 200%', async ({ page }) => {
  286 |     await page.goto('/security-quizzes');
  287 |
  288 |     // Simulate 200% zoom
  289 |     await page.setViewportSize({ width: 640, height: 360 }); // Half size = 200% zoom effect
  290 |
  291 |     // Should still be usable
  292 |     await expect(page.locator('h1')).toBeVisible();
  293 |
  294 |     // Navigation should still work
  295 |     const firstQuiz = page.locator('.quiz-card, .quiz-item').first();
  296 |     if (await firstQuiz.isVisible()) {
  297 |       await expect(firstQuiz).toBeVisible();
  298 |     }
  299 |   });
  300 |
  301 |   test('should have proper table accessibility', async ({ page }) => {
  302 |     // Navigate to a page that might have tables (analytics)
  303 |     await page.goto('/dashboard/analytics');
  304 |
  305 |     const tables = page.locator('table');
  306 |     const tableCount = await tables.count();
  307 |
  308 |     if (tableCount > 0) {
  309 |       const firstTable = tables.first();
  310 |
  311 |       // Should have table headers
  312 |       const headers = firstTable.locator('th');
  313 |       if (await headers.count() > 0) {
  314 |         await expect(headers.first()).toBeVisible();
  315 |       }
  316 |
  317 |       // Should have proper table structure
  318 |       const caption = firstTable.locator('caption');
  319 |       const summary = await firstTable.getAttribute('summary');
  320 |
  321 |       // Table should have caption or summary for accessibility
  322 |       if (await caption.count() > 0 || summary) {
  323 |         expect(true).toBe(true); // Has accessibility feature
  324 |       }
  325 |     }
  326 |   });
  327 |
  328 |   test('should handle error states accessibly', async ({ page }) => {
  329 |     // Try to access a non-existent page
  330 |     await page.goto('/nonexistent-page');
  331 |
  332 |     // Should show accessible error message
  333 |     const errorMessage = page.locator('h1, .error-message, .not-found');
  334 |     if (await errorMessage.count() > 0) {
  335 |       const text = await errorMessage.first().textContent();
  336 |       expect(text?.trim()).toBeTruthy();
  337 |     }
  338 |   });
  339 | });
  340 |
```