# Test info

- Name: Answer Exposure Security Tests >> should not expose correct answers in quiz API response
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/security/answer-exposure.test.ts:73:7

# Error details

```
PrismaClientValidationError: 
Invalid `prisma.quiz.create()` invocation in
/Users/<USER>/Documents/augment-projects/hacking-quiz/tests/utils/test-data-seeder.ts:117:43

  114 
  115 const quiz = { ...defaultQuiz, ...quizData };
  116 
→ 117 const createdQuiz = await prisma.quiz.create({
        data: {
          title: "Security Test Quiz",
          description: "Quiz for testing answer exposure",
          content: {
            quiz_id: "security-test-quiz",
            title: {
              en: "Security Test Quiz"
            },
            description: {
              en: "Quiz for testing answer exposure"
            },
            version: "1.0.0",
            language: "en",
            questions: [
              {
                question_id: "mc_test",
                type: "multiple_choice",
                question_text: {
                  en: "What is 2+2?"
                },
                options: [
                  {
                    id: "a",
                    text: {
                      en: "3"
                    },
                    is_correct: false
                  },
                  {
                    id: "b",
                    text: {
                      en: "4"
                    },
                    is_correct: true
                  },
                  {
                    id: "c",
                    text: {
                      en: "5"
                    },
                    is_correct: false
                  }
                ],
                points: 1
              },
              {
                question_id: "tf_test",
                type: "true_false",
                question_text: {
                  en: "The sky is blue."
                },
                correct_answer: true,
                points: 1
              },
              {
                question_id: "sa_test",
                type: "short_answer",
                question_text: {
                  en: "What is the capital of France?"
                },
                correct_answers: [
                  "Paris",
                  "paris"
                ],
                points: 1
              }
            ],
            settings: {
              time_limit: null,
              shuffle_questions: false,
              shuffle_options: false,
              show_results: true,
              allow_review: true
            }
          },
          authorId: "683c6dddb66be77564906ced",
          isPublished: true,
      +   quizId: String
        }
      })

Argument `quizId` is missing.
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/utils/test-data-seeder.ts:117:43
    at kn (/Users/<USER>/Documents/augment-projects/hacking-quiz/src/generated/prisma/runtime/library.js:32:1363)
    at Zn.handleRequestError (/Users/<USER>/Documents/augment-projects/hacking-quiz/src/generated/prisma/runtime/library.js:124:7102)
    at Zn.handleAndLogRequestError (/Users/<USER>/Documents/augment-projects/hacking-quiz/src/generated/prisma/runtime/library.js:124:6784)
    at Zn.request (/Users/<USER>/Documents/augment-projects/hacking-quiz/src/generated/prisma/runtime/library.js:124:6491)
    at l (/Users/<USER>/Documents/augment-projects/hacking-quiz/src/generated/prisma/runtime/library.js:133:9778)
    at TestDataSeeder.seedTestQuiz (/Users/<USER>/Documents/augment-projects/hacking-quiz/tests/utils/test-data-seeder.ts:117:25)
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/security/answer-exposure.test.ts:19:22
```

# Test source

```ts
   17 |   content: any;
   18 |   authorId: string;
   19 | }
   20 |
   21 | export class TestDataSeeder {
   22 |   private static instance: TestDataSeeder;
   23 |   private seededData: {
   24 |     users: TestUser[];
   25 |     quizzes: TestQuiz[];
   26 |   } = {
   27 |     users: [],
   28 |     quizzes: []
   29 |   };
   30 |
   31 |   static getInstance(): TestDataSeeder {
   32 |     if (!TestDataSeeder.instance) {
   33 |       TestDataSeeder.instance = new TestDataSeeder();
   34 |     }
   35 |     return TestDataSeeder.instance;
   36 |   }
   37 |
   38 |   async seedTestUser(userData: Partial<TestUser> = {}): Promise<TestUser> {
   39 |     const defaultUser = {
   40 |       email: `test-${Date.now()}@example.com`,
   41 |       password: 'testpassword123',
   42 |       role: 'USER' as const,
   43 |       name: 'Test User'
   44 |     };
   45 |
   46 |     const user = { ...defaultUser, ...userData };
   47 |     const hashedPassword = await bcrypt.hash(user.password, 12);
   48 |
   49 |     const createdUser = await prisma.user.create({
   50 |       data: {
   51 |         email: user.email,
   52 |         password: hashedPassword,
   53 |         role: user.role,
   54 |         name: user.name
   55 |       }
   56 |     });
   57 |
   58 |     const testUser: TestUser = {
   59 |       id: createdUser.id,
   60 |       email: user.email,
   61 |       password: user.password, // Store original password for testing
   62 |       role: user.role
   63 |     };
   64 |
   65 |     this.seededData.users.push(testUser);
   66 |     return testUser;
   67 |   }
   68 |
   69 |   async seedTestAdmin(): Promise<TestUser> {
   70 |     return this.seedTestUser({
   71 |       email: `admin-${Date.now()}@example.com`,
   72 |       role: 'ADMIN'
   73 |     });
   74 |   }
   75 |
   76 |   async seedTestQuiz(quizData: Partial<TestQuiz> = {}, authorId?: string): Promise<TestQuiz> {
   77 |     if (!authorId) {
   78 |       const admin = await this.seedTestAdmin();
   79 |       authorId = admin.id;
   80 |     }
   81 |
   82 |     const defaultQuiz = {
   83 |       title: `Test Quiz ${Date.now()}`,
   84 |       description: 'A test quiz for E2E testing',
   85 |       content: {
   86 |         quiz_id: `test-quiz-${Date.now()}`,
   87 |         title: { en: `Test Quiz ${Date.now()}` },
   88 |         description: { en: 'A test quiz for E2E testing' },
   89 |         version: '1.0.0',
   90 |         language: 'en',
   91 |         questions: [
   92 |           {
   93 |             question_id: 'q1',
   94 |             type: 'multiple_choice',
   95 |             question_text: { en: 'What is 2 + 2?' },
   96 |             options: [
   97 |               { id: 'a', text: { en: '3' }, is_correct: false },
   98 |               { id: 'b', text: { en: '4' }, is_correct: true },
   99 |               { id: 'c', text: { en: '5' }, is_correct: false }
  100 |             ],
  101 |             points: 1,
  102 |             explanation: { en: '2 + 2 equals 4' }
  103 |           }
  104 |         ],
  105 |         settings: {
  106 |           time_limit: null,
  107 |           shuffle_questions: false,
  108 |           shuffle_options: false,
  109 |           show_results: true,
  110 |           allow_review: true
  111 |         }
  112 |       }
  113 |     };
  114 |
  115 |     const quiz = { ...defaultQuiz, ...quizData };
  116 |
> 117 |     const createdQuiz = await prisma.quiz.create({
      |                                           ^ PrismaClientValidationError: 
  118 |       data: {
  119 |         title: quiz.title,
  120 |         description: quiz.description,
  121 |         content: quiz.content,
  122 |         authorId: authorId,
  123 |         isPublished: true
  124 |       }
  125 |     });
  126 |
  127 |     const testQuiz: TestQuiz = {
  128 |       id: createdQuiz.id,
  129 |       title: quiz.title,
  130 |       description: quiz.description,
  131 |       content: quiz.content,
  132 |       authorId: authorId
  133 |     };
  134 |
  135 |     this.seededData.quizzes.push(testQuiz);
  136 |     return testQuiz;
  137 |   }
  138 |
  139 |   async seedMultipleQuizzes(count: number = 3, authorId?: string): Promise<TestQuiz[]> {
  140 |     const quizzes: TestQuiz[] = [];
  141 |
  142 |     for (let i = 0; i < count; i++) {
  143 |       const quiz = await this.seedTestQuiz({
  144 |         title: `Test Quiz ${i + 1} - ${Date.now()}`,
  145 |         description: `Test quiz number ${i + 1} for E2E testing`
  146 |       }, authorId);
  147 |       quizzes.push(quiz);
  148 |     }
  149 |
  150 |     return quizzes;
  151 |   }
  152 |
  153 |   async cleanupSeededData(): Promise<void> {
  154 |     // Delete quizzes first (due to foreign key constraints)
  155 |     for (const quiz of this.seededData.quizzes) {
  156 |       await prisma.quiz.delete({
  157 |         where: { id: quiz.id }
  158 |       }).catch(() => {
  159 |         // Ignore errors if already deleted
  160 |       });
  161 |     }
  162 |
  163 |     // Delete users
  164 |     for (const user of this.seededData.users) {
  165 |       await prisma.user.delete({
  166 |         where: { id: user.id }
  167 |       }).catch(() => {
  168 |         // Ignore errors if already deleted
  169 |       });
  170 |     }
  171 |
  172 |     // Reset seeded data
  173 |     this.seededData = { users: [], quizzes: [] };
  174 |   }
  175 |
  176 |   getSeededUsers(): TestUser[] {
  177 |     return [...this.seededData.users];
  178 |   }
  179 |
  180 |   getSeededQuizzes(): TestQuiz[] {
  181 |     return [...this.seededData.quizzes];
  182 |   }
  183 |
  184 |   async disconnect(): Promise<void> {
  185 |     await prisma.$disconnect();
  186 |   }
  187 | }
  188 |
  189 | // Helper functions for common test scenarios
  190 | export async function createTestUserAndLogin() {
  191 |   const seeder = TestDataSeeder.getInstance();
  192 |   return await seeder.seedTestUser();
  193 | }
  194 |
  195 | export async function createTestAdminAndLogin() {
  196 |   const seeder = TestDataSeeder.getInstance();
  197 |   return await seeder.seedTestAdmin();
  198 | }
  199 |
  200 | export async function createTestQuizWithAdmin() {
  201 |   const seeder = TestDataSeeder.getInstance();
  202 |   const admin = await seeder.seedTestAdmin();
  203 |   const quiz = await seeder.seedTestQuiz({}, admin.id);
  204 |   return { admin, quiz };
  205 | }
  206 |
  207 | export async function cleanupAllTestData() {
  208 |   const seeder = TestDataSeeder.getInstance();
  209 |   await seeder.cleanupSeededData();
  210 | }
  211 |
```