# Test info

- Name: Authentication Flow >> should persist session across page reloads
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/authentication.spec.ts:115:7

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('text=Create Quiz')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('text=Create Quiz')

    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/authentication.spec.ts:129:52
```

# Page snapshot

```yaml
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "My Quizzes":
      - /url: /dashboard/quizzes
    - link "Activity":
      - /url: /dashboard/activity
    - link "Explore":
      - /url: /explore
    - link "Admin":
      - /url: /dashboard/admin
  - text: <NAME_EMAIL>
  - button "Sign Out"
- main:
  - heading "Dashboard" [level=1]
  - heading "Welcome, QuizFlow Admin!" [level=3]
  - paragraph: Manage quizzes and view your progress
  - link "Create New Quiz":
    - /url: /dashboard/quizzes/create
  - link "View All Quizzes":
    - /url: /dashboard/quizzes
  - link "Explore Quizzes":
    - /url: /explore
  - heading "Your Stats" [level=3]
  - paragraph: Your quiz activity and performance
  - paragraph: Created Quizzes
  - paragraph: "5"
  - paragraph: Completed Quizzes
  - paragraph: "0"
  - heading "Your Quizzes" [level=3]
  - paragraph: Recently created and updated quizzes
  - list:
    - listitem:
      - paragraph: adsad
      - paragraph: Draft
      - link "Edit":
        - /url: /dashboard/quizzes/683cdc7cc3dde88047e471a9
    - listitem:
      - paragraph: Show Answer Test Quiz
      - paragraph: Published
      - link "Edit":
        - /url: /dashboard/quizzes/683637f169ce32e25dd6fde2
    - listitem:
      - paragraph: Empty Quiz Preview
      - paragraph: Published
      - link "Edit":
        - /url: /dashboard/quizzes/6835f34b08a7ee876514f589
    - listitem:
      - paragraph: Settings Preview Quiz
      - paragraph: Draft
      - link "Edit":
        - /url: /dashboard/quizzes/6835f34b08a7ee876514f58a
    - listitem:
      - paragraph: Exit Preview Test
      - paragraph: Draft
      - link "Edit":
        - /url: /dashboard/quizzes/6835f34a08a7ee876514f588
  - link "View all quizzes":
    - /url: /dashboard/quizzes
  - heading "Recent Activity" [level=3]
  - paragraph: Your recent quiz attempts
  - paragraph: You haven't taken any quizzes yet.
- alert
```

# Test source

```ts
   29 |   test('should handle admin login', async ({ page, testAdmin, auth }) => {
   30 |     // Test admin login
   31 |     await auth.loginAsAdmin(testAdmin);
   32 |
   33 |     // Verify we're on dashboard
   34 |     await expect(page).toHaveURL(/\/dashboard/);
   35 |     await expect(page.locator('text="Welcome"')).toBeVisible();
   36 |
   37 |     // Verify admin can access admin panel
   38 |     await page.goto('/dashboard/admin');
   39 |     await expect(page.locator('h1')).toBeVisible();
   40 |   });
   41 |
   42 |   test('should show error for invalid credentials', async ({ page }) => {
   43 |     await page.goto('/auth/login');
   44 |
   45 |     // Fill in invalid credentials
   46 |     await page.fill('input[id="email"]', '<EMAIL>');
   47 |     await page.fill('input[id="password"]', 'wrongpassword');
   48 |
   49 |     // Submit form
   50 |     await page.click('button[type="submit"]');
   51 |
   52 |     // Should show error message
   53 |     await expect(page.locator('text=Invalid email or password')).toBeVisible();
   54 |
   55 |     // Should stay on login page
   56 |     expect(page.url()).toContain('/auth/login');
   57 |   });
   58 |
   59 |   test('should allow user registration', async ({ page }) => {
   60 |     await page.goto('/auth/register');
   61 |
   62 |     // Fill in registration form
   63 |     await page.fill('input[id="name"]', 'Test User');
   64 |     await page.fill('input[id="email"]', `test${Date.now()}@example.com`);
   65 |     await page.fill('input[id="password"]', 'testpassword123');
   66 |     await page.fill('input[id="confirmPassword"]', 'testpassword123');
   67 |
   68 |     // Submit form
   69 |     await page.click('button[type="submit"]');
   70 |
   71 |     // Should redirect to dashboard or show success message
   72 |     await page.waitForURL('/dashboard');
   73 |     expect(page.url()).toContain('/dashboard');
   74 |   });
   75 |
   76 |   test('should validate registration form', async ({ page }) => {
   77 |     await page.goto('/auth/register');
   78 |
   79 |     // Try to submit empty form
   80 |     await page.click('button[type="submit"]');
   81 |
   82 |     // Should show validation errors
   83 |     await expect(page.locator('input[id="email"]:invalid')).toBeVisible();
   84 |     await expect(page.locator('input[id="password"]:invalid')).toBeVisible();
   85 |   });
   86 |
   87 |   test('should validate password confirmation', async ({ page }) => {
   88 |     await page.goto('/auth/register');
   89 |
   90 |     // Fill in mismatched passwords
   91 |     await page.fill('input[id="name"]', 'Test User');
   92 |     await page.fill('input[id="email"]', '<EMAIL>');
   93 |     await page.fill('input[id="password"]', 'password123');
   94 |     await page.fill('input[id="confirmPassword"]', 'differentpassword');
   95 |
   96 |     // Submit form
   97 |     await page.click('button[type="submit"]');
   98 |
   99 |     // Should show password mismatch error
  100 |     await expect(page.locator('text=Passwords do not match')).toBeVisible();
  101 |   });
  102 |
  103 |   test('should handle logout', async ({ page, authenticatedUser: _authenticatedUser, auth }) => {
  104 |     // User is already logged in via fixture
  105 |     await expect(page.locator('button:has-text("Sign Out")')).toBeVisible();
  106 |
  107 |     // Logout
  108 |     await auth.logout();
  109 |
  110 |     // Verify we're logged out
  111 |     await expect(page.locator('a:has-text("Sign In")')).toBeVisible();
  112 |     await expect(page).toHaveURL('/');
  113 |   });
  114 |
  115 |   test('should persist session across page reloads', async ({ page }) => {
  116 |     // Login
  117 |     await page.goto('/auth/login');
  118 |     await page.fill('input[id="email"]', '<EMAIL>');
  119 |     await page.fill('input[id="password"]', 'admin123');
  120 |     await page.click('button[type="submit"]');
  121 |
  122 |     await page.waitForURL('/dashboard');
  123 |
  124 |     // Reload page
  125 |     await page.reload();
  126 |
  127 |     // Should still be logged in
  128 |     expect(page.url()).toContain('/dashboard');
> 129 |     await expect(page.locator('text=Create Quiz')).toBeVisible();
      |                                                    ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
  130 |   });
  131 |
  132 |   test('should handle session expiration', async ({ page }) => {
  133 |     // Login
  134 |     await page.goto('/auth/login');
  135 |     await page.fill('input[id="email"]', '<EMAIL>');
  136 |     await page.fill('input[id="password"]', 'admin123');
  137 |     await page.click('button[type="submit"]');
  138 |
  139 |     await page.waitForURL('/dashboard');
  140 |
  141 |     // Simulate session expiration by clearing cookies
  142 |     await page.context().clearCookies();
  143 |
  144 |     // Try to access protected resource
  145 |     await page.goto('/dashboard/quizzes/create');
  146 |
  147 |     // Should redirect to login
  148 |     await page.waitForURL('/auth/login');
  149 |   });
  150 |
  151 |   test('should restrict admin features to admin users', async ({ page }) => {
  152 |     // Login as regular user
  153 |     await page.goto('/auth/login');
  154 |     await page.fill('input[id="email"]', '<EMAIL>');
  155 |     await page.fill('input[id="password"]', 'user123');
  156 |     await page.click('button[type="submit"]');
  157 |
  158 |     await page.waitForURL('/dashboard');
  159 |
  160 |     // Try to access quiz creation (admin only)
  161 |     await page.goto('/dashboard/quizzes/create');
  162 |
  163 |     // Should be redirected or show access denied
  164 |     await expect(page.locator('text=Access denied')).toBeVisible();
  165 |   });
  166 | });
  167 |
```