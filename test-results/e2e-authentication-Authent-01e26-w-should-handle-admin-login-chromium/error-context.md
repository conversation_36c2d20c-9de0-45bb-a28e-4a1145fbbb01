# Test info

- Name: Authentication Flow >> should handle admin login
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/authentication.spec.ts:29:7

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toHaveURL(expected)

Locator: locator(':root')
Expected pattern: /\/dashboard/
Received string:  "http://localhost:3000/admin"
Call log:
  - expect.toHaveURL with timeout 5000ms
  - waiting for locator(':root')
    9 × locator resolved to <html lang="en" class="light">…</html>
      - unexpected value "http://localhost:3000/admin"

    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/authentication.spec.ts:34:24
```

# Page snapshot

```yaml
- heading "404" [level=1]
- heading "This page could not be found." [level=2]
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
   1 | import { test, expect } from '../utils/e2e-setup';
   2 |
   3 | test.describe('Authentication Flow', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Ensure we start logged out
   6 |     await page.goto('/');
   7 |     await page.waitForLoadState('networkidle');
   8 |   });
   9 |
   10 |   test('should redirect unauthenticated users to login', async ({ page }) => {
   11 |     // Try to access protected dashboard
   12 |     await page.goto('/dashboard');
   13 |
   14 |     // Should redirect to login page
   15 |     await page.waitForURL(/\/auth\/login/, { timeout: 10000 });
   16 |     expect(page.url()).toContain('/auth/login');
   17 |   });
   18 |
   19 |   test('should handle login with test user', async ({ page, testUser, auth }) => {
   20 |     // Test login with seeded user
   21 |     await auth.loginUser(testUser);
   22 |
   23 |     // Verify we're on dashboard
   24 |     await expect(page).toHaveURL(/\/dashboard/);
   25 |     await expect(page.locator('text="Welcome"')).toBeVisible();
   26 |     await expect(page.locator('button:has-text("Sign Out")')).toBeVisible();
   27 |   });
   28 |
   29 |   test('should handle admin login', async ({ page, testAdmin, auth }) => {
   30 |     // Test admin login
   31 |     await auth.loginAsAdmin(testAdmin);
   32 |
   33 |     // Verify we're on dashboard
>  34 |     await expect(page).toHaveURL(/\/dashboard/);
      |                        ^ Error: Timed out 5000ms waiting for expect(locator).toHaveURL(expected)
   35 |     await expect(page.locator('text="Welcome"')).toBeVisible();
   36 |
   37 |     // Verify admin can access admin panel
   38 |     await page.goto('/dashboard/admin');
   39 |     await expect(page.locator('h1')).toBeVisible();
   40 |   });
   41 |
   42 |   test('should show error for invalid credentials', async ({ page }) => {
   43 |     await page.goto('/auth/login');
   44 |
   45 |     // Fill in invalid credentials
   46 |     await page.fill('input[id="email"]', '<EMAIL>');
   47 |     await page.fill('input[id="password"]', 'wrongpassword');
   48 |
   49 |     // Submit form
   50 |     await page.click('button[type="submit"]');
   51 |
   52 |     // Should show error message
   53 |     await expect(page.locator('text=Invalid email or password')).toBeVisible();
   54 |
   55 |     // Should stay on login page
   56 |     expect(page.url()).toContain('/auth/login');
   57 |   });
   58 |
   59 |   test('should allow user registration', async ({ page }) => {
   60 |     await page.goto('/auth/register');
   61 |
   62 |     // Fill in registration form
   63 |     await page.fill('input[id="name"]', 'Test User');
   64 |     await page.fill('input[id="email"]', `test${Date.now()}@example.com`);
   65 |     await page.fill('input[id="password"]', 'testpassword123');
   66 |     await page.fill('input[id="confirmPassword"]', 'testpassword123');
   67 |
   68 |     // Submit form
   69 |     await page.click('button[type="submit"]');
   70 |
   71 |     // Should redirect to dashboard or show success message
   72 |     await page.waitForURL('/dashboard');
   73 |     expect(page.url()).toContain('/dashboard');
   74 |   });
   75 |
   76 |   test('should validate registration form', async ({ page }) => {
   77 |     await page.goto('/auth/register');
   78 |
   79 |     // Try to submit empty form
   80 |     await page.click('button[type="submit"]');
   81 |
   82 |     // Should show validation errors
   83 |     await expect(page.locator('input[id="email"]:invalid')).toBeVisible();
   84 |     await expect(page.locator('input[id="password"]:invalid')).toBeVisible();
   85 |   });
   86 |
   87 |   test('should validate password confirmation', async ({ page }) => {
   88 |     await page.goto('/auth/register');
   89 |
   90 |     // Fill in mismatched passwords
   91 |     await page.fill('input[id="name"]', 'Test User');
   92 |     await page.fill('input[id="email"]', '<EMAIL>');
   93 |     await page.fill('input[id="password"]', 'password123');
   94 |     await page.fill('input[id="confirmPassword"]', 'differentpassword');
   95 |
   96 |     // Submit form
   97 |     await page.click('button[type="submit"]');
   98 |
   99 |     // Should show password mismatch error
  100 |     await expect(page.locator('text=Passwords do not match')).toBeVisible();
  101 |   });
  102 |
  103 |   test('should handle logout', async ({ page, authenticatedUser: _authenticatedUser, auth }) => {
  104 |     // User is already logged in via fixture
  105 |     await expect(page.locator('button:has-text("Sign Out")')).toBeVisible();
  106 |
  107 |     // Logout
  108 |     await auth.logout();
  109 |
  110 |     // Verify we're logged out
  111 |     await expect(page.locator('a:has-text("Sign In")')).toBeVisible();
  112 |     await expect(page).toHaveURL('/');
  113 |   });
  114 |
  115 |   test('should persist session across page reloads', async ({ page }) => {
  116 |     // Login
  117 |     await page.goto('/auth/login');
  118 |     await page.fill('input[id="email"]', '<EMAIL>');
  119 |     await page.fill('input[id="password"]', 'admin123');
  120 |     await page.click('button[type="submit"]');
  121 |
  122 |     await page.waitForURL('/dashboard');
  123 |
  124 |     // Reload page
  125 |     await page.reload();
  126 |
  127 |     // Should still be logged in
  128 |     expect(page.url()).toContain('/dashboard');
  129 |     await expect(page.locator('text=Create Quiz')).toBeVisible();
  130 |   });
  131 |
  132 |   test('should handle session expiration', async ({ page }) => {
  133 |     // Login
  134 |     await page.goto('/auth/login');
```