<testsuites id="" name="" tests="3" failures="0" skipped="0" errors="0" time="11.926900999999999">
<testsuite name="auth-simple.spec.ts" timestamp="2025-05-28T00:50:16.979Z" hostname="chromium" tests="3" failures="0" skipped="0" time="6.344" errors="0">
<testcase name="Simple Authentication Test › should create test user and login successfully" classname="auth-simple.spec.ts" time="3.271">
</testcase>
<testcase name="Simple Authentication Test › should create admin user and access admin panel" classname="auth-simple.spec.ts" time="2.16">
</testcase>
<testcase name="Simple Authentication Test › should handle invalid credentials" classname="auth-simple.spec.ts" time="0.913">
</testcase>
</testsuite>
</testsuites>