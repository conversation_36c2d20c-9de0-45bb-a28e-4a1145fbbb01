<testsuites id="" name="" tests="845" failures="23" skipped="767" errors="0" time="403.272388">
<testsuite name="e2e/accessibility.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="16" failures="2" skipped="0" time="30.248" errors="0">
<testcase name="Accessibility Tests › should have proper heading hierarchy" classname="e2e/accessibility.spec.ts" time="3.757">
</testcase>
<testcase name="Accessibility Tests › should have proper form labels" classname="e2e/accessibility.spec.ts" time="1.719">
</testcase>
<testcase name="Accessibility Tests › should have proper button accessibility" classname="e2e/accessibility.spec.ts" time="1.727">
</testcase>
<testcase name="Accessibility Tests › should have proper link accessibility" classname="e2e/accessibility.spec.ts" time="1.776">
</testcase>
<testcase name="Accessibility Tests › should have proper image alt text" classname="e2e/accessibility.spec.ts" time="1.735">
</testcase>
<testcase name="Accessibility Tests › should support keyboard navigation" classname="e2e/accessibility.spec.ts" time="1.729">
</testcase>
<testcase name="Accessibility Tests › should have proper focus indicators" classname="e2e/accessibility.spec.ts" time="1.747">
</testcase>
<testcase name="Accessibility Tests › should have proper color contrast" classname="e2e/accessibility.spec.ts" time="1.716">
</testcase>
<testcase name="Accessibility Tests › should have proper ARIA attributes" classname="e2e/accessibility.spec.ts" time="1.715">
</testcase>
<testcase name="Accessibility Tests › should support screen reader navigation" classname="e2e/accessibility.spec.ts" time="1.724">
</testcase>
<testcase name="Accessibility Tests › should handle high contrast mode" classname="e2e/accessibility.spec.ts" time="1.722">
<failure message="accessibility.spec.ts:224:7 should handle high contrast mode" type="FAILURE">
<![CDATA[  [chromium] › e2e/accessibility.spec.ts:224:7 › Accessibility Tests › should handle high contrast mode 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      241 |     const quizElements = page.locator('.quiz-card, .quiz-item');
      242 |     const count = await quizElements.count();
    > 243 |     expect(count).toBeGreaterThan(0);
          |                   ^
      244 |   });
      245 |
      246 |   test('should support reduced motion preferences', async ({ page }) => {
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/accessibility.spec.ts:243:19

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-accessibility-Accessib-32a6a-d-handle-high-contrast-mode-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-accessibility-Accessib-32a6a-d-handle-high-contrast-mode-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-accessibility-Accessib-32a6a-d-handle-high-contrast-mode-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-accessibility-Accessib-32a6a-d-handle-high-contrast-mode-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-accessibility-Accessib-32a6a-d-handle-high-contrast-mode-chromium/video.webm]]

[[ATTACHMENT|e2e-accessibility-Accessib-32a6a-d-handle-high-contrast-mode-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Tests › should support reduced motion preferences" classname="e2e/accessibility.spec.ts" time="1.842">
<failure message="accessibility.spec.ts:246:7 should support reduced motion preferences" type="FAILURE">
<![CDATA[  [chromium] › e2e/accessibility.spec.ts:246:7 › Accessibility Tests › should support reduced motion preferences 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      264 |     const quizElements2 = page.locator('.quiz-card, .quiz-item');
      265 |     const count2 = await quizElements2.count();
    > 266 |     expect(count2).toBeGreaterThan(0);
          |                    ^
      267 |   });
      268 |
      269 |   test('should have proper form validation messages', async ({ page }) => {
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/accessibility.spec.ts:266:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-accessibility-Accessib-8ac31--reduced-motion-preferences-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-accessibility-Accessib-8ac31--reduced-motion-preferences-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-accessibility-Accessib-8ac31--reduced-motion-preferences-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-accessibility-Accessib-8ac31--reduced-motion-preferences-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-accessibility-Accessib-8ac31--reduced-motion-preferences-chromium/video.webm]]

[[ATTACHMENT|e2e-accessibility-Accessib-8ac31--reduced-motion-preferences-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Tests › should have proper form validation messages" classname="e2e/accessibility.spec.ts" time="1.851">
</testcase>
<testcase name="Accessibility Tests › should support zoom up to 200%" classname="e2e/accessibility.spec.ts" time="1.705">
</testcase>
<testcase name="Accessibility Tests › should have proper table accessibility" classname="e2e/accessibility.spec.ts" time="2.022">
</testcase>
<testcase name="Accessibility Tests › should handle error states accessibly" classname="e2e/accessibility.spec.ts" time="1.761">
</testcase>
</testsuite>
<testsuite name="e2e/admin.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="12" failures="3" skipped="0" time="43.547" errors="0">
<testcase name="Admin Dashboard › should display admin dashboard" classname="e2e/admin.spec.ts" time="1.041">
</testcase>
<testcase name="Admin Dashboard › should manage users" classname="e2e/admin.spec.ts" time="1.737">
<failure message="admin.spec.ts:26:7 should manage users" type="FAILURE">
<![CDATA[  [chromium] › e2e/admin.spec.ts:26:7 › Admin Dashboard › should manage users ──────────────────────

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      36 |       const userContent = page.locator('.user-list, .users-table, table, form, .content');
      37 |       const contentCount = await userContent.count();
    > 38 |       expect(contentCount).toBeGreaterThan(0);
         |                            ^
      39 |     } else {
      40 |       // User management might not be implemented yet - that's ok
      41 |       console.log('User management not found - feature may not be implemented yet');
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/admin.spec.ts:38:28

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-admin-Admin-Dashboard-should-manage-users-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-admin-Admin-Dashboard-should-manage-users-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-admin-Admin-Dashboard-should-manage-users-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-admin-Admin-Dashboard-should-manage-users-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-admin-Admin-Dashboard-should-manage-users-chromium/video.webm]]

[[ATTACHMENT|e2e-admin-Admin-Dashboard-should-manage-users-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should create new user" classname="e2e/admin.spec.ts" time="0.857">
</testcase>
<testcase name="Admin Dashboard › should manage quiz settings" classname="e2e/admin.spec.ts" time="1.735">
<failure message="admin.spec.ts:80:7 should manage quiz settings" type="FAILURE">
<![CDATA[  [chromium] › e2e/admin.spec.ts:80:7 › Admin Dashboard › should manage quiz settings ──────────────

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      90 |       const content = page.locator('.quiz-management, .admin-quizzes, .content, main');
      91 |       const contentCount = await content.count();
    > 92 |       expect(contentCount).toBeGreaterThan(0);
         |                            ^
      93 |     } else {
      94 |       console.log('Quiz management not found - feature may not be implemented yet');
      95 |     }
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/admin.spec.ts:92:28

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-admin-Admin-Dashboard-should-manage-quiz-settings-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-admin-Admin-Dashboard-should-manage-quiz-settings-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-admin-Admin-Dashboard-should-manage-quiz-settings-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-admin-Admin-Dashboard-should-manage-quiz-settings-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-admin-Admin-Dashboard-should-manage-quiz-settings-chromium/video.webm]]

[[ATTACHMENT|e2e-admin-Admin-Dashboard-should-manage-quiz-settings-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should configure system settings" classname="e2e/admin.spec.ts" time="1.839">
</testcase>
<testcase name="Admin Dashboard › should view system logs" classname="e2e/admin.spec.ts" time="1.912">
</testcase>
<testcase name="Admin Dashboard › should manage categories and tags" classname="e2e/admin.spec.ts" time="0.686">
<system-out>
<![CDATA[Category management not found - feature may not be implemented yet
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should backup and restore data" classname="e2e/admin.spec.ts" time="0.711">
<system-out>
<![CDATA[Backup functionality not found - feature may not be implemented yet
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should monitor system health" classname="e2e/admin.spec.ts" time="0.702">
<system-out>
<![CDATA[System health monitoring not found - feature may not be implemented yet
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should manage permissions and roles" classname="e2e/admin.spec.ts" time="0.659">
<system-out>
<![CDATA[Permissions management not found - feature may not be implemented yet
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should handle bulk operations" classname="e2e/admin.spec.ts" time="0.78">
</testcase>
<testcase name="Admin Access Control › should deny access to non-admin users" classname="e2e/admin.spec.ts" time="30.888">
<failure message="admin.spec.ts:235:7 should deny access to non-admin users" type="FAILURE">
<![CDATA[  [chromium] › e2e/admin.spec.ts:235:7 › Admin Access Control › should deny access to non-admin users 

    Test timeout of 30000ms exceeded.

    Error: page.waitForURL: Test timeout of 30000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "/dashboard" until "load"
    ============================================================

      239 |     await page.fill('input[id="password"]', 'user123');
      240 |     await page.click('button[type="submit"]');
    > 241 |     await page.waitForURL('/dashboard');
          |                ^
      242 |
      243 |     // Try to access admin dashboard
      244 |     await page.goto('/dashboard/admin');
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/admin.spec.ts:241:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-admin-Admin-Access-Con-46fad-y-access-to-non-admin-users-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-admin-Admin-Access-Con-46fad-y-access-to-non-admin-users-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-admin-Admin-Access-Con-46fad-y-access-to-non-admin-users-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-admin-Admin-Access-Con-46fad-y-access-to-non-admin-users-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-admin-Admin-Access-Con-46fad-y-access-to-non-admin-users-chromium/video.webm]]

[[ATTACHMENT|e2e-admin-Admin-Access-Con-46fad-y-access-to-non-admin-users-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="e2e/analytics.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="12" failures="3" skipped="0" time="69.38" errors="0">
<testcase name="Analytics Dashboard › should display analytics overview" classname="e2e/analytics.spec.ts" time="0.849">
<failure message="analytics.spec.ts:16:7 should display analytics overview" type="FAILURE">
<![CDATA[  [chromium] › e2e/analytics.spec.ts:16:7 › Analytics Dashboard › should display analytics overview 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      21 |     const metrics = page.locator('.metric, .stat-card, .analytics-card');
      22 |     const metricCount = await metrics.count();
    > 23 |     expect(metricCount).toBeGreaterThan(0);
         |                         ^
      24 |
      25 |     // Should show numbers
      26 |     const numbers = page.locator('text=/\\d+/');
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/analytics.spec.ts:23:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-analytics-Analytics-Da-3d7de--display-analytics-overview-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-analytics-Analytics-Da-3d7de--display-analytics-overview-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-analytics-Analytics-Da-3d7de--display-analytics-overview-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-analytics-Analytics-Da-3d7de--display-analytics-overview-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-analytics-Analytics-Da-3d7de--display-analytics-overview-chromium/video.webm]]

[[ATTACHMENT|e2e-analytics-Analytics-Da-3d7de--display-analytics-overview-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Analytics Dashboard › should display quiz performance metrics" classname="e2e/analytics.spec.ts" time="0.791">
</testcase>
<testcase name="Analytics Dashboard › should display user engagement metrics" classname="e2e/analytics.spec.ts" time="0.794">
</testcase>
<testcase name="Analytics Dashboard › should display charts and graphs" classname="e2e/analytics.spec.ts" time="0.749">
</testcase>
<testcase name="Analytics Dashboard › should filter analytics by date range" classname="e2e/analytics.spec.ts" time="0.727">
</testcase>
<testcase name="Analytics Dashboard › should export analytics data" classname="e2e/analytics.spec.ts" time="0.699">
</testcase>
<testcase name="Analytics Dashboard › should display quiz-specific analytics" classname="e2e/analytics.spec.ts" time="0.736">
</testcase>
<testcase name="Analytics Dashboard › should show question-level analytics" classname="e2e/analytics.spec.ts" time="0.859">
</testcase>
<testcase name="Analytics Dashboard › should display real-time analytics" classname="e2e/analytics.spec.ts" time="0.719">
</testcase>
<testcase name="Analytics Dashboard › should handle analytics refresh" classname="e2e/analytics.spec.ts" time="0.711">
</testcase>
<testcase name="Analytics - User View › should show user personal analytics" classname="e2e/analytics.spec.ts" time="30.894">
<failure message="analytics.spec.ts:197:7 should show user personal analytics" type="FAILURE">
<![CDATA[  [chromium] › e2e/analytics.spec.ts:197:7 › Analytics - User View › should show user personal analytics 

    Test timeout of 30000ms exceeded while running "beforeEach" hook.

      186 |
      187 | test.describe('Analytics - User View', () => {
    > 188 |   test.beforeEach(async ({ page }) => {
          |        ^
      189 |     // Login as regular user
      190 |     await page.goto('/auth/login');
      191 |     await page.fill('input[id="email"]', '<EMAIL>');
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/analytics.spec.ts:188:8

    Error: page.waitForURL: Test timeout of 30000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "/dashboard" until "load"
    ============================================================

      192 |     await page.fill('input[id="password"]', 'user123');
      193 |     await page.click('button[type="submit"]');
    > 194 |     await page.waitForURL('/dashboard');
          |                ^
      195 |   });
      196 |
      197 |   test('should show user personal analytics', async ({ page }) => {
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/analytics.spec.ts:194:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-analytics-Analytics----cfd38-how-user-personal-analytics-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-analytics-Analytics----cfd38-how-user-personal-analytics-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-analytics-Analytics----cfd38-how-user-personal-analytics-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-analytics-Analytics----cfd38-how-user-personal-analytics-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-analytics-Analytics----cfd38-how-user-personal-analytics-chromium/video.webm]]

[[ATTACHMENT|e2e-analytics-Analytics----cfd38-how-user-personal-analytics-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Analytics - User View › should not show admin analytics" classname="e2e/analytics.spec.ts" time="30.852">
<failure message="analytics.spec.ts:217:7 should not show admin analytics" type="FAILURE">
<![CDATA[  [chromium] › e2e/analytics.spec.ts:217:7 › Analytics - User View › should not show admin analytics 

    Test timeout of 30000ms exceeded while running "beforeEach" hook.

      186 |
      187 | test.describe('Analytics - User View', () => {
    > 188 |   test.beforeEach(async ({ page }) => {
          |        ^
      189 |     // Login as regular user
      190 |     await page.goto('/auth/login');
      191 |     await page.fill('input[id="email"]', '<EMAIL>');
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/analytics.spec.ts:188:8

    Error: page.waitForURL: Test timeout of 30000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "/dashboard" until "load"
    ============================================================

      192 |     await page.fill('input[id="password"]', 'user123');
      193 |     await page.click('button[type="submit"]');
    > 194 |     await page.waitForURL('/dashboard');
          |                ^
      195 |   });
      196 |
      197 |   test('should show user personal analytics', async ({ page }) => {
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/analytics.spec.ts:194:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-analytics-Analytics----09f2d-ld-not-show-admin-analytics-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-analytics-Analytics----09f2d-ld-not-show-admin-analytics-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-analytics-Analytics----09f2d-ld-not-show-admin-analytics-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-analytics-Analytics----09f2d-ld-not-show-admin-analytics-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-analytics-Analytics----09f2d-ld-not-show-admin-analytics-chromium/video.webm]]

[[ATTACHMENT|e2e-analytics-Analytics----09f2d-ld-not-show-admin-analytics-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="e2e/auth-simple.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="3" failures="0" skipped="0" time="4.796" errors="0">
<testcase name="Simple Authentication Test › should create test user and login successfully" classname="e2e/auth-simple.spec.ts" time="1.907">
</testcase>
<testcase name="Simple Authentication Test › should create admin user and access admin panel" classname="e2e/auth-simple.spec.ts" time="1.964">
</testcase>
<testcase name="Simple Authentication Test › should handle invalid credentials" classname="e2e/auth-simple.spec.ts" time="0.925">
</testcase>
</testsuite>
<testsuite name="e2e/authentication.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="11" failures="5" skipped="0" time="91.507" errors="0">
<testcase name="Authentication Flow › should redirect unauthenticated users to login" classname="e2e/authentication.spec.ts" time="0.86">
</testcase>
<testcase name="Authentication Flow › should handle login with test user" classname="e2e/authentication.spec.ts" time="7.327">
<failure message="authentication.spec.ts:19:7 should handle login with test user" type="FAILURE">
<![CDATA[  [chromium] › e2e/authentication.spec.ts:19:7 › Authentication Flow › should handle login with test user 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text="Welcome"')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - expect.toBeVisible with timeout 5000ms
      - waiting for locator('text="Welcome"')


      23 |     // Verify we're on dashboard
      24 |     await expect(page).toHaveURL(/\/dashboard/);
    > 25 |     await expect(page.locator('text="Welcome"')).toBeVisible();
         |                                                  ^
      26 |     await expect(page.locator('button:has-text("Sign Out")')).toBeVisible();
      27 |   });
      28 |
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/authentication.spec.ts:25:50

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-authentication-Authent-f3ada-handle-login-with-test-user-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-authentication-Authent-f3ada-handle-login-with-test-user-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-authentication-Authent-f3ada-handle-login-with-test-user-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-authentication-Authent-f3ada-handle-login-with-test-user-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-authentication-Authent-f3ada-handle-login-with-test-user-chromium/video.webm]]

[[ATTACHMENT|e2e-authentication-Authent-f3ada-handle-login-with-test-user-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Authentication Flow › should handle admin login" classname="e2e/authentication.spec.ts" time="7.498">
<failure message="authentication.spec.ts:29:7 should handle admin login" type="FAILURE">
<![CDATA[  [chromium] › e2e/authentication.spec.ts:29:7 › Authentication Flow › should handle admin login ───

    Error: Timed out 5000ms waiting for expect(locator).toHaveURL(expected)

    Locator: locator(':root')
    Expected pattern: /\/dashboard/
    Received string:  "http://localhost:3000/admin"
    Call log:
      - expect.toHaveURL with timeout 5000ms
      - waiting for locator(':root')
        9 × locator resolved to <html lang="en" class="light">…</html>
          - unexpected value "http://localhost:3000/admin"


      32 |
      33 |     // Verify we're on dashboard
    > 34 |     await expect(page).toHaveURL(/\/dashboard/);
         |                        ^
      35 |     await expect(page.locator('text="Welcome"')).toBeVisible();
      36 |
      37 |     // Verify admin can access admin panel
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/authentication.spec.ts:34:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-authentication-Authent-01e26-w-should-handle-admin-login-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-authentication-Authent-01e26-w-should-handle-admin-login-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-authentication-Authent-01e26-w-should-handle-admin-login-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-authentication-Authent-01e26-w-should-handle-admin-login-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-authentication-Authent-01e26-w-should-handle-admin-login-chromium/video.webm]]

[[ATTACHMENT|e2e-authentication-Authent-01e26-w-should-handle-admin-login-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Authentication Flow › should show error for invalid credentials" classname="e2e/authentication.spec.ts" time="1.077">
</testcase>
<testcase name="Authentication Flow › should allow user registration" classname="e2e/authentication.spec.ts" time="30.847">
<failure message="authentication.spec.ts:59:7 should allow user registration" type="FAILURE">
<![CDATA[  [chromium] › e2e/authentication.spec.ts:59:7 › Authentication Flow › should allow user registration 

    Test timeout of 30000ms exceeded.

    Error: page.waitForURL: Test timeout of 30000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "/dashboard" until "load"
      navigated to "http://localhost:3000/auth/login?registered=true"
    ============================================================

      70 |
      71 |     // Should redirect to dashboard or show success message
    > 72 |     await page.waitForURL('/dashboard');
         |                ^
      73 |     expect(page.url()).toContain('/dashboard');
      74 |   });
      75 |
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/authentication.spec.ts:72:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-authentication-Authent-aa752-uld-allow-user-registration-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-authentication-Authent-aa752-uld-allow-user-registration-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-authentication-Authent-aa752-uld-allow-user-registration-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-authentication-Authent-aa752-uld-allow-user-registration-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-authentication-Authent-aa752-uld-allow-user-registration-chromium/video.webm]]

[[ATTACHMENT|e2e-authentication-Authent-aa752-uld-allow-user-registration-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Authentication Flow › should validate registration form" classname="e2e/authentication.spec.ts" time="0.983">
</testcase>
<testcase name="Authentication Flow › should validate password confirmation" classname="e2e/authentication.spec.ts" time="0.929">
</testcase>
<testcase name="Authentication Flow › should handle logout" classname="e2e/authentication.spec.ts" time="3.127">
</testcase>
<testcase name="Authentication Flow › should persist session across page reloads" classname="e2e/authentication.spec.ts" time="6.433">
<failure message="authentication.spec.ts:115:7 should persist session across page reloads" type="FAILURE">
<![CDATA[  [chromium] › e2e/authentication.spec.ts:115:7 › Authentication Flow › should persist session across page reloads 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Create Quiz')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - expect.toBeVisible with timeout 5000ms
      - waiting for locator('text=Create Quiz')


      127 |     // Should still be logged in
      128 |     expect(page.url()).toContain('/dashboard');
    > 129 |     await expect(page.locator('text=Create Quiz')).toBeVisible();
          |                                                    ^
      130 |   });
      131 |
      132 |   test('should handle session expiration', async ({ page }) => {
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/authentication.spec.ts:129:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-authentication-Authent-c09be-session-across-page-reloads-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-authentication-Authent-c09be-session-across-page-reloads-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-authentication-Authent-c09be-session-across-page-reloads-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-authentication-Authent-c09be-session-across-page-reloads-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-authentication-Authent-c09be-session-across-page-reloads-chromium/video.webm]]

[[ATTACHMENT|e2e-authentication-Authent-c09be-session-across-page-reloads-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Authentication Flow › should handle session expiration" classname="e2e/authentication.spec.ts" time="1.578">
</testcase>
<testcase name="Authentication Flow › should restrict admin features to admin users" classname="e2e/authentication.spec.ts" time="30.848">
<failure message="authentication.spec.ts:151:7 should restrict admin features to admin users" type="FAILURE">
<![CDATA[  [chromium] › e2e/authentication.spec.ts:151:7 › Authentication Flow › should restrict admin features to admin users 

    Test timeout of 30000ms exceeded.

    Error: page.waitForURL: Test timeout of 30000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "/dashboard" until "load"
    ============================================================

      156 |     await page.click('button[type="submit"]');
      157 |
    > 158 |     await page.waitForURL('/dashboard');
          |                ^
      159 |
      160 |     // Try to access quiz creation (admin only)
      161 |     await page.goto('/dashboard/quizzes/create');
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/authentication.spec.ts:158:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-authentication-Authent-7e534-min-features-to-admin-users-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-authentication-Authent-7e534-min-features-to-admin-users-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-authentication-Authent-7e534-min-features-to-admin-users-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-authentication-Authent-7e534-min-features-to-admin-users-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-authentication-Authent-7e534-min-features-to-admin-users-chromium/video.webm]]

[[ATTACHMENT|e2e-authentication-Authent-7e534-min-features-to-admin-users-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="e2e/code-snippet.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="4" failures="4" skipped="0" time="45.476" errors="0">
<testcase name="Code Snippet Functionality › should display code snippets with syntax highlighting" classname="e2e/code-snippet.spec.ts" time="12.448">
<failure message="code-snippet.spec.ts:4:7 should display code snippets with syntax highlighting" type="FAILURE">
<![CDATA[  [chromium] › e2e/code-snippet.spec.ts:4:7 › Code Snippet Functionality › should display code snippets with syntax highlighting 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="question-renderer"], .question-container') to be visible


       8 |
       9 |     // Wait for the quiz to load
    > 10 |     await page.waitForSelector('[data-testid="question-renderer"], .question-container', { timeout: 10000 });
         |                ^
      11 |
      12 |     // Look for code snippet elements
      13 |     const codeSnippet = page.locator('.react-syntax-highlighter, pre code, .code-snippet');
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/code-snippet.spec.ts:10:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-code-snippet-Code-Snip-f7ff0-ts-with-syntax-highlighting-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-code-snippet-Code-Snip-f7ff0-ts-with-syntax-highlighting-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-code-snippet-Code-Snip-f7ff0-ts-with-syntax-highlighting-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-code-snippet-Code-Snip-f7ff0-ts-with-syntax-highlighting-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-code-snippet-Code-Snip-f7ff0-ts-with-syntax-highlighting-chromium/video.webm]]

[[ATTACHMENT|e2e-code-snippet-Code-Snip-f7ff0-ts-with-syntax-highlighting-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Code Snippet Functionality › should handle code snippet copy functionality" classname="e2e/code-snippet.spec.ts" time="10.99">
<failure message="code-snippet.spec.ts:66:7 should handle code snippet copy functionality" type="FAILURE">
<![CDATA[  [chromium] › e2e/code-snippet.spec.ts:66:7 › Code Snippet Functionality › should handle code snippet copy functionality 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="question-renderer"], .question-container') to be visible


      70 |
      71 |     // Wait for the quiz to load
    > 72 |     await page.waitForSelector('[data-testid="question-renderer"], .question-container', { timeout: 10000 });
         |                ^
      73 |
      74 |     // Look for copy button
      75 |     const copyButton = page.locator('button[title="Copy code"], button:has-text("Copy")');
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/code-snippet.spec.ts:72:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-code-snippet-Code-Snip-ba5a1--snippet-copy-functionality-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-code-snippet-Code-Snip-ba5a1--snippet-copy-functionality-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-code-snippet-Code-Snip-ba5a1--snippet-copy-functionality-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-code-snippet-Code-Snip-ba5a1--snippet-copy-functionality-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-code-snippet-Code-Snip-ba5a1--snippet-copy-functionality-chromium/video.webm]]

[[ATTACHMENT|e2e-code-snippet-Code-Snip-ba5a1--snippet-copy-functionality-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Code Snippet Functionality › should display code snippet metadata correctly" classname="e2e/code-snippet.spec.ts" time="11.067">
<failure message="code-snippet.spec.ts:95:7 should display code snippet metadata correctly" type="FAILURE">
<![CDATA[  [chromium] › e2e/code-snippet.spec.ts:95:7 › Code Snippet Functionality › should display code snippet metadata correctly 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="question-renderer"], .question-container') to be visible


       99 |
      100 |     // Wait for the quiz to load
    > 101 |     await page.waitForSelector('[data-testid="question-renderer"], .question-container', { timeout: 10000 });
          |                ^
      102 |
      103 |     // Check for code snippet card structure
      104 |     const codeCard = page.locator('.code-snippet, [class*="Card"]').filter({ has: page.locator('pre, code') });
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/code-snippet.spec.ts:101:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-code-snippet-Code-Snip-1d58a--snippet-metadata-correctly-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-code-snippet-Code-Snip-1d58a--snippet-metadata-correctly-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-code-snippet-Code-Snip-1d58a--snippet-metadata-correctly-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-code-snippet-Code-Snip-1d58a--snippet-metadata-correctly-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-code-snippet-Code-Snip-1d58a--snippet-metadata-correctly-chromium/video.webm]]

[[ATTACHMENT|e2e-code-snippet-Code-Snip-1d58a--snippet-metadata-correctly-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Code Snippet Functionality › should handle questions without code snippets" classname="e2e/code-snippet.spec.ts" time="10.971">
<failure message="code-snippet.spec.ts:132:7 should handle questions without code snippets" type="FAILURE">
<![CDATA[  [chromium] › e2e/code-snippet.spec.ts:132:7 › Code Snippet Functionality › should handle questions without code snippets 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="question-renderer"], .question-container') to be visible


      136 |
      137 |     // Wait for the quiz to load
    > 138 |     await page.waitForSelector('[data-testid="question-renderer"], .question-container', { timeout: 10000 });
          |                ^
      139 |
      140 |     // Navigate through questions to find one without code snippet
      141 |     let foundQuestionWithoutCode = false;
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/code-snippet.spec.ts:138:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-code-snippet-Code-Snip-96581-tions-without-code-snippets-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-code-snippet-Code-Snip-96581-tions-without-code-snippets-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-code-snippet-Code-Snip-96581-tions-without-code-snippets-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-code-snippet-Code-Snip-96581-tions-without-code-snippets-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-code-snippet-Code-Snip-96581-tions-without-code-snippets-chromium/video.webm]]

[[ATTACHMENT|e2e-code-snippet-Code-Snip-96581-tions-without-code-snippets-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="e2e/dashboard.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="12" failures="3" skipped="0" time="68.705" errors="0">
<testcase name="Dashboard › should display dashboard overview" classname="e2e/dashboard.spec.ts" time="0.758">
</testcase>
<testcase name="Dashboard › should navigate to different dashboard sections" classname="e2e/dashboard.spec.ts" time="0.731">
<failure message="dashboard.spec.ts:35:7 should navigate to different dashboard sections" type="FAILURE">
<![CDATA[  [chromium] › e2e/dashboard.spec.ts:35:7 › Dashboard › should navigate to different dashboard sections 

    Error: page.click: Unexpected token "=" while parsing css selector "a[href="/dashboard/quizzes"], text=Quizzes". Did you mean to CSS.escape it?
    Call log:
      - waiting for a[href="/dashboard/quizzes"], text=Quizzes


      35 |   test('should navigate to different dashboard sections', async ({ page }) => {
      36 |     // Test navigation to quizzes
    > 37 |     await page.click('a[href="/dashboard/quizzes"], text=Quizzes');
         |                ^
      38 |     await page.waitForURL('/dashboard/quizzes');
      39 |     await expect(page.locator('h1')).toContainText('Quizzes');
      40 |
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/dashboard.spec.ts:37:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-dashboard-Dashboard-sh-f8684-ifferent-dashboard-sections-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-dashboard-Dashboard-sh-f8684-ifferent-dashboard-sections-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-dashboard-Dashboard-sh-f8684-ifferent-dashboard-sections-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-dashboard-Dashboard-sh-f8684-ifferent-dashboard-sections-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-dashboard-Dashboard-sh-f8684-ifferent-dashboard-sections-chromium/video.webm]]

[[ATTACHMENT|e2e-dashboard-Dashboard-sh-f8684-ifferent-dashboard-sections-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard › should display user profile information" classname="e2e/dashboard.spec.ts" time="0.737">
</testcase>
<testcase name="Dashboard › should show recent activity" classname="e2e/dashboard.spec.ts" time="0.611">
</testcase>
<testcase name="Dashboard › should display quiz statistics" classname="e2e/dashboard.spec.ts" time="0.584">
</testcase>
<testcase name="Dashboard › should handle responsive design" classname="e2e/dashboard.spec.ts" time="0.787">
</testcase>
<testcase name="Dashboard › should search functionality" classname="e2e/dashboard.spec.ts" time="0.626">
</testcase>
<testcase name="Dashboard › should handle notifications" classname="e2e/dashboard.spec.ts" time="0.649">
</testcase>
<testcase name="Dashboard › should display quick actions" classname="e2e/dashboard.spec.ts" time="0.761">
</testcase>
<testcase name="Dashboard › should handle dark mode toggle" classname="e2e/dashboard.spec.ts" time="0.717">
</testcase>
<testcase name="Dashboard - User Role › should hide admin features for regular users" classname="e2e/dashboard.spec.ts" time="30.88">
<failure message="dashboard.spec.ts:189:7 should hide admin features for regular users" type="FAILURE">
<![CDATA[  [chromium] › e2e/dashboard.spec.ts:189:7 › Dashboard - User Role › should hide admin features for regular users 

    Test timeout of 30000ms exceeded while running "beforeEach" hook.

      178 |
      179 | test.describe('Dashboard - User Role', () => {
    > 180 |   test.beforeEach(async ({ page }) => {
          |        ^
      181 |     // Login as regular user
      182 |     await page.goto('/auth/login');
      183 |     await page.fill('input[id="email"]', '<EMAIL>');
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/dashboard.spec.ts:180:8

    Error: page.waitForURL: Test timeout of 30000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "/dashboard" until "load"
    ============================================================

      184 |     await page.fill('input[id="password"]', 'user123');
      185 |     await page.click('button[type="submit"]');
    > 186 |     await page.waitForURL('/dashboard');
          |                ^
      187 |   });
      188 |
      189 |   test('should hide admin features for regular users', async ({ page }) => {
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/dashboard.spec.ts:186:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-dashboard-Dashboard----fbbe1--features-for-regular-users-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-dashboard-Dashboard----fbbe1--features-for-regular-users-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-dashboard-Dashboard----fbbe1--features-for-regular-users-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-dashboard-Dashboard----fbbe1--features-for-regular-users-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-dashboard-Dashboard----fbbe1--features-for-regular-users-chromium/video.webm]]

[[ATTACHMENT|e2e-dashboard-Dashboard----fbbe1--features-for-regular-users-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard - User Role › should show user-specific dashboard content" classname="e2e/dashboard.spec.ts" time="30.864">
<failure message="dashboard.spec.ts:200:7 should show user-specific dashboard content" type="FAILURE">
<![CDATA[  [chromium] › e2e/dashboard.spec.ts:200:7 › Dashboard - User Role › should show user-specific dashboard content 

    Test timeout of 30000ms exceeded while running "beforeEach" hook.

      178 |
      179 | test.describe('Dashboard - User Role', () => {
    > 180 |   test.beforeEach(async ({ page }) => {
          |        ^
      181 |     // Login as regular user
      182 |     await page.goto('/auth/login');
      183 |     await page.fill('input[id="email"]', '<EMAIL>');
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/dashboard.spec.ts:180:8

    Error: page.waitForURL: Test timeout of 30000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "/dashboard" until "load"
    ============================================================

      184 |     await page.fill('input[id="password"]', 'user123');
      185 |     await page.click('button[type="submit"]');
    > 186 |     await page.waitForURL('/dashboard');
          |                ^
      187 |   });
      188 |
      189 |   test('should hide admin features for regular users', async ({ page }) => {
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/dashboard.spec.ts:186:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-dashboard-Dashboard----b34f7--specific-dashboard-content-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-dashboard-Dashboard----b34f7--specific-dashboard-content-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-dashboard-Dashboard----b34f7--specific-dashboard-content-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-dashboard-Dashboard----b34f7--specific-dashboard-content-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-dashboard-Dashboard----b34f7--specific-dashboard-content-chromium/video.webm]]

[[ATTACHMENT|e2e-dashboard-Dashboard----b34f7--specific-dashboard-content-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="e2e/diagnostic-real-issues.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="4" failures="1" skipped="0" time="24.966" errors="0">
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check quiz list page functionality" classname="e2e/diagnostic-real-issues.spec.ts" time="3.733">
<system-out>
<![CDATA[🔍 Testing Quiz List Page...
Quiz list page title: QuizFlow - Interactive Quiz Ecosystem
✅ Quiz list page loads
Quiz cards found: [33m0[39m
⚠️ No quiz cards found - either no quizzes exist or UI not implemented
Page content preview: ((e, i, s, u, m, a, l, h)=>{
    let d = document.documentElement, w = [
        "light",
        "dark"
    ];
    function p(n) {
        (Array.isArray(e) ? e : [
            e
        ]).forEach((y)=>{
            let k = y === "class", S = k && a ? m.map((f)=>a[f] || f) : m;
            k ? (d.
]]>
</system-out>
</testcase>
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check quiz edit route directly" classname="e2e/diagnostic-real-issues.spec.ts" time="5.101">
<system-out>
<![CDATA[🔍 Testing Quiz Edit Route Directly...
Created quiz with ID: 683ce1d5e712f6dfc47b0324
✅ Current edit page loads
Testing direct navigation to: /dashboard/quizzes/683ce1d5e712f6dfc47b0324/edit
✅ Direct navigation to edit page works
]]>
</system-out>
</testcase>
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check question management UI" classname="e2e/diagnostic-real-issues.spec.ts" time="3.923">
<system-out>
<![CDATA[🔍 Testing Question Management UI...
✅ Edit page loads
Questions tab exists: [33mtrue[39m
Add Question button exists: [33mtrue[39m
Question type select exists: [33mtrue[39m
Question text area exists: [33mtrue[39m
Points input exists: [33mtrue[39m
Option inputs found: [33m4[39m
✅ Question form UI appears to be implemented
]]>
</system-out>
</testcase>
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check question addition workflow" classname="e2e/diagnostic-real-issues.spec.ts" time="12.209">
<failure message="diagnostic-real-issues.spec.ts:205:7 DIAGNOSTIC: Check question addition workflow" type="FAILURE">
<![CDATA[  [chromium] › e2e/diagnostic-real-issues.spec.ts:205:7 › Diagnostic Tests - Real Issues › DIAGNOSTIC: Check question addition workflow 

    TimeoutError: locator.textContent: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Questions (')


      222 |
      223 |     // Check initial state
    > 224 |     const initialQuestionCount = await page.locator('text=Questions (').textContent();
          |                                                                         ^
      225 |     console.log('Initial question count text:', initialQuestionCount);
      226 |
      227 |     // Click Add Question tab
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/diagnostic-real-issues.spec.ts:224:73

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-diagnostic-real-issues-87621--question-addition-workflow-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-diagnostic-real-issues-87621--question-addition-workflow-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/e2e-diagnostic-real-issues-87621--question-addition-workflow-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔍 Testing Question Addition Workflow...
✅ Quiz created and edit page loaded
✅ Questions tab clicked

[[ATTACHMENT|e2e-diagnostic-real-issues-87621--question-addition-workflow-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-diagnostic-real-issues-87621--question-addition-workflow-chromium/video.webm]]

[[ATTACHMENT|e2e-diagnostic-real-issues-87621--question-addition-workflow-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="e2e/error-handling.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="16" failures="2" skipped="12" time="3.863" errors="0">
<testcase name="Error Handling › should handle 404 errors gracefully" classname="e2e/error-handling.spec.ts" time="0.308">
</testcase>
<testcase name="Error Handling › should handle network errors" classname="e2e/error-handling.spec.ts" time="0.504">
<failure message="error-handling.spec.ts:19:7 should handle network errors" type="FAILURE">
<![CDATA[  [chromium] › e2e/error-handling.spec.ts:19:7 › Error Handling › should handle network errors ─────

    Error: locator.count: Unexpected token "=" while parsing css selector ".error-message, .alert-error, text=error, text=failed". Did you mean to CSS.escape it?

      28 |     // Should show error message
      29 |     const errorMessage = page.locator('.error-message, .alert-error, text=error, text=failed');
    > 30 |     if (await errorMessage.count() > 0) {
         |                            ^
      31 |       await expect(errorMessage.first()).toBeVisible();
      32 |     }
      33 |   });
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/error-handling.spec.ts:30:28

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-error-handling-Error-H-7c1cd-hould-handle-network-errors-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-error-handling-Error-H-7c1cd-hould-handle-network-errors-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-error-handling-Error-H-7c1cd-hould-handle-network-errors-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-error-handling-Error-H-7c1cd-hould-handle-network-errors-chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Error Handling › should handle server errors (500)" classname="e2e/error-handling.spec.ts" time="0.401">
<failure message="error-handling.spec.ts:35:7 should handle server errors (500)" type="FAILURE">
<![CDATA[  [chromium] › e2e/error-handling.spec.ts:35:7 › Error Handling › should handle server errors (500) 

    Error: locator.count: Unexpected token "=" while parsing css selector ".error, .alert, text=error, text=something went wrong". Did you mean to CSS.escape it?

      50 |     // Should handle server error gracefully
      51 |     const errorIndicator = page.locator('.error, .alert, text=error, text=something went wrong');
    > 52 |     if (await errorIndicator.count() > 0) {
         |                              ^
      53 |       await expect(errorIndicator.first()).toBeVisible();
      54 |     }
      55 |   });
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/error-handling.spec.ts:52:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-error-handling-Error-H-fe916-d-handle-server-errors-500--chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/e2e-error-handling-Error-H-fe916-d-handle-server-errors-500--chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-error-handling-Error-H-fe916-d-handle-server-errors-500--chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-error-handling-Error-H-fe916-d-handle-server-errors-500--chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Error Handling › should handle authentication errors" classname="e2e/error-handling.spec.ts" time="0.448">
</testcase>
<testcase name="Error Handling › should handle session expiration" classname="e2e/error-handling.spec.ts" time="2.202">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle form validation errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle quiz loading errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle file upload errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle database connection errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle permission errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle timeout errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle JavaScript errors gracefully" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should provide error recovery options" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle malformed data gracefully" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle concurrent request errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should show user-friendly error messages" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/mobile.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="13" failures="0" skipped="13" time="0" errors="0">
<testcase name="Mobile Tests › should display mobile-friendly login" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should have mobile navigation" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should display quiz cards responsively" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle touch interactions" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should support swipe gestures" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should optimize for different mobile sizes" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile form inputs" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should support mobile accessibility" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile quiz taking" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile orientation changes" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should optimize mobile performance" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile search" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should support mobile gestures in quiz" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-creation.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Quiz Creation - All Possibilities › should create basic quiz with minimal required fields" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with all optional fields" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with different difficulty levels" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with different categories" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with time limits" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with different passing scores" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with multiple tags" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should handle special characters in quiz data" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with maximum field lengths" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-editing.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Quiz Editing - All Possibilities › should edit basic quiz information" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should edit quiz settings" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should edit existing questions" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should delete questions from quiz" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should reorder questions in quiz" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should validate quiz before saving" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-editor.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="Quiz Editor › should create a new quiz" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editor › should add a multiple choice question" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editor › should validate required fields" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editor › should handle different question types" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-preview.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="5" failures="0" skipped="5" time="0" errors="0">
<testcase name="Quiz Preview Functionality › debug - check quiz routes and 404 errors" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should test preview button functionality" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should exit preview mode" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should preview quiz with different settings" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should handle preview with no questions gracefully" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-search.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Quiz Search and Discovery › should search quizzes by title" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should filter quizzes by category" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should filter quizzes by difficulty" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should sort quizzes" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should show quiz details in search results" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should handle empty search results" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should clear search filters" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should paginate search results" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should show recent searches" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should suggest search terms" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should handle advanced search" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should save search preferences" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-taking.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="13" failures="0" skipped="13" time="0" errors="0">
<testcase name="Quiz Taking Experience › should display available quizzes" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should start a quiz" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should answer multiple choice questions" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should answer true/false questions" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should show progress indicator" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should allow navigation between questions" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should show hints when available" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should submit quiz and show results" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should save quiz progress" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should handle quiz timer if present" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should show explanations after answering" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should handle different question types in same quiz" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should validate required answers" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="security/answer-exposure.test.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Answer Exposure Security Tests › should not expose correct answers in quiz API response" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should not expose answers in questions API endpoint" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should require authentication for quiz submission" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should validate answers server-side" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should prevent rapid quiz submissions" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should detect suspicious activity" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should handle invalid quiz submission data" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should not allow access to non-existent quiz" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="security/client-side-security.test.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="chromium" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Client-Side Security Tests › should not expose answers in browser console" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose answers in page source" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose answers in network requests" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not allow answer manipulation via browser DevTools" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should handle quiz submission securely" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose database queries or internal errors" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should implement proper CSRF protection" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should sanitize user input in quiz content" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose sensitive configuration" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/accessibility.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Accessibility Tests › should have proper heading hierarchy" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper form labels" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper button accessibility" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper link accessibility" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper image alt text" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should support keyboard navigation" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper focus indicators" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper color contrast" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper ARIA attributes" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should support screen reader navigation" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should handle high contrast mode" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should support reduced motion preferences" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper form validation messages" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should support zoom up to 200%" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper table accessibility" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should handle error states accessibly" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/admin.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Admin Dashboard › should display admin dashboard" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should manage users" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should create new user" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should manage quiz settings" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should configure system settings" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should view system logs" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should manage categories and tags" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should backup and restore data" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should monitor system health" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should manage permissions and roles" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should handle bulk operations" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Access Control › should deny access to non-admin users" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/analytics.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Analytics Dashboard › should display analytics overview" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display quiz performance metrics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display user engagement metrics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display charts and graphs" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should filter analytics by date range" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should export analytics data" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display quiz-specific analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should show question-level analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display real-time analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should handle analytics refresh" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics - User View › should show user personal analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics - User View › should not show admin analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/auth-simple.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="3" failures="0" skipped="3" time="0" errors="0">
<testcase name="Simple Authentication Test › should create test user and login successfully" classname="e2e/auth-simple.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Simple Authentication Test › should create admin user and access admin panel" classname="e2e/auth-simple.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Simple Authentication Test › should handle invalid credentials" classname="e2e/auth-simple.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/authentication.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Authentication Flow › should redirect unauthenticated users to login" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle login with test user" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle admin login" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show error for invalid credentials" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should allow user registration" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should validate registration form" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should validate password confirmation" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle logout" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should persist session across page reloads" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle session expiration" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should restrict admin features to admin users" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/code-snippet.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="Code Snippet Functionality › should display code snippets with syntax highlighting" classname="e2e/code-snippet.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Code Snippet Functionality › should handle code snippet copy functionality" classname="e2e/code-snippet.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Code Snippet Functionality › should display code snippet metadata correctly" classname="e2e/code-snippet.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Code Snippet Functionality › should handle questions without code snippets" classname="e2e/code-snippet.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/dashboard.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Dashboard › should display dashboard overview" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should navigate to different dashboard sections" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should display user profile information" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should show recent activity" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should display quiz statistics" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should handle responsive design" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should search functionality" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should handle notifications" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should display quick actions" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should handle dark mode toggle" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard - User Role › should hide admin features for regular users" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard - User Role › should show user-specific dashboard content" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/diagnostic-real-issues.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check quiz list page functionality" classname="e2e/diagnostic-real-issues.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check quiz edit route directly" classname="e2e/diagnostic-real-issues.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check question management UI" classname="e2e/diagnostic-real-issues.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check question addition workflow" classname="e2e/diagnostic-real-issues.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/error-handling.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Error Handling › should handle 404 errors gracefully" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle network errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle server errors (500)" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle authentication errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle session expiration" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle form validation errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle quiz loading errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle file upload errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle database connection errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle permission errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle timeout errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle JavaScript errors gracefully" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should provide error recovery options" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle malformed data gracefully" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle concurrent request errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should show user-friendly error messages" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/mobile.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="13" failures="0" skipped="13" time="0" errors="0">
<testcase name="Mobile Tests › should display mobile-friendly login" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should have mobile navigation" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should display quiz cards responsively" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle touch interactions" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should support swipe gestures" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should optimize for different mobile sizes" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile form inputs" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should support mobile accessibility" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile quiz taking" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile orientation changes" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should optimize mobile performance" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile search" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should support mobile gestures in quiz" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-creation.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Quiz Creation - All Possibilities › should create basic quiz with minimal required fields" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with all optional fields" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with different difficulty levels" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with different categories" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with time limits" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with different passing scores" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with multiple tags" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should handle special characters in quiz data" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with maximum field lengths" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-editing.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Quiz Editing - All Possibilities › should edit basic quiz information" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should edit quiz settings" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should edit existing questions" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should delete questions from quiz" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should reorder questions in quiz" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should validate quiz before saving" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-editor.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="Quiz Editor › should create a new quiz" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editor › should add a multiple choice question" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editor › should validate required fields" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editor › should handle different question types" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-preview.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="5" failures="0" skipped="5" time="0" errors="0">
<testcase name="Quiz Preview Functionality › debug - check quiz routes and 404 errors" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should test preview button functionality" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should exit preview mode" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should preview quiz with different settings" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should handle preview with no questions gracefully" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-search.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Quiz Search and Discovery › should search quizzes by title" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should filter quizzes by category" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should filter quizzes by difficulty" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should sort quizzes" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should show quiz details in search results" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should handle empty search results" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should clear search filters" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should paginate search results" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should show recent searches" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should suggest search terms" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should handle advanced search" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should save search preferences" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-taking.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="13" failures="0" skipped="13" time="0" errors="0">
<testcase name="Quiz Taking Experience › should display available quizzes" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should start a quiz" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should answer multiple choice questions" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should answer true/false questions" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should show progress indicator" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should allow navigation between questions" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should show hints when available" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should submit quiz and show results" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should save quiz progress" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should handle quiz timer if present" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should show explanations after answering" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should handle different question types in same quiz" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should validate required answers" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="security/answer-exposure.test.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Answer Exposure Security Tests › should not expose correct answers in quiz API response" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should not expose answers in questions API endpoint" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should require authentication for quiz submission" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should validate answers server-side" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should prevent rapid quiz submissions" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should detect suspicious activity" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should handle invalid quiz submission data" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should not allow access to non-existent quiz" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="security/client-side-security.test.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="firefox" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Client-Side Security Tests › should not expose answers in browser console" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose answers in page source" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose answers in network requests" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not allow answer manipulation via browser DevTools" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should handle quiz submission securely" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose database queries or internal errors" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should implement proper CSRF protection" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should sanitize user input in quiz content" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose sensitive configuration" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/accessibility.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Accessibility Tests › should have proper heading hierarchy" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper form labels" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper button accessibility" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper link accessibility" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper image alt text" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should support keyboard navigation" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper focus indicators" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper color contrast" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper ARIA attributes" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should support screen reader navigation" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should handle high contrast mode" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should support reduced motion preferences" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper form validation messages" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should support zoom up to 200%" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper table accessibility" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should handle error states accessibly" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/admin.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Admin Dashboard › should display admin dashboard" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should manage users" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should create new user" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should manage quiz settings" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should configure system settings" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should view system logs" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should manage categories and tags" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should backup and restore data" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should monitor system health" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should manage permissions and roles" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should handle bulk operations" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Access Control › should deny access to non-admin users" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/analytics.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Analytics Dashboard › should display analytics overview" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display quiz performance metrics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display user engagement metrics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display charts and graphs" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should filter analytics by date range" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should export analytics data" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display quiz-specific analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should show question-level analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display real-time analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should handle analytics refresh" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics - User View › should show user personal analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics - User View › should not show admin analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/auth-simple.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="3" failures="0" skipped="3" time="0" errors="0">
<testcase name="Simple Authentication Test › should create test user and login successfully" classname="e2e/auth-simple.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Simple Authentication Test › should create admin user and access admin panel" classname="e2e/auth-simple.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Simple Authentication Test › should handle invalid credentials" classname="e2e/auth-simple.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/authentication.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Authentication Flow › should redirect unauthenticated users to login" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle login with test user" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle admin login" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show error for invalid credentials" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should allow user registration" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should validate registration form" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should validate password confirmation" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle logout" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should persist session across page reloads" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle session expiration" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should restrict admin features to admin users" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/code-snippet.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="Code Snippet Functionality › should display code snippets with syntax highlighting" classname="e2e/code-snippet.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Code Snippet Functionality › should handle code snippet copy functionality" classname="e2e/code-snippet.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Code Snippet Functionality › should display code snippet metadata correctly" classname="e2e/code-snippet.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Code Snippet Functionality › should handle questions without code snippets" classname="e2e/code-snippet.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/dashboard.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Dashboard › should display dashboard overview" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should navigate to different dashboard sections" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should display user profile information" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should show recent activity" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should display quiz statistics" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should handle responsive design" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should search functionality" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should handle notifications" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should display quick actions" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should handle dark mode toggle" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard - User Role › should hide admin features for regular users" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard - User Role › should show user-specific dashboard content" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/diagnostic-real-issues.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check quiz list page functionality" classname="e2e/diagnostic-real-issues.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check quiz edit route directly" classname="e2e/diagnostic-real-issues.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check question management UI" classname="e2e/diagnostic-real-issues.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check question addition workflow" classname="e2e/diagnostic-real-issues.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/error-handling.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Error Handling › should handle 404 errors gracefully" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle network errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle server errors (500)" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle authentication errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle session expiration" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle form validation errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle quiz loading errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle file upload errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle database connection errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle permission errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle timeout errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle JavaScript errors gracefully" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should provide error recovery options" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle malformed data gracefully" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle concurrent request errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should show user-friendly error messages" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/mobile.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="13" failures="0" skipped="13" time="0" errors="0">
<testcase name="Mobile Tests › should display mobile-friendly login" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should have mobile navigation" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should display quiz cards responsively" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle touch interactions" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should support swipe gestures" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should optimize for different mobile sizes" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile form inputs" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should support mobile accessibility" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile quiz taking" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile orientation changes" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should optimize mobile performance" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile search" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should support mobile gestures in quiz" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-creation.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Quiz Creation - All Possibilities › should create basic quiz with minimal required fields" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with all optional fields" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with different difficulty levels" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with different categories" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with time limits" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with different passing scores" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with multiple tags" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should handle special characters in quiz data" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with maximum field lengths" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-editing.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Quiz Editing - All Possibilities › should edit basic quiz information" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should edit quiz settings" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should edit existing questions" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should delete questions from quiz" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should reorder questions in quiz" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should validate quiz before saving" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-editor.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="Quiz Editor › should create a new quiz" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editor › should add a multiple choice question" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editor › should validate required fields" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editor › should handle different question types" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-preview.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="5" failures="0" skipped="5" time="0" errors="0">
<testcase name="Quiz Preview Functionality › debug - check quiz routes and 404 errors" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should test preview button functionality" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should exit preview mode" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should preview quiz with different settings" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should handle preview with no questions gracefully" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-search.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Quiz Search and Discovery › should search quizzes by title" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should filter quizzes by category" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should filter quizzes by difficulty" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should sort quizzes" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should show quiz details in search results" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should handle empty search results" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should clear search filters" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should paginate search results" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should show recent searches" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should suggest search terms" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should handle advanced search" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should save search preferences" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-taking.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="13" failures="0" skipped="13" time="0" errors="0">
<testcase name="Quiz Taking Experience › should display available quizzes" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should start a quiz" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should answer multiple choice questions" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should answer true/false questions" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should show progress indicator" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should allow navigation between questions" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should show hints when available" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should submit quiz and show results" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should save quiz progress" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should handle quiz timer if present" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should show explanations after answering" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should handle different question types in same quiz" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should validate required answers" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="security/answer-exposure.test.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Answer Exposure Security Tests › should not expose correct answers in quiz API response" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should not expose answers in questions API endpoint" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should require authentication for quiz submission" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should validate answers server-side" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should prevent rapid quiz submissions" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should detect suspicious activity" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should handle invalid quiz submission data" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should not allow access to non-existent quiz" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="security/client-side-security.test.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="webkit" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Client-Side Security Tests › should not expose answers in browser console" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose answers in page source" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose answers in network requests" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not allow answer manipulation via browser DevTools" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should handle quiz submission securely" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose database queries or internal errors" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should implement proper CSRF protection" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should sanitize user input in quiz content" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose sensitive configuration" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/accessibility.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Accessibility Tests › should have proper heading hierarchy" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper form labels" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper button accessibility" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper link accessibility" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper image alt text" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should support keyboard navigation" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper focus indicators" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper color contrast" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper ARIA attributes" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should support screen reader navigation" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should handle high contrast mode" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should support reduced motion preferences" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper form validation messages" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should support zoom up to 200%" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper table accessibility" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should handle error states accessibly" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/admin.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Admin Dashboard › should display admin dashboard" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should manage users" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should create new user" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should manage quiz settings" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should configure system settings" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should view system logs" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should manage categories and tags" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should backup and restore data" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should monitor system health" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should manage permissions and roles" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should handle bulk operations" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Access Control › should deny access to non-admin users" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/analytics.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Analytics Dashboard › should display analytics overview" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display quiz performance metrics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display user engagement metrics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display charts and graphs" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should filter analytics by date range" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should export analytics data" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display quiz-specific analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should show question-level analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display real-time analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should handle analytics refresh" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics - User View › should show user personal analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics - User View › should not show admin analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/auth-simple.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="3" failures="0" skipped="3" time="0" errors="0">
<testcase name="Simple Authentication Test › should create test user and login successfully" classname="e2e/auth-simple.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Simple Authentication Test › should create admin user and access admin panel" classname="e2e/auth-simple.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Simple Authentication Test › should handle invalid credentials" classname="e2e/auth-simple.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/authentication.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Authentication Flow › should redirect unauthenticated users to login" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle login with test user" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle admin login" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show error for invalid credentials" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should allow user registration" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should validate registration form" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should validate password confirmation" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle logout" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should persist session across page reloads" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle session expiration" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should restrict admin features to admin users" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/code-snippet.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="Code Snippet Functionality › should display code snippets with syntax highlighting" classname="e2e/code-snippet.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Code Snippet Functionality › should handle code snippet copy functionality" classname="e2e/code-snippet.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Code Snippet Functionality › should display code snippet metadata correctly" classname="e2e/code-snippet.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Code Snippet Functionality › should handle questions without code snippets" classname="e2e/code-snippet.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/dashboard.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Dashboard › should display dashboard overview" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should navigate to different dashboard sections" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should display user profile information" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should show recent activity" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should display quiz statistics" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should handle responsive design" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should search functionality" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should handle notifications" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should display quick actions" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should handle dark mode toggle" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard - User Role › should hide admin features for regular users" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard - User Role › should show user-specific dashboard content" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/diagnostic-real-issues.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check quiz list page functionality" classname="e2e/diagnostic-real-issues.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check quiz edit route directly" classname="e2e/diagnostic-real-issues.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check question management UI" classname="e2e/diagnostic-real-issues.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check question addition workflow" classname="e2e/diagnostic-real-issues.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/error-handling.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Error Handling › should handle 404 errors gracefully" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle network errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle server errors (500)" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle authentication errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle session expiration" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle form validation errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle quiz loading errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle file upload errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle database connection errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle permission errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle timeout errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle JavaScript errors gracefully" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should provide error recovery options" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle malformed data gracefully" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle concurrent request errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should show user-friendly error messages" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/mobile.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="13" failures="0" skipped="13" time="0" errors="0">
<testcase name="Mobile Tests › should display mobile-friendly login" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should have mobile navigation" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should display quiz cards responsively" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle touch interactions" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should support swipe gestures" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should optimize for different mobile sizes" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile form inputs" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should support mobile accessibility" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile quiz taking" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile orientation changes" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should optimize mobile performance" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile search" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should support mobile gestures in quiz" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-creation.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Quiz Creation - All Possibilities › should create basic quiz with minimal required fields" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with all optional fields" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with different difficulty levels" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with different categories" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with time limits" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with different passing scores" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with multiple tags" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should handle special characters in quiz data" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with maximum field lengths" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-editing.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Quiz Editing - All Possibilities › should edit basic quiz information" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should edit quiz settings" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should edit existing questions" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should delete questions from quiz" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should reorder questions in quiz" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should validate quiz before saving" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-editor.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="Quiz Editor › should create a new quiz" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editor › should add a multiple choice question" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editor › should validate required fields" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editor › should handle different question types" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-preview.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="5" failures="0" skipped="5" time="0" errors="0">
<testcase name="Quiz Preview Functionality › debug - check quiz routes and 404 errors" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should test preview button functionality" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should exit preview mode" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should preview quiz with different settings" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should handle preview with no questions gracefully" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-search.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Quiz Search and Discovery › should search quizzes by title" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should filter quizzes by category" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should filter quizzes by difficulty" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should sort quizzes" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should show quiz details in search results" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should handle empty search results" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should clear search filters" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should paginate search results" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should show recent searches" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should suggest search terms" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should handle advanced search" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should save search preferences" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-taking.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="13" failures="0" skipped="13" time="0" errors="0">
<testcase name="Quiz Taking Experience › should display available quizzes" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should start a quiz" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should answer multiple choice questions" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should answer true/false questions" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should show progress indicator" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should allow navigation between questions" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should show hints when available" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should submit quiz and show results" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should save quiz progress" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should handle quiz timer if present" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should show explanations after answering" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should handle different question types in same quiz" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should validate required answers" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="security/answer-exposure.test.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Answer Exposure Security Tests › should not expose correct answers in quiz API response" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should not expose answers in questions API endpoint" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should require authentication for quiz submission" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should validate answers server-side" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should prevent rapid quiz submissions" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should detect suspicious activity" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should handle invalid quiz submission data" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should not allow access to non-existent quiz" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="security/client-side-security.test.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Chrome" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Client-Side Security Tests › should not expose answers in browser console" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose answers in page source" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose answers in network requests" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not allow answer manipulation via browser DevTools" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should handle quiz submission securely" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose database queries or internal errors" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should implement proper CSRF protection" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should sanitize user input in quiz content" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose sensitive configuration" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/accessibility.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Accessibility Tests › should have proper heading hierarchy" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper form labels" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper button accessibility" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper link accessibility" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper image alt text" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should support keyboard navigation" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper focus indicators" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper color contrast" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper ARIA attributes" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should support screen reader navigation" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should handle high contrast mode" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should support reduced motion preferences" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper form validation messages" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should support zoom up to 200%" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should have proper table accessibility" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Tests › should handle error states accessibly" classname="e2e/accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/admin.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Admin Dashboard › should display admin dashboard" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should manage users" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should create new user" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should manage quiz settings" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should configure system settings" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should view system logs" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should manage categories and tags" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should backup and restore data" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should monitor system health" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should manage permissions and roles" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should handle bulk operations" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Access Control › should deny access to non-admin users" classname="e2e/admin.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/analytics.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Analytics Dashboard › should display analytics overview" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display quiz performance metrics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display user engagement metrics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display charts and graphs" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should filter analytics by date range" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should export analytics data" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display quiz-specific analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should show question-level analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should display real-time analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics Dashboard › should handle analytics refresh" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics - User View › should show user personal analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Analytics - User View › should not show admin analytics" classname="e2e/analytics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/auth-simple.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="3" failures="0" skipped="3" time="0" errors="0">
<testcase name="Simple Authentication Test › should create test user and login successfully" classname="e2e/auth-simple.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Simple Authentication Test › should create admin user and access admin panel" classname="e2e/auth-simple.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Simple Authentication Test › should handle invalid credentials" classname="e2e/auth-simple.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/authentication.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Authentication Flow › should redirect unauthenticated users to login" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle login with test user" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle admin login" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show error for invalid credentials" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should allow user registration" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should validate registration form" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should validate password confirmation" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle logout" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should persist session across page reloads" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should handle session expiration" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should restrict admin features to admin users" classname="e2e/authentication.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/code-snippet.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="Code Snippet Functionality › should display code snippets with syntax highlighting" classname="e2e/code-snippet.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Code Snippet Functionality › should handle code snippet copy functionality" classname="e2e/code-snippet.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Code Snippet Functionality › should display code snippet metadata correctly" classname="e2e/code-snippet.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Code Snippet Functionality › should handle questions without code snippets" classname="e2e/code-snippet.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/dashboard.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Dashboard › should display dashboard overview" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should navigate to different dashboard sections" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should display user profile information" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should show recent activity" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should display quiz statistics" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should handle responsive design" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should search functionality" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should handle notifications" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should display quick actions" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard › should handle dark mode toggle" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard - User Role › should hide admin features for regular users" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard - User Role › should show user-specific dashboard content" classname="e2e/dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/diagnostic-real-issues.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check quiz list page functionality" classname="e2e/diagnostic-real-issues.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check quiz edit route directly" classname="e2e/diagnostic-real-issues.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check question management UI" classname="e2e/diagnostic-real-issues.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Diagnostic Tests - Real Issues › DIAGNOSTIC: Check question addition workflow" classname="e2e/diagnostic-real-issues.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/error-handling.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Error Handling › should handle 404 errors gracefully" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle network errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle server errors (500)" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle authentication errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle session expiration" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle form validation errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle quiz loading errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle file upload errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle database connection errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle permission errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle timeout errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle JavaScript errors gracefully" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should provide error recovery options" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle malformed data gracefully" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should handle concurrent request errors" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling › should show user-friendly error messages" classname="e2e/error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/mobile.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="13" failures="0" skipped="13" time="0" errors="0">
<testcase name="Mobile Tests › should display mobile-friendly login" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should have mobile navigation" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should display quiz cards responsively" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle touch interactions" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should support swipe gestures" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should optimize for different mobile sizes" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile form inputs" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should support mobile accessibility" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile quiz taking" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile orientation changes" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should optimize mobile performance" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should handle mobile search" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Mobile Tests › should support mobile gestures in quiz" classname="e2e/mobile.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-creation.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Quiz Creation - All Possibilities › should create basic quiz with minimal required fields" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with all optional fields" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with different difficulty levels" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with different categories" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with time limits" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with different passing scores" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with multiple tags" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should handle special characters in quiz data" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Creation - All Possibilities › should create quiz with maximum field lengths" classname="e2e/quiz-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-editing.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Quiz Editing - All Possibilities › should edit basic quiz information" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should edit quiz settings" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should edit existing questions" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should delete questions from quiz" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should reorder questions in quiz" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editing - All Possibilities › should validate quiz before saving" classname="e2e/quiz-editing.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-editor.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="Quiz Editor › should create a new quiz" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editor › should add a multiple choice question" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editor › should validate required fields" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Editor › should handle different question types" classname="e2e/quiz-editor.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-preview.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="5" failures="0" skipped="5" time="0" errors="0">
<testcase name="Quiz Preview Functionality › debug - check quiz routes and 404 errors" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should test preview button functionality" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should exit preview mode" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should preview quiz with different settings" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Preview Functionality › should handle preview with no questions gracefully" classname="e2e/quiz-preview.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-search.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Quiz Search and Discovery › should search quizzes by title" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should filter quizzes by category" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should filter quizzes by difficulty" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should sort quizzes" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should show quiz details in search results" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should handle empty search results" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should clear search filters" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should paginate search results" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should show recent searches" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should suggest search terms" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should handle advanced search" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Search and Discovery › should save search preferences" classname="e2e/quiz-search.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e/quiz-taking.spec.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="13" failures="0" skipped="13" time="0" errors="0">
<testcase name="Quiz Taking Experience › should display available quizzes" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should start a quiz" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should answer multiple choice questions" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should answer true/false questions" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should show progress indicator" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should allow navigation between questions" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should show hints when available" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should submit quiz and show results" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should save quiz progress" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should handle quiz timer if present" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should show explanations after answering" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should handle different question types in same quiz" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Quiz Taking Experience › should validate required answers" classname="e2e/quiz-taking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="security/answer-exposure.test.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Answer Exposure Security Tests › should not expose correct answers in quiz API response" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should not expose answers in questions API endpoint" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should require authentication for quiz submission" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should validate answers server-side" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should prevent rapid quiz submissions" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should detect suspicious activity" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should handle invalid quiz submission data" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should not allow access to non-existent quiz" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="security/client-side-security.test.ts" timestamp="2025-06-01T23:21:05.791Z" hostname="Mobile Safari" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Client-Side Security Tests › should not expose answers in browser console" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose answers in page source" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose answers in network requests" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not allow answer manipulation via browser DevTools" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should handle quiz submission securely" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose database queries or internal errors" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should implement proper CSRF protection" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should sanitize user input in quiz content" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Client-Side Security Tests › should not expose sensitive configuration" classname="security/client-side-security.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>