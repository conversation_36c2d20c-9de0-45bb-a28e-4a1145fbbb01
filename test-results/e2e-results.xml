<testsuites id="" name="" tests="8" failures="1" skipped="7" errors="0" time="6.100605">
<testsuite name="security/answer-exposure.test.ts" timestamp="2025-06-01T15:12:28.613Z" hostname="chromium" tests="8" failures="1" skipped="7" time="0" errors="0">
<testcase name="Answer Exposure Security Tests › should not expose correct answers in quiz API response" classname="security/answer-exposure.test.ts" time="0">
<failure message="answer-exposure.test.ts:73:7 should not expose correct answers in quiz API response" type="FAILURE">
<![CDATA[  [chromium] › security/answer-exposure.test.ts:73:7 › Answer Exposure Security Tests › should not expose correct answers in quiz API response 

    PrismaClientValidationError: 
    Invalid `prisma.quiz.create()` invocation in
    /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/utils/test-data-seeder.ts:117:43

      114 
      115 const quiz = { ...defaultQuiz, ...quizData };
      116 
    → 117 const createdQuiz = await prisma.quiz.create({
            data: {
              title: "Security Test Quiz",
              description: "Quiz for testing answer exposure",
              content: {
                quiz_id: "security-test-quiz",
                title: {
                  en: "Security Test Quiz"
                },
                description: {
                  en: "Quiz for testing answer exposure"
                },
                version: "1.0.0",
                language: "en",
                questions: [
                  {
                    question_id: "mc_test",
                    type: "multiple_choice",
                    question_text: {
                      en: "What is 2+2?"
                    },
                    options: [
                      {
                        id: "a",
                        text: {
                          en: "3"
                        },
                        is_correct: false
                      },
                      {
                        id: "b",
                        text: {
                          en: "4"
                        },
                        is_correct: true
                      },
                      {
                        id: "c",
                        text: {
                          en: "5"
                        },
                        is_correct: false
                      }
                    ],
                    points: 1
                  },
                  {
                    question_id: "tf_test",
                    type: "true_false",
                    question_text: {
                      en: "The sky is blue."
                    },
                    correct_answer: true,
                    points: 1
                  },
                  {
                    question_id: "sa_test",
                    type: "short_answer",
                    question_text: {
                      en: "What is the capital of France?"
                    },
                    correct_answers: [
                      "Paris",
                      "paris"
                    ],
                    points: 1
                  }
                ],
                settings: {
                  time_limit: null,
                  shuffle_questions: false,
                  shuffle_options: false,
                  show_results: true,
                  allow_review: true
                }
              },
              authorId: "683c6dddb66be77564906ced",
              isPublished: true,
          +   quizId: String
            }
          })

    Argument `quizId` is missing.

       at utils/test-data-seeder.ts:117

      115 |     const quiz = { ...defaultQuiz, ...quizData };
      116 |
    > 117 |     const createdQuiz = await prisma.quiz.create({
          |                                           ^
      118 |       data: {
      119 |         title: quiz.title,
      120 |         description: quiz.description,
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/utils/test-data-seeder.ts:117:43
        at kn (/Users/<USER>/Documents/augment-projects/hacking-quiz/src/generated/prisma/runtime/library.js:32:1363)
        at Zn.handleRequestError (/Users/<USER>/Documents/augment-projects/hacking-quiz/src/generated/prisma/runtime/library.js:124:7102)
        at Zn.handleAndLogRequestError (/Users/<USER>/Documents/augment-projects/hacking-quiz/src/generated/prisma/runtime/library.js:124:6784)
        at Zn.request (/Users/<USER>/Documents/augment-projects/hacking-quiz/src/generated/prisma/runtime/library.js:124:6491)
        at l (/Users/<USER>/Documents/augment-projects/hacking-quiz/src/generated/prisma/runtime/library.js:133:9778)
        at TestDataSeeder.seedTestQuiz (/Users/<USER>/Documents/augment-projects/hacking-quiz/tests/utils/test-data-seeder.ts:117:25)
        at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/security/answer-exposure.test.ts:19:22

    Error Context: ../test-results/security-answer-exposure-A-bdca0-nswers-in-quiz-API-response-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|security-answer-exposure-A-bdca0-nswers-in-quiz-API-response-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Answer Exposure Security Tests › should not expose answers in questions API endpoint" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should require authentication for quiz submission" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should validate answers server-side" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should prevent rapid quiz submissions" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should detect suspicious activity" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should handle invalid quiz submission data" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Answer Exposure Security Tests › should not allow access to non-existent quiz" classname="security/answer-exposure.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>