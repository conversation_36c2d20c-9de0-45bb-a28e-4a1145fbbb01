# Test info

- Name: Answer Exposure Security Tests >> should not expose correct answers in quiz API response
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/security/answer-exposure.test.ts:73:7

# Error details

```
PrismaClientValidationError: 
Invalid `prisma.quiz.create()` invocation in
/Users/<USER>/Documents/augment-projects/hacking-quiz/tests/utils/test-data-seeder.ts:117:43

  114 
  115 const quiz = { ...defaultQuiz, ...quizData };
  116 
→ 117 const createdQuiz = await prisma.quiz.create({
        data: {
          quizId: "security-test-quiz",
          title: "Security Test Quiz",
          description: "Quiz for testing answer exposure",
          content: {
          ~~~~~~~
            quiz_id: "security-test-quiz",
            title: {
              en: "Security Test Quiz"
            },
            description: {
              en: "Quiz for testing answer exposure"
            },
            version: "1.0.0",
            language: "en",
            questions: [
              {
                question_id: "mc_test",
                type: "multiple_choice",
                question_text: {
                  en: "What is 2+2?"
                },
                options: [
                  {
                    id: "a",
                    text: {
                      en: "3"
                    },
                    is_correct: false
                  },
                  {
                    id: "b",
                    text: {
                      en: "4"
                    },
                    is_correct: true
                  },
                  {
                    id: "c",
                    text: {
                      en: "5"
                    },
                    is_correct: false
                  }
                ],
                points: 1
              },
              {
                question_id: "tf_test",
                type: "true_false",
                question_text: {
                  en: "The sky is blue."
                },
                correct_answer: true,
                points: 1
              },
              {
                question_id: "sa_test",
                type: "short_answer",
                question_text: {
                  en: "What is the capital of France?"
                },
                correct_answers: [
                  "Paris",
                  "paris"
                ],
                points: 1
              }
            ],
            settings: {
              time_limit: null,
              shuffle_questions: false,
              shuffle_options: false,
              show_results: true,
              allow_review: true
            }
          },
          authorId: "683c82f35f647aa393fe2ed9",
          isPublished: true,
      ?   id?: String,
      ?   author?: String | Null,
      ?   creationDate?: DateTime,
      ?   tags?: QuizCreatetagsInput | String[],
      ?   passingScore?: Float | Null,
      ?   timeLimit?: Int | Null,
      ?   markupFormat?: String,
      ?   locale?: String,
      ?   formatVersion?: String,
      ?   estimatedTime?: Int | Null,
      ?   prerequisites?: QuizCreateprerequisitesInput | String[],
      ?   learningPath?: String | Null,
      ?   version?: String,
      ?   isTemplate?: Boolean,
      ?   sourceType?: String,
      ?   metadata?: Json | Null,
      ?   showCorrectAnswer?: Boolean,
      ?   showUserAnswer?: Boolean,
      ?   showExplanationAfterAnswer?: Boolean,
      ?   highlightCorrectAnswer?: Boolean,
      ?   immediateAnswerFeedback?: Boolean,
      ?   createdAt?: DateTime,
      ?   updatedAt?: DateTime,
      ?   category?: CategoryCreateNestedOneWithoutQuizzesInput,
      ?   difficulty?: DifficultyLevelCreateNestedOneWithoutQuizzesInput,
      ?   questions?: QuestionCreateNestedManyWithoutQuizInput,
      ?   questionPools?: QuestionPoolCreateNestedManyWithoutQuizInput,
      ?   selectionRules?: SelectionRuleCreateNestedManyWithoutQuizInput,
      ?   responses?: UserResponseCreateNestedManyWithoutQuizInput,
      ?   quizSessions?: QuizSessionCreateNestedManyWithoutQuizInput,
      ?   creator?: UserCreateNestedOneWithoutCreatedQuizzesInput
        }
      })

Unknown argument `content`. Available options are marked with ?.
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/utils/test-data-seeder.ts:117:43
    at kn (/Users/<USER>/Documents/augment-projects/hacking-quiz/src/generated/prisma/runtime/library.js:32:1363)
    at Zn.handleRequestError (/Users/<USER>/Documents/augment-projects/hacking-quiz/src/generated/prisma/runtime/library.js:124:7102)
    at Zn.handleAndLogRequestError (/Users/<USER>/Documents/augment-projects/hacking-quiz/src/generated/prisma/runtime/library.js:124:6784)
    at Zn.request (/Users/<USER>/Documents/augment-projects/hacking-quiz/src/generated/prisma/runtime/library.js:124:6491)
    at l (/Users/<USER>/Documents/augment-projects/hacking-quiz/src/generated/prisma/runtime/library.js:133:9778)
    at TestDataSeeder.seedTestQuiz (/Users/<USER>/Documents/augment-projects/hacking-quiz/tests/utils/test-data-seeder.ts:117:25)
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/security/answer-exposure.test.ts:19:22
```

# Test source

```ts
   17 |   content: any;
   18 |   authorId: string;
   19 | }
   20 |
   21 | export class TestDataSeeder {
   22 |   private static instance: TestDataSeeder;
   23 |   private seededData: {
   24 |     users: TestUser[];
   25 |     quizzes: TestQuiz[];
   26 |   } = {
   27 |     users: [],
   28 |     quizzes: []
   29 |   };
   30 |
   31 |   static getInstance(): TestDataSeeder {
   32 |     if (!TestDataSeeder.instance) {
   33 |       TestDataSeeder.instance = new TestDataSeeder();
   34 |     }
   35 |     return TestDataSeeder.instance;
   36 |   }
   37 |
   38 |   async seedTestUser(userData: Partial<TestUser> = {}): Promise<TestUser> {
   39 |     const defaultUser = {
   40 |       email: `test-${Date.now()}@example.com`,
   41 |       password: 'testpassword123',
   42 |       role: 'USER' as const,
   43 |       name: 'Test User'
   44 |     };
   45 |
   46 |     const user = { ...defaultUser, ...userData };
   47 |     const hashedPassword = await bcrypt.hash(user.password, 12);
   48 |
   49 |     const createdUser = await prisma.user.create({
   50 |       data: {
   51 |         email: user.email,
   52 |         password: hashedPassword,
   53 |         role: user.role,
   54 |         name: user.name
   55 |       }
   56 |     });
   57 |
   58 |     const testUser: TestUser = {
   59 |       id: createdUser.id,
   60 |       email: user.email,
   61 |       password: user.password, // Store original password for testing
   62 |       role: user.role
   63 |     };
   64 |
   65 |     this.seededData.users.push(testUser);
   66 |     return testUser;
   67 |   }
   68 |
   69 |   async seedTestAdmin(): Promise<TestUser> {
   70 |     return this.seedTestUser({
   71 |       email: `admin-${Date.now()}@example.com`,
   72 |       role: 'ADMIN'
   73 |     });
   74 |   }
   75 |
   76 |   async seedTestQuiz(quizData: Partial<TestQuiz> = {}, authorId?: string): Promise<TestQuiz> {
   77 |     if (!authorId) {
   78 |       const admin = await this.seedTestAdmin();
   79 |       authorId = admin.id;
   80 |     }
   81 |
   82 |     const defaultQuiz = {
   83 |       title: `Test Quiz ${Date.now()}`,
   84 |       description: 'A test quiz for E2E testing',
   85 |       content: {
   86 |         quiz_id: `test-quiz-${Date.now()}`,
   87 |         title: { en: `Test Quiz ${Date.now()}` },
   88 |         description: { en: 'A test quiz for E2E testing' },
   89 |         version: '1.0.0',
   90 |         language: 'en',
   91 |         questions: [
   92 |           {
   93 |             question_id: 'q1',
   94 |             type: 'multiple_choice',
   95 |             question_text: { en: 'What is 2 + 2?' },
   96 |             options: [
   97 |               { id: 'a', text: { en: '3' }, is_correct: false },
   98 |               { id: 'b', text: { en: '4' }, is_correct: true },
   99 |               { id: 'c', text: { en: '5' }, is_correct: false }
  100 |             ],
  101 |             points: 1,
  102 |             explanation: { en: '2 + 2 equals 4' }
  103 |           }
  104 |         ],
  105 |         settings: {
  106 |           time_limit: null,
  107 |           shuffle_questions: false,
  108 |           shuffle_options: false,
  109 |           show_results: true,
  110 |           allow_review: true
  111 |         }
  112 |       }
  113 |     };
  114 |
  115 |     const quiz = { ...defaultQuiz, ...quizData };
  116 |
> 117 |     const createdQuiz = await prisma.quiz.create({
      |                                           ^ PrismaClientValidationError: 
  118 |       data: {
  119 |         quizId: quiz.content.quiz_id || `test-quiz-${Date.now()}`,
  120 |         title: quiz.title,
  121 |         description: quiz.description,
  122 |         content: quiz.content,
  123 |         authorId: authorId,
  124 |         isPublished: true
  125 |       }
  126 |     });
  127 |
  128 |     const testQuiz: TestQuiz = {
  129 |       id: createdQuiz.id,
  130 |       title: quiz.title,
  131 |       description: quiz.description,
  132 |       content: quiz.content,
  133 |       authorId: authorId
  134 |     };
  135 |
  136 |     this.seededData.quizzes.push(testQuiz);
  137 |     return testQuiz;
  138 |   }
  139 |
  140 |   async seedMultipleQuizzes(count: number = 3, authorId?: string): Promise<TestQuiz[]> {
  141 |     const quizzes: TestQuiz[] = [];
  142 |
  143 |     for (let i = 0; i < count; i++) {
  144 |       const quiz = await this.seedTestQuiz({
  145 |         title: `Test Quiz ${i + 1} - ${Date.now()}`,
  146 |         description: `Test quiz number ${i + 1} for E2E testing`
  147 |       }, authorId);
  148 |       quizzes.push(quiz);
  149 |     }
  150 |
  151 |     return quizzes;
  152 |   }
  153 |
  154 |   async cleanupSeededData(): Promise<void> {
  155 |     // Delete quizzes first (due to foreign key constraints)
  156 |     for (const quiz of this.seededData.quizzes) {
  157 |       await prisma.quiz.delete({
  158 |         where: { id: quiz.id }
  159 |       }).catch(() => {
  160 |         // Ignore errors if already deleted
  161 |       });
  162 |     }
  163 |
  164 |     // Delete users
  165 |     for (const user of this.seededData.users) {
  166 |       await prisma.user.delete({
  167 |         where: { id: user.id }
  168 |       }).catch(() => {
  169 |         // Ignore errors if already deleted
  170 |       });
  171 |     }
  172 |
  173 |     // Reset seeded data
  174 |     this.seededData = { users: [], quizzes: [] };
  175 |   }
  176 |
  177 |   getSeededUsers(): TestUser[] {
  178 |     return [...this.seededData.users];
  179 |   }
  180 |
  181 |   getSeededQuizzes(): TestQuiz[] {
  182 |     return [...this.seededData.quizzes];
  183 |   }
  184 |
  185 |   async disconnect(): Promise<void> {
  186 |     await prisma.$disconnect();
  187 |   }
  188 | }
  189 |
  190 | // Helper functions for common test scenarios
  191 | export async function createTestUserAndLogin() {
  192 |   const seeder = TestDataSeeder.getInstance();
  193 |   return await seeder.seedTestUser();
  194 | }
  195 |
  196 | export async function createTestAdminAndLogin() {
  197 |   const seeder = TestDataSeeder.getInstance();
  198 |   return await seeder.seedTestAdmin();
  199 | }
  200 |
  201 | export async function createTestQuizWithAdmin() {
  202 |   const seeder = TestDataSeeder.getInstance();
  203 |   const admin = await seeder.seedTestAdmin();
  204 |   const quiz = await seeder.seedTestQuiz({}, admin.id);
  205 |   return { admin, quiz };
  206 | }
  207 |
  208 | export async function cleanupAllTestData() {
  209 |   const seeder = TestDataSeeder.getInstance();
  210 |   await seeder.cleanupSeededData();
  211 | }
  212 |
```