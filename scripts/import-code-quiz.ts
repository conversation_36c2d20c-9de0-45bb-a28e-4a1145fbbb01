import { PrismaClient } from '@/generated/prisma';
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

async function importCodeSecurityQuiz() {
  try {
    console.log('🚀 Starting import of Code Security Quiz...');

    // Read the quiz file
    const quizPath = path.join(process.cwd(), 'src/data/code-security-quiz.json');
    const quizData = JSON.parse(fs.readFileSync(quizPath, 'utf8'));
    const quiz = quizData.quiz;

    // Check if quiz already exists
    const existingQuiz = await prisma.quiz.findUnique({
      where: { quizId: quiz.metadata.quiz_id }
    });

    if (existingQuiz) {
      console.log('⚠️  Quiz already exists, updating...');
      await prisma.quiz.delete({
        where: { quizId: quiz.metadata.quiz_id }
      });
    }

    // Create the quiz
    const createdQuiz = await prisma.quiz.create({
      data: {
        quizId: quiz.metadata.quiz_id,
        title: quiz.metadata.title,
        description: quiz.metadata.description,
        author: quiz.metadata.author,
        tags: quiz.metadata.tags,
        passingScore: quiz.metadata.passing_score_percentage,
        timeLimit: quiz.metadata.time_limit_minutes,
        markupFormat: quiz.metadata.markup_format,
        locale: quiz.metadata.locale,
        formatVersion: quiz.metadata.format_version,
        isPublished: true,
      }
    });

    console.log(`✅ Created quiz: ${createdQuiz.title}`);

    // Create questions
    for (const question of quiz.questions) {
      const questionData: any = {
        questionId: question.question_id,
        type: question.type,
        text: question.text,
        points: question.points,
        feedbackCorrect: question.feedback_correct,
        feedbackIncorrect: question.feedback_incorrect,
        explanation: question.explanation,
        media: question.media,
        codeSnippet: question.code_snippet, // Add code snippet support
        hint: question.hint,
        dependsOn: question.depends_on,
        quizId: createdQuiz.id,
      };

      // Add type-specific fields
      switch (question.type) {
        case 'multiple_choice':
          questionData.options = question.options;
          break;
        case 'true_false':
          questionData.correctAnswer = question.correct_answer;
          break;
        case 'short_answer':
          questionData.correctAnswers = question.correct_answers;
          questionData.caseSensitive = question.case_sensitive;
          questionData.trimWhitespace = question.trim_whitespace;
          questionData.exactMatch = question.exact_match;
          break;
        case 'matching':
          questionData.stems = question.stems;
          questionData.options = question.options;
          questionData.correctPairs = question.correct_pairs;
          break;
        case 'fill_in_the_blank':
          questionData.textTemplate = question.text_template;
          questionData.blanks = question.blanks;
          break;
        case 'essay':
          questionData.minWordCount = question.min_word_count;
          questionData.maxWordCount = question.max_word_count;
          questionData.guidelines = question.guidelines;
          break;
      }

      await prisma.question.create({
        data: questionData
      });

      console.log(`  ✅ Created question: ${question.question_id}`);
    }

    console.log('🎉 Code Security Quiz imported successfully!');
    console.log(`📊 Quiz ID: ${quiz.metadata.quiz_id}`);
    console.log(`📝 Questions: ${quiz.questions.length}`);
    console.log(`🔗 URL: http://localhost:3001/quiz/${createdQuiz.id}`);

  } catch (error) {
    console.error('❌ Error importing quiz:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the import
importCodeSecurityQuiz();
