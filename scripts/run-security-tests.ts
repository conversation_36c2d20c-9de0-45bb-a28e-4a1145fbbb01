#!/usr/bin/env tsx

/**
 * Security Test Runner
 *
 * This script runs comprehensive security tests to verify that
 * QuizFlow properly protects quiz answers and implements security measures.
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

interface TestResult {
  name: string;
  passed: boolean;
  duration: number;
  errors: string[];
}

interface SecurityTestReport {
  timestamp: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  duration: number;
  results: TestResult[];
  securityIssues: string[];
  recommendations: string[];
}

async function runSecurityTests(): Promise<SecurityTestReport> {
  console.log('🔒 Starting QuizFlow Security Test Suite...\n');

  const startTime = Date.now();
  const results: TestResult[] = [];
  const securityIssues: string[] = [];
  const recommendations: string[] = [];

  // Test categories to run
  const testCategories = [
    {
      name: 'Answer Exposure Prevention',
      command: 'npx playwright test tests/security/answer-exposure.test.ts --reporter=json',
      critical: true
    },
    {
      name: 'Client-Side Security',
      command: 'npx playwright test tests/security/client-side-security.test.ts --reporter=json',
      critical: true
    },
    {
      name: 'API Security',
      command: 'npx playwright test tests/e2e/auth-simple.spec.ts --reporter=json',
      critical: false
    }
  ];

  for (const category of testCategories) {
    console.log(`🧪 Running ${category.name} tests...`);

    const testStart = Date.now();
    try {
      const output = execSync(category.command, {
        encoding: 'utf8',
        timeout: 120000 // 2 minutes timeout
      });

      const testDuration = Date.now() - testStart;

      // Parse Playwright JSON output
      let testResult: TestResult;
      try {
        const jsonOutput = JSON.parse(output);
        const passed = jsonOutput.stats?.failed === 0;

        testResult = {
          name: category.name,
          passed,
          duration: testDuration,
          errors: passed ? [] : ['Some tests failed - check detailed output']
        };

        if (!passed && category.critical) {
          securityIssues.push(`CRITICAL: ${category.name} tests failed`);
        }

      } catch (parseError) {
        // Fallback if JSON parsing fails
        testResult = {
          name: category.name,
          passed: !output.includes('failed'),
          duration: testDuration,
          errors: output.includes('failed') ? ['Test execution failed'] : []
        };
      }

      results.push(testResult);

      if (testResult.passed) {
        console.log(`✅ ${category.name}: PASSED (${testDuration}ms)\n`);
      } else {
        console.log(`❌ ${category.name}: FAILED (${testDuration}ms)\n`);
      }

    } catch (error) {
      const testDuration = Date.now() - testStart;
      console.log(`💥 ${category.name}: ERROR (${testDuration}ms)`);
      console.log(`Error: ${error}\n`);

      results.push({
        name: category.name,
        passed: false,
        duration: testDuration,
        errors: [String(error)]
      });

      if (category.critical) {
        securityIssues.push(`CRITICAL: ${category.name} tests could not run`);
      }
    }
  }

  // Run manual security checks
  console.log('🔍 Running manual security checks...');

  // Check for answer exposure in API endpoints
  try {
    console.log('  - Checking API endpoints for answer exposure...');
    const apiCheckResult = await checkApiSecurity();
    if (!apiCheckResult.secure) {
      securityIssues.push('API endpoints expose quiz answers');
      recommendations.push('Implement answer sanitization in API responses');
    }
  } catch (error) {
    console.log(`    Warning: Could not check API security: ${error}`);
  }

  // Check for client-side answer exposure
  console.log('  - Checking for client-side answer exposure...');
  const clientCheckResult = checkClientSideSecurity();
  if (!clientCheckResult.secure) {
    securityIssues.push('Client-side code may expose answers');
    recommendations.push('Remove answer validation from client-side code');
  }

  // Check for proper server-side validation
  console.log('  - Checking server-side validation...');
  const serverCheckResult = checkServerSideValidation();
  if (!serverCheckResult.secure) {
    securityIssues.push('Missing server-side answer validation');
    recommendations.push('Implement secure quiz submission endpoint');
  }

  const totalDuration = Date.now() - startTime;
  const passedTests = results.filter(r => r.passed).length;
  const failedTests = results.length - passedTests;

  const report: SecurityTestReport = {
    timestamp: new Date().toISOString(),
    totalTests: results.length,
    passedTests,
    failedTests,
    duration: totalDuration,
    results,
    securityIssues,
    recommendations
  };

  // Generate report
  generateSecurityReport(report);

  return report;
}

async function checkApiSecurity(): Promise<{ secure: boolean; issues: string[] }> {
  const issues: string[] = [];

  // Check if quiz API files contain proper sanitization
  const quizApiPath = path.join(process.cwd(), 'src/app/api/quizzes/[id]/route.ts');

  if (fs.existsSync(quizApiPath)) {
    const content = fs.readFileSync(quizApiPath, 'utf8');

    // Check for sanitization function
    if (!content.includes('sanitizeQuestionForClient')) {
      issues.push('Missing answer sanitization in quiz API');
    }

    // Check for proper field exclusion
    if (!content.includes('correctAnswer: false') && !content.includes('options: false')) {
      issues.push('API may still expose answer fields');
    }
  } else {
    issues.push('Quiz API file not found');
  }

  return {
    secure: issues.length === 0,
    issues
  };
}

function checkClientSideSecurity(): { secure: boolean; issues: string[] } {
  const issues: string[] = [];

  // Check QuizRenderer for client-side scoring
  const quizRendererPath = path.join(process.cwd(), 'src/components/quiz/QuizRenderer.tsx');

  if (fs.existsSync(quizRendererPath)) {
    const content = fs.readFileSync(quizRendererPath, 'utf8');

    // Check if it still uses client-side scoring as primary method
    if (content.includes('checkAnswerCorrectness') && !content.includes('/submit')) {
      issues.push('Client-side scoring still active without server validation');
    }

    // Check for answer exposure in getCorrectAnswerForDisplay
    if (content.includes('getCorrectAnswerForDisplay') && !content.includes('SECURITY:')) {
      issues.push('Answer display function may expose correct answers');
    }
  }

  return {
    secure: issues.length === 0,
    issues
  };
}

function checkServerSideValidation(): { secure: boolean; issues: string[] } {
  const issues: string[] = [];

  // Check if submit endpoint exists
  const submitApiPath = path.join(process.cwd(), 'src/app/api/quizzes/[id]/submit/route.ts');

  if (!fs.existsSync(submitApiPath)) {
    issues.push('Server-side quiz submission endpoint missing');
  } else {
    const content = fs.readFileSync(submitApiPath, 'utf8');

    // Check for proper validation
    if (!content.includes('validateAndScoreAnswers')) {
      issues.push('Missing server-side answer validation');
    }

    // Check for rate limiting
    if (!content.includes('rate limit') && !content.includes('recentAttempts')) {
      issues.push('Missing rate limiting protection');
    }
  }

  return {
    secure: issues.length === 0,
    issues
  };
}

function generateSecurityReport(report: SecurityTestReport): void {
  console.log('\n' + '='.repeat(60));
  console.log('🔒 QUIZFLOW SECURITY TEST REPORT');
  console.log('='.repeat(60));

  console.log(`📅 Timestamp: ${report.timestamp}`);
  console.log(`⏱️  Duration: ${report.duration}ms`);
  console.log(`📊 Tests: ${report.totalTests} total, ${report.passedTests} passed, ${report.failedTests} failed\n`);

  // Test Results
  console.log('📋 TEST RESULTS:');
  report.results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`  ${status} ${result.name} (${result.duration}ms)`);
    if (result.errors.length > 0) {
      result.errors.forEach(error => {
        console.log(`    ⚠️  ${error}`);
      });
    }
  });

  // Security Issues
  if (report.securityIssues.length > 0) {
    console.log('\n🚨 SECURITY ISSUES FOUND:');
    report.securityIssues.forEach(issue => {
      console.log(`  ❌ ${issue}`);
    });
  } else {
    console.log('\n✅ NO CRITICAL SECURITY ISSUES FOUND');
  }

  // Recommendations
  if (report.recommendations.length > 0) {
    console.log('\n💡 RECOMMENDATIONS:');
    report.recommendations.forEach(rec => {
      console.log(`  🔧 ${rec}`);
    });
  }

  // Overall Security Status
  const criticalIssues = report.securityIssues.filter(issue => issue.includes('CRITICAL')).length;
  const overallSecure = report.failedTests === 0 && criticalIssues === 0;

  console.log('\n' + '='.repeat(60));
  if (overallSecure) {
    console.log('🛡️  OVERALL SECURITY STATUS: SECURE ✅');
  } else {
    console.log('⚠️  OVERALL SECURITY STATUS: NEEDS ATTENTION ❌');
    console.log(`   Critical Issues: ${criticalIssues}`);
    console.log(`   Failed Tests: ${report.failedTests}`);
  }
  console.log('='.repeat(60));

  // Save report to file
  const reportPath = path.join(process.cwd(), 'security-test-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`\n📄 Detailed report saved to: ${reportPath}`);
}

// Run the security tests
if (require.main === module) {
  runSecurityTests()
    .then(report => {
      const exitCode = report.failedTests > 0 || report.securityIssues.length > 0 ? 1 : 0;
      process.exit(exitCode);
    })
    .catch(error => {
      console.error('💥 Security test runner failed:', error);
      process.exit(1);
    });
}

export { runSecurityTests };
export type { SecurityTestReport };
