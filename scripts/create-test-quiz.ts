import { PrismaClient } from '../src/generated/prisma';

const prisma = new PrismaClient();

async function createTestQuiz() {
  try {
    console.log('Starting test quiz creation...');

    // Find admin user
    const adminUser = await prisma.user.findFirst({
      where: { role: 'admin' }
    });

    if (!adminUser) {
      console.error('No admin user found. Please create an admin user first.');
      return;
    }

    console.log('Found admin user:', adminUser.email);

    // Create a test quiz
    const quiz = await prisma.quiz.create({
      data: {
        quizId: 'show-answer-test-quiz',
        title: 'Show Answer Test Quiz',
        description: 'A quiz to test the Show Answer functionality with multiple choice questions',
        timeLimit: 30,
        passingScore: 70,
        tags: ['testing', 'show-answer', 'multiple-choice'],
        creatorId: adminUser.id,
        isPublished: true,
      }
    });

    console.log('Created quiz:', quiz.title, 'with ID:', quiz.id);

    // Create multiple choice question with single correct answer
    const question1 = await prisma.question.create({
      data: {
        questionId: 'test_q1',
        type: 'multiple_choice',
        text: JSON.stringify({
          default: 'What is the most common type of cyber attack?'
        }),
        points: 2,
        options: JSON.stringify([
          {
            id: 'q1_opt1',
            text: 'Phishing',
            is_correct: true
          },
          {
            id: 'q1_opt2',
            text: 'DDoS',
            is_correct: false
          },
          {
            id: 'q1_opt3',
            text: 'Malware',
            is_correct: false
          },
          {
            id: 'q1_opt4',
            text: 'SQL Injection',
            is_correct: false
          }
        ]),
        correctAnswer: JSON.stringify(['q1_opt1']),
        quizId: quiz.id,
      }
    });

    // Create multiple choice question with multiple correct answers
    const question2 = await prisma.question.create({
      data: {
        questionId: 'test_q2',
        type: 'multiple_choice',
        text: JSON.stringify({
          default: 'Which of the following are considered secure password practices? (Select all that apply)'
        }),
        points: 3,
        options: JSON.stringify([
          {
            id: 'q2_opt1',
            text: 'Using a mix of uppercase and lowercase letters',
            is_correct: true
          },
          {
            id: 'q2_opt2',
            text: 'Including numbers and special characters',
            is_correct: true
          },
          {
            id: 'q2_opt3',
            text: 'Using the same password for all accounts',
            is_correct: false
          },
          {
            id: 'q2_opt4',
            text: 'Making passwords at least 12 characters long',
            is_correct: true
          },
          {
            id: 'q2_opt5',
            text: 'Using personal information like birthdate',
            is_correct: false
          }
        ]),
        correctAnswer: JSON.stringify(['q2_opt1', 'q2_opt2', 'q2_opt4']),
        quizId: quiz.id,
      }
    });

    // Create a true/false question
    const question3 = await prisma.question.create({
      data: {
        questionId: 'test_q3',
        type: 'true_false',
        text: JSON.stringify({
          default: 'HTTPS encrypts all data transmitted between a web browser and server.'
        }),
        points: 1,
        correctAnswer: JSON.stringify(true),
        quizId: quiz.id,
      }
    });

    // Create a short answer question
    const question4 = await prisma.question.create({
      data: {
        questionId: 'test_q4',
        type: 'short_answer',
        text: JSON.stringify({
          default: 'What does "CIA" stand for in cybersecurity? (Enter the three words separated by commas)'
        }),
        points: 2,
        correctAnswer: JSON.stringify(['Confidentiality, Integrity, Availability']),
        quizId: quiz.id,
      }
    });

    console.log('Created questions:');
    console.log('- Question 1 (Single choice):', question1.questionId);
    console.log('- Question 2 (Multiple choice):', question2.questionId);
    console.log('- Question 3 (True/False):', question3.questionId);
    console.log('- Question 4 (Short Answer):', question4.questionId);

    console.log('\nTest quiz created successfully!');
    console.log('Quiz URL:', `http://localhost:3002/quiz/${quiz.id}`);
    console.log('Preview URL:', `http://localhost:3002/dashboard/quizzes/${quiz.id}/preview`);

  } catch (error) {
    console.error('Error creating test quiz:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestQuiz();
