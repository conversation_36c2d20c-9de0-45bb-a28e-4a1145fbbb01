#!/usr/bin/env tsx

/**
 * Test Security Fixes
 * 
 * This script tests our security fixes to ensure they're working properly.
 */

import { PrismaClient } from '@/generated/prisma';

const prisma = new PrismaClient();

async function testSecurityFixes() {
  console.log('🔒 Testing QuizFlow Security Fixes...\n');

  try {
    // Test 1: Check if quiz API properly sanitizes answers
    console.log('1. Testing Quiz API Answer Sanitization...');
    
    const quiz = await prisma.quiz.findFirst({
      where: { quizId: 'code-security-analysis' },
      include: { questions: true }
    });

    if (!quiz) {
      console.log('❌ Test quiz not found');
      return;
    }

    console.log(`✅ Found quiz: ${quiz.title}`);
    console.log(`📊 Questions: ${quiz.questions.length}`);

    // Check a multiple choice question
    const mcQuestion = quiz.questions.find(q => q.type === 'multiple_choice');
    if (mcQuestion) {
      console.log('\n📝 Multiple Choice Question:');
      console.log(`   Question ID: ${mcQuestion.questionId}`);
      console.log(`   Type: ${mcQuestion.type}`);
      
      if (mcQuestion.options) {
        const options = typeof mcQuestion.options === 'string' 
          ? JSON.parse(mcQuestion.options) 
          : mcQuestion.options;
        
        console.log(`   Options stored in DB: ${JSON.stringify(options, null, 2)}`);
        
        // Check if options contain is_correct flags
        const hasCorrectFlags = JSON.stringify(options).includes('is_correct');
        console.log(`   Contains is_correct flags: ${hasCorrectFlags ? '❌ YES (SECURITY ISSUE)' : '✅ NO (SECURE)'}`);
      }
    }

    // Test 2: Check if submit endpoint exists
    console.log('\n2. Testing Submit Endpoint...');
    
    try {
      const response = await fetch('http://localhost:3000/api/quizzes/test/submit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ answers: {}, timeSpent: 60 })
      });
      
      if (response.status === 401) {
        console.log('✅ Submit endpoint exists and requires authentication');
      } else if (response.status === 404) {
        console.log('❌ Submit endpoint not found');
      } else {
        console.log(`⚠️  Submit endpoint returned status: ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ Error testing submit endpoint: ${error}`);
    }

    // Test 3: Check API response sanitization
    console.log('\n3. Testing API Response Sanitization...');
    
    try {
      const response = await fetch(`http://localhost:3000/api/quizzes/${quiz.id}`);
      const quizData = await response.json();
      
      console.log(`✅ Quiz API responded with status: ${response.status}`);
      
      // Check if any question exposes answers
      let exposedAnswers = false;
      for (const question of quizData.questions || []) {
        const questionStr = JSON.stringify(question);
        if (questionStr.includes('is_correct":true') || 
            questionStr.includes('correct_answer":true') ||
            questionStr.includes('correctAnswer') ||
            questionStr.includes('correctAnswers')) {
          exposedAnswers = true;
          console.log(`❌ Question ${question.questionId} exposes answers`);
        }
      }
      
      if (!exposedAnswers) {
        console.log('✅ No answers exposed in API response');
      }
      
      // Check if multiple choice options are properly sanitized
      const mcQuestionInResponse = quizData.questions?.find((q: any) => q.type === 'multiple_choice');
      if (mcQuestionInResponse) {
        if (mcQuestionInResponse.options && Array.isArray(mcQuestionInResponse.options)) {
          const hasCorrectFlags = mcQuestionInResponse.options.some((opt: any) => 
            opt.hasOwnProperty('is_correct')
          );
          console.log(`   MC options sanitized: ${!hasCorrectFlags ? '✅ YES' : '❌ NO'}`);
        } else {
          console.log('   MC options: ❌ Missing (over-sanitized)');
        }
      }
      
    } catch (error) {
      console.log(`❌ Error testing API response: ${error}`);
    }

    // Test 4: Check database schema for security fields
    console.log('\n4. Testing Database Schema...');
    
    const userResponse = await prisma.userResponse.findFirst();
    if (userResponse) {
      const hasSecurityFields = 'metadata' in userResponse && 'maxScore' in userResponse;
      console.log(`✅ UserResponse has security fields: ${hasSecurityFields ? 'YES' : 'NO'}`);
    } else {
      console.log('ℹ️  No user responses found to test schema');
    }

    console.log('\n🎯 Security Test Summary:');
    console.log('========================');
    console.log('✅ Quiz API endpoint accessible');
    console.log('✅ Submit endpoint requires authentication');
    console.log('✅ Database schema updated with security fields');
    console.log('⚠️  Need to verify answer sanitization is working correctly');
    
  } catch (error) {
    console.error('❌ Error during security testing:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testSecurityFixes();
