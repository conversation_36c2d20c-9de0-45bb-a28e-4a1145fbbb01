#!/usr/bin/env tsx

/**
 * Final Security Verification
 * 
 * This script performs a comprehensive security verification to ensure
 * all critical security fixes are properly implemented.
 */

async function runSecurityVerification() {
  console.log('🔒 QUIZFLOW FINAL SECURITY VERIFICATION');
  console.log('=====================================\n');

  const results = {
    passed: 0,
    failed: 0,
    warnings: 0,
    tests: [] as Array<{name: string, status: 'PASS' | 'FAIL' | 'WARN', details: string}>
  };

  // Test 1: API Answer Exposure Prevention
  console.log('1. Testing API Answer Exposure Prevention...');
  try {
    const response = await fetch('http://localhost:3000/api/quizzes/683660ad0c42de016c5eb070');
    const quizData = await response.json();
    
    let answerExposure = false;
    let optionsAvailable = false;
    
    for (const question of quizData.questions || []) {
      const questionStr = JSON.stringify(question);
      
      // Check for answer exposure
      if (questionStr.includes('is_correct":true') || 
          questionStr.includes('correct_answer":true') ||
          questionStr.includes('"correctAnswer":') ||
          questionStr.includes('"correctAnswers":')) {
        answerExposure = true;
      }
      
      // Check if options are available for MC questions
      if (question.type === 'multiple_choice' && question.options && Array.isArray(question.options)) {
        optionsAvailable = true;
      }
    }
    
    if (answerExposure) {
      results.failed++;
      results.tests.push({
        name: 'API Answer Exposure',
        status: 'FAIL',
        details: 'Quiz API still exposes correct answers'
      });
      console.log('❌ FAIL: Quiz API exposes correct answers');
    } else {
      results.passed++;
      results.tests.push({
        name: 'API Answer Exposure',
        status: 'PASS',
        details: 'No answers exposed in API response'
      });
      console.log('✅ PASS: No answers exposed in API response');
    }
    
    if (optionsAvailable) {
      console.log('✅ PASS: Multiple choice options are available');
    } else {
      results.warnings++;
      results.tests.push({
        name: 'MC Options Availability',
        status: 'WARN',
        details: 'Multiple choice options may be over-sanitized'
      });
      console.log('⚠️  WARN: Multiple choice options may be missing');
    }
    
  } catch (error) {
    results.failed++;
    results.tests.push({
      name: 'API Answer Exposure',
      status: 'FAIL',
      details: `Error testing API: ${error}`
    });
    console.log(`❌ FAIL: Error testing API - ${error}`);
  }

  // Test 2: Server-Side Submission Endpoint
  console.log('\n2. Testing Server-Side Submission Endpoint...');
  try {
    const response = await fetch('http://localhost:3000/api/quizzes/test/submit', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ answers: {}, timeSpent: 60 })
    });
    
    if (response.status === 401) {
      results.passed++;
      results.tests.push({
        name: 'Submit Endpoint Auth',
        status: 'PASS',
        details: 'Submit endpoint properly requires authentication'
      });
      console.log('✅ PASS: Submit endpoint requires authentication');
    } else if (response.status === 404) {
      results.failed++;
      results.tests.push({
        name: 'Submit Endpoint Auth',
        status: 'FAIL',
        details: 'Submit endpoint not found'
      });
      console.log('❌ FAIL: Submit endpoint not found');
    } else {
      results.warnings++;
      results.tests.push({
        name: 'Submit Endpoint Auth',
        status: 'WARN',
        details: `Unexpected status code: ${response.status}`
      });
      console.log(`⚠️  WARN: Unexpected status code: ${response.status}`);
    }
  } catch (error) {
    results.failed++;
    results.tests.push({
      name: 'Submit Endpoint Auth',
      status: 'FAIL',
      details: `Error testing submit endpoint: ${error}`
    });
    console.log(`❌ FAIL: Error testing submit endpoint - ${error}`);
  }

  // Test 3: Code Snippet Security
  console.log('\n3. Testing Code Snippet Security...');
  try {
    const response = await fetch('http://localhost:3000/api/quizzes/683660ad0c42de016c5eb070');
    const quizData = await response.json();
    
    let codeSnippetFound = false;
    let codeSnippetSecure = true;
    
    for (const question of quizData.questions || []) {
      if (question.codeSnippet) {
        codeSnippetFound = true;
        
        // Check if code snippet contains any dangerous content
        const codeStr = JSON.stringify(question.codeSnippet);
        if (codeStr.includes('<script>') || codeStr.includes('javascript:') || codeStr.includes('eval(')) {
          codeSnippetSecure = false;
        }
      }
    }
    
    if (codeSnippetFound) {
      if (codeSnippetSecure) {
        results.passed++;
        results.tests.push({
          name: 'Code Snippet Security',
          status: 'PASS',
          details: 'Code snippets are properly sanitized'
        });
        console.log('✅ PASS: Code snippets found and appear secure');
      } else {
        results.failed++;
        results.tests.push({
          name: 'Code Snippet Security',
          status: 'FAIL',
          details: 'Code snippets contain potentially dangerous content'
        });
        console.log('❌ FAIL: Code snippets contain dangerous content');
      }
    } else {
      results.warnings++;
      results.tests.push({
        name: 'Code Snippet Security',
        status: 'WARN',
        details: 'No code snippets found to test'
      });
      console.log('⚠️  WARN: No code snippets found to test');
    }
  } catch (error) {
    results.failed++;
    results.tests.push({
      name: 'Code Snippet Security',
      status: 'FAIL',
      details: `Error testing code snippets: ${error}`
    });
    console.log(`❌ FAIL: Error testing code snippets - ${error}`);
  }

  // Test 4: Client-Side Security Headers
  console.log('\n4. Testing Security Headers...');
  try {
    const response = await fetch('http://localhost:3000/api/quizzes/683660ad0c42de016c5eb070');
    const headers = response.headers;
    
    // Check for basic security headers
    const hasContentType = headers.get('content-type')?.includes('application/json');
    
    if (hasContentType) {
      results.passed++;
      results.tests.push({
        name: 'Security Headers',
        status: 'PASS',
        details: 'Basic security headers present'
      });
      console.log('✅ PASS: Basic security headers present');
    } else {
      results.warnings++;
      results.tests.push({
        name: 'Security Headers',
        status: 'WARN',
        details: 'Some security headers missing'
      });
      console.log('⚠️  WARN: Some security headers missing');
    }
  } catch (error) {
    results.failed++;
    results.tests.push({
      name: 'Security Headers',
      status: 'FAIL',
      details: `Error testing headers: ${error}`
    });
    console.log(`❌ FAIL: Error testing headers - ${error}`);
  }

  // Generate Final Report
  console.log('\n' + '='.repeat(50));
  console.log('🔒 FINAL SECURITY VERIFICATION REPORT');
  console.log('='.repeat(50));
  
  console.log(`📊 Test Results:`);
  console.log(`   ✅ Passed: ${results.passed}`);
  console.log(`   ❌ Failed: ${results.failed}`);
  console.log(`   ⚠️  Warnings: ${results.warnings}`);
  console.log(`   📝 Total: ${results.tests.length}`);
  
  console.log('\n📋 Detailed Results:');
  results.tests.forEach(test => {
    const icon = test.status === 'PASS' ? '✅' : test.status === 'FAIL' ? '❌' : '⚠️';
    console.log(`   ${icon} ${test.name}: ${test.details}`);
  });
  
  // Overall Security Status
  const criticalFailures = results.failed;
  const overallSecure = criticalFailures === 0;
  
  console.log('\n' + '='.repeat(50));
  if (overallSecure) {
    console.log('🛡️  OVERALL SECURITY STATUS: ✅ SECURE');
    console.log('🎉 QuizFlow security fixes are working correctly!');
    console.log('🔒 Quiz answers are properly protected from client exposure.');
    console.log('🚀 Ready for production deployment with enhanced security.');
  } else {
    console.log('⚠️  OVERALL SECURITY STATUS: ❌ NEEDS ATTENTION');
    console.log(`🚨 ${criticalFailures} critical security issue(s) found.`);
    console.log('🔧 Please address the failed tests before deployment.');
  }
  console.log('='.repeat(50));
  
  // Security Recommendations
  console.log('\n💡 SECURITY RECOMMENDATIONS:');
  console.log('1. ✅ Implement server-side answer validation (DONE)');
  console.log('2. ✅ Remove answer exposure from API responses (DONE)');
  console.log('3. ✅ Add authentication to quiz submission (DONE)');
  console.log('4. 🔄 Add rate limiting for quiz attempts (IN PROGRESS)');
  console.log('5. 🔄 Implement CSRF protection (RECOMMENDED)');
  console.log('6. 🔄 Add comprehensive audit logging (RECOMMENDED)');
  console.log('7. 🔄 Implement session-based quiz management (RECOMMENDED)');
  
  return overallSecure;
}

// Run verification
if (require.main === module) {
  runSecurityVerification()
    .then(secure => {
      process.exit(secure ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Security verification failed:', error);
      process.exit(1);
    });
}

export { runSecurityVerification };
