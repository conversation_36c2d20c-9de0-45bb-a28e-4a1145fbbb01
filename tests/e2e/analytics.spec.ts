import { test, expect } from '@playwright/test';

test.describe('Analytics Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');

    // Navigate to analytics
    await page.goto('/dashboard/analytics');
  });

  test('should display analytics overview', async ({ page }) => {
    // Should show analytics page title
    await expect(page.locator('h1')).toContainText('Analytics');

    // Should show key metrics
    const metrics = page.locator('.metric, .stat-card, .analytics-card');
    const metricCount = await metrics.count();
    expect(metricCount).toBeGreaterThan(0);

    // Should show numbers
    const numbers = page.locator('text=/\\d+/');
    await expect(numbers.first()).toBeVisible();
  });

  test('should display quiz performance metrics', async ({ page }) => {
    // Look for quiz performance section
    const performanceSection = page.locator('.quiz-performance, .performance-metrics');
    if (await performanceSection.isVisible()) {
      await expect(performanceSection).toBeVisible();

      // Should show completion rates
      const completionRate = page.locator('text=/completion/i, text=/complete/i');
      if (await completionRate.count() > 0) {
        await expect(completionRate.first()).toBeVisible();
      }

      // Should show average scores
      const averageScore = page.locator('text=/average/i, text=/score/i');
      if (await averageScore.count() > 0) {
        await expect(averageScore.first()).toBeVisible();
      }
    }
  });

  test('should display user engagement metrics', async ({ page }) => {
    // Look for user engagement data
    const engagementSection = page.locator('.user-engagement, .engagement-metrics');
    if (await engagementSection.isVisible()) {
      await expect(engagementSection).toBeVisible();

      // Should show active users
      const activeUsers = page.locator('text=/active users/i, text=/users/i');
      if (await activeUsers.count() > 0) {
        await expect(activeUsers.first()).toBeVisible();
      }
    }
  });

  test('should display charts and graphs', async ({ page }) => {
    // Look for chart containers
    const charts = page.locator('.chart, .graph, canvas, svg');
    if (await charts.count() > 0) {
      await expect(charts.first()).toBeVisible();

      // Wait for charts to load
      await page.waitForTimeout(2000);

      // Verify chart has content
      const chartContent = await charts.first().isVisible();
      expect(chartContent).toBe(true);
    }
  });

  test('should filter analytics by date range', async ({ page }) => {
    // Look for date range picker
    const dateFilter = page.locator('.date-picker, .date-range, input[type="date"]');
    if (await dateFilter.count() > 0) {
      const firstDateInput = dateFilter.first();
      await firstDateInput.click();

      // Set a date range
      await firstDateInput.fill('2024-01-01');

      // Look for apply button or second date input
      const applyBtn = page.locator('button:has-text("Apply"), button:has-text("Filter")');
      if (await applyBtn.isVisible()) {
        await applyBtn.click();

        // Wait for data to update
        await page.waitForTimeout(1000);

        // Verify data updated (this is basic - real test would check specific values)
        await expect(page.locator('.metric, .stat-card')).toBeVisible();
      }
    }
  });

  test('should export analytics data', async ({ page }) => {
    // Look for export button
    const exportBtn = page.locator('button:has-text("Export"), button:has-text("Download")');
    if (await exportBtn.isVisible()) {
      // Set up download handler
      const downloadPromise = page.waitForEvent('download');

      await exportBtn.click();

      // Wait for download to start
      const download = await downloadPromise;

      // Verify download started
      expect(download.suggestedFilename()).toMatch(/\.(csv|xlsx|pdf)$/);
    }
  });

  test('should display quiz-specific analytics', async ({ page }) => {
    // Look for quiz selection dropdown
    const quizSelector = page.locator('select[name="quiz"], .quiz-selector');
    if (await quizSelector.isVisible()) {
      // Select a specific quiz
      await quizSelector.click();

      const quizOptions = page.locator('option, .quiz-option');
      if (await quizOptions.count() > 1) {
        await quizOptions.nth(1).click();

        // Wait for data to load
        await page.waitForTimeout(1000);

        // Should show quiz-specific metrics
        await expect(page.locator('.quiz-analytics, .quiz-metrics')).toBeVisible();
      }
    }
  });

  test('should show question-level analytics', async ({ page }) => {
    // Look for question analytics section
    const questionAnalytics = page.locator('.question-analytics, .question-performance');
    if (await questionAnalytics.isVisible()) {
      await expect(questionAnalytics).toBeVisible();

      // Should show question difficulty or success rates
      const questionMetrics = page.locator('.question-metric, .question-stat');
      if (await questionMetrics.count() > 0) {
        await expect(questionMetrics.first()).toBeVisible();
      }
    }
  });

  test('should display real-time analytics', async ({ page }) => {
    // Look for real-time indicators
    const realTimeSection = page.locator('.real-time, .live-data');
    if (await realTimeSection.isVisible()) {
      await expect(realTimeSection).toBeVisible();

      // Should show current active users or sessions
      const liveMetrics = page.locator('text=/live/i, text=/active/i, text=/online/i');
      if (await liveMetrics.count() > 0) {
        await expect(liveMetrics.first()).toBeVisible();
      }
    }
  });

  test('should handle analytics refresh', async ({ page }) => {
    // Look for refresh button
    const refreshBtn = page.locator('button:has-text("Refresh"), button[aria-label*="refresh"]');
    if (await refreshBtn.isVisible()) {
      // Get initial data
      const initialMetric = await page.locator('.metric, .stat-card').first().textContent();

      // Click refresh
      await refreshBtn.click();

      // Wait for refresh to complete
      await page.waitForTimeout(2000);

      // Verify page is still functional
      await expect(page.locator('.metric, .stat-card')).toBeVisible();
    }
  });
});

test.describe('Analytics - User View', () => {
  test.beforeEach(async ({ page }) => {
    // Login as regular user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
  });

  test('should show user personal analytics', async ({ page }) => {
    // Navigate to user analytics (if available)
    const analyticsLink = page.locator('a[href*="analytics"], text=Analytics');
    if (await analyticsLink.isVisible()) {
      await analyticsLink.click();

      // Should show personal performance
      const personalStats = page.locator('.personal-stats, .my-performance');
      if (await personalStats.isVisible()) {
        await expect(personalStats).toBeVisible();

        // Should show user's quiz scores
        const scores = page.locator('text=/score/i, text=/points/i');
        if (await scores.count() > 0) {
          await expect(scores.first()).toBeVisible();
        }
      }
    }
  });

  test('should not show admin analytics', async ({ page }) => {
    // Try to access admin analytics
    await page.goto('/dashboard/analytics');

    // Should either redirect or show limited view
    const adminMetrics = page.locator('.admin-analytics, .system-metrics');
    await expect(adminMetrics).not.toBeVisible();
  });
});
