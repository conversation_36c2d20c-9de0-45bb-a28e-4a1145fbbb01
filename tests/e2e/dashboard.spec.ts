import { test, expect } from '@playwright/test';

test.describe('Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
  });

  test('should display dashboard overview', async ({ page }) => {
    // Should show main dashboard elements
    await expect(page.locator('h1')).toBeVisible();

    // Should show some form of navigation
    const navigation = page.locator('nav, .navigation, .nav-menu, a[href*="dashboard"]');
    const navCount = await navigation.count();
    expect(navCount).toBeGreaterThan(0);

    // Should show dashboard content (any content is fine)
    const content = page.locator('main, .main-content, .dashboard-content, .content');
    const contentCount = await content.count();
    if (contentCount === 0) {
      // Fallback: check for any visible content
      const anyContent = page.locator('body *:visible');
      const anyContentCount = await anyContent.count();
      expect(anyContentCount).toBeGreaterThan(5); // Should have some content
    } else {
      expect(contentCount).toBeGreaterThan(0);
    }
  });

  test('should navigate to different dashboard sections', async ({ page }) => {
    // Test navigation to quizzes
    await page.click('a[href="/dashboard/quizzes"], text=Quizzes');
    await page.waitForURL('/dashboard/quizzes');
    await expect(page.locator('h1')).toContainText('Quizzes');

    // Test navigation to analytics
    await page.click('a[href="/dashboard/analytics"], text=Analytics');
    await page.waitForURL('/dashboard/analytics');
    await expect(page.locator('h1')).toContainText('Analytics');

    // Test navigation to admin section (admin only)
    const adminLink = page.locator('a[href="/dashboard/admin"], text=Admin');
    if (await adminLink.isVisible()) {
      await adminLink.click();
      await page.waitForURL('/dashboard/admin');
      await expect(page.locator('h1')).toContainText('Admin');
    }
  });

  test('should display user profile information', async ({ page }) => {
    // Look for user profile section
    const profileSection = page.locator('.user-profile, .profile-info, [data-testid="user-profile"]');
    if (await profileSection.isVisible()) {
      await expect(profileSection).toContainText('<EMAIL>');
    }

    // Check for user menu or dropdown
    const userMenu = page.locator('.user-menu, .profile-dropdown, button:has-text("admin")');
    if (await userMenu.isVisible()) {
      await userMenu.click();
      await expect(page.locator('text=Logout, text=Sign out')).toBeVisible();
    }
  });

  test('should show recent activity', async ({ page }) => {
    // Look for recent activity section
    const activitySection = page.locator('.recent-activity, .activity-feed, [data-testid="recent-activity"]');
    if (await activitySection.isVisible()) {
      await expect(activitySection).toBeVisible();

      // Should show some activity items
      const activityItems = page.locator('.activity-item, .activity-entry');
      const activityCount = await activityItems.count();
      if (activityCount > 0) {
        await expect(activityItems.first()).toBeVisible();
      }
    }
  });

  test('should display quiz statistics', async ({ page }) => {
    // Look for quiz statistics
    const statsElements = page.locator('.quiz-stats, .statistics, .metrics');
    if (await statsElements.count() > 0) {
      await expect(statsElements.first()).toBeVisible();

      // Should show numbers or charts
      const numbers = page.locator('text=/\\d+/');
      await expect(numbers.first()).toBeVisible();
    }
  });

  test('should handle responsive design', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();

    // Should still show main content
    await expect(page.locator('h1')).toBeVisible();

    // Mobile menu should be accessible
    const mobileMenu = page.locator('.mobile-menu, .hamburger, button[aria-label*="menu"]');
    if (await mobileMenu.isVisible()) {
      await mobileMenu.click();
      await expect(page.locator('nav, .navigation')).toBeVisible();
    }

    // Reset to desktop
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('should search functionality', async ({ page }) => {
    // Look for search input
    const searchInput = page.locator('input[type="search"], input[placeholder*="search"], input[placeholder*="Search"]');
    if (await searchInput.isVisible()) {
      await searchInput.fill('security');
      await searchInput.press('Enter');

      // Should show search results or filter content
      await page.waitForTimeout(1000); // Wait for search to process

      // Verify search worked (results or filtered content)
      const results = page.locator('.search-results, .filtered-content');
      if (await results.isVisible()) {
        await expect(results).toBeVisible();
      }
    }
  });

  test('should handle notifications', async ({ page }) => {
    // Look for notification bell or indicator
    const notificationBell = page.locator('.notification-bell, .notifications, [data-testid="notifications"]');
    if (await notificationBell.isVisible()) {
      await notificationBell.click();

      // Should show notifications dropdown or panel
      await expect(page.locator('.notification-dropdown, .notification-panel')).toBeVisible();
    }
  });

  test('should display quick actions', async ({ page }) => {
    // Look for quick action buttons
    const quickActions = page.locator('.quick-actions, .action-buttons');
    if (await quickActions.isVisible()) {
      await expect(quickActions).toBeVisible();

      // Should have create quiz button for admin
      const createQuizBtn = page.locator('button:has-text("Create Quiz"), a:has-text("Create Quiz")');
      if (await createQuizBtn.isVisible()) {
        await expect(createQuizBtn).toBeVisible();
      }
    }
  });

  test('should handle dark mode toggle', async ({ page }) => {
    // Look for theme toggle
    const themeToggle = page.locator('.theme-toggle, .dark-mode-toggle, button[aria-label*="theme"]');
    if (await themeToggle.isVisible()) {
      // Get initial theme
      const initialTheme = await page.evaluate(() => document.documentElement.classList.contains('dark'));

      // Toggle theme
      await themeToggle.click();

      // Wait for theme change
      await page.waitForTimeout(500);

      // Verify theme changed
      const newTheme = await page.evaluate(() => document.documentElement.classList.contains('dark'));
      expect(newTheme).not.toBe(initialTheme);
    }
  });
});

test.describe('Dashboard - User Role', () => {
  test.beforeEach(async ({ page }) => {
    // Login as regular user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
  });

  test('should hide admin features for regular users', async ({ page }) => {
    // Should not show admin navigation
    await expect(page.locator('a[href="/dashboard/admin"]')).not.toBeVisible();

    // Should not show create quiz button
    await expect(page.locator('button:has-text("Create Quiz")')).not.toBeVisible();

    // Should show user-appropriate content
    await expect(page.locator('text=Take Quiz, text=Browse Quizzes')).toBeVisible();
  });

  test('should show user-specific dashboard content', async ({ page }) => {
    // Should show user's quiz history or progress
    const userContent = page.locator('.user-progress, .quiz-history, .my-quizzes');
    if (await userContent.isVisible()) {
      await expect(userContent).toBeVisible();
    }

    // Should show available quizzes to take
    const availableQuizzes = page.locator('.available-quizzes, .browse-quizzes');
    if (await availableQuizzes.isVisible()) {
      await expect(availableQuizzes).toBeVisible();
    }
  });
});
