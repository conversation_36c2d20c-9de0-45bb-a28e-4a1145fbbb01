import { test, expect } from '@playwright/test';

test.describe('Quiz Search and Discovery', () => {
  test.beforeEach(async ({ page }) => {
    // Login as regular user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
  });

  test('should search quizzes by title', async ({ page }) => {
    // Navigate to quiz browse page
    await page.goto('/security-quizzes');

    // Look for search input
    const searchInput = page.locator('input[type="search"], input[placeholder*="search"], input[placeholder*="Search"]');
    if (await searchInput.isVisible()) {
      // Search for specific term
      await searchInput.fill('security');
      await searchInput.press('Enter');

      // Wait for search results
      await page.waitForTimeout(1000);

      // Should show filtered results
      const quizCards = page.locator('.quiz-card, .quiz-item');
      if (await quizCards.count() > 0) {
        // Verify results contain search term
        const firstCard = quizCards.first();
        const cardText = await firstCard.textContent();
        expect(cardText?.toLowerCase()).toContain('security');
      }
    }
  });

  test('should filter quizzes by category', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Look for category filter
    const categoryFilter = page.locator('select[name*="category"], .category-filter select');
    if (await categoryFilter.isVisible()) {
      // Select a category
      await categoryFilter.selectOption('Web Application Security');

      // Wait for filtering
      await page.waitForTimeout(1000);

      // Should show filtered results
      const quizCards = page.locator('.quiz-card, .quiz-item');
      if (await quizCards.count() > 0) {
        await expect(quizCards.first()).toBeVisible();
      }
    }
  });

  test('should filter quizzes by difficulty', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Look for difficulty filter
    const difficultyFilter = page.locator('select[name*="difficulty"], .difficulty-filter select');
    if (await difficultyFilter.isVisible()) {
      // Select difficulty level
      await difficultyFilter.selectOption('Beginner');

      // Wait for filtering
      await page.waitForTimeout(1000);

      // Should show filtered results
      const quizCards = page.locator('.quiz-card, .quiz-item');
      if (await quizCards.count() > 0) {
        // Verify difficulty is shown
        const difficultyBadges = page.locator('text=Beginner, .difficulty-badge');
        if (await difficultyBadges.count() > 0) {
          await expect(difficultyBadges.first()).toBeVisible();
        }
      }
    }
  });

  test('should sort quizzes', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Look for sort dropdown
    const sortSelect = page.locator('select[name*="sort"], .sort-select select');
    if (await sortSelect.isVisible()) {
      // Get initial order
      const initialQuizzes = await page.locator('.quiz-card h3, .quiz-title').allTextContents();

      // Change sort order
      await sortSelect.selectOption('title');
      await page.waitForTimeout(1000);

      // Get new order
      const sortedQuizzes = await page.locator('.quiz-card h3, .quiz-title').allTextContents();

      // Verify order changed (basic check)
      expect(sortedQuizzes).not.toEqual(initialQuizzes);
    }
  });

  test('should show quiz details in search results', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Should show quiz cards with details
    const quizCards = page.locator('.quiz-card, .quiz-item');
    if (await quizCards.count() > 0) {
      const firstCard = quizCards.first();

      // Should show title
      await expect(firstCard.locator('h3, .quiz-title')).toBeVisible();

      // Should show description
      const description = firstCard.locator('.description, .quiz-description');
      if (await description.isVisible()) {
        await expect(description).toBeVisible();
      }

      // Should show difficulty
      const difficulty = firstCard.locator('.difficulty, .difficulty-badge');
      if (await difficulty.isVisible()) {
        await expect(difficulty).toBeVisible();
      }

      // Should show question count
      const questionCount = firstCard.locator('text=/\\d+ questions?/');
      if (await questionCount.isVisible()) {
        await expect(questionCount).toBeVisible();
      }
    }
  });

  test('should handle empty search results', async ({ page }) => {
    await page.goto('/security-quizzes');

    const searchInput = page.locator('input[type="search"], input[placeholder*="search"]');
    if (await searchInput.isVisible()) {
      // Search for something that doesn't exist
      await searchInput.fill('nonexistentquizxyz123');
      await searchInput.press('Enter');

      await page.waitForTimeout(1000);

      // Should show no results message
      const noResults = page.locator('text=No quizzes found, text=No results, .no-results');
      if (await noResults.isVisible()) {
        await expect(noResults).toBeVisible();
      }
    }
  });

  test('should clear search filters', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Apply some filters
    const searchInput = page.locator('input[type="search"]');
    if (await searchInput.isVisible()) {
      await searchInput.fill('test');
      await searchInput.press('Enter');
      await page.waitForTimeout(1000);

      // Look for clear button
      const clearBtn = page.locator('button:has-text("Clear"), button:has-text("Reset"), .clear-filters');
      if (await clearBtn.isVisible()) {
        await clearBtn.click();

        // Should reset search
        const searchValue = await searchInput.inputValue();
        expect(searchValue).toBe('');

        // Should show all quizzes again
        await page.waitForTimeout(1000);
        const quizCards = page.locator('.quiz-card, .quiz-item');
        const cardCount = await quizCards.count();
        expect(cardCount).toBeGreaterThan(0);
      }
    }
  });

  test('should paginate search results', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Look for pagination
    const pagination = page.locator('.pagination, .page-navigation');
    if (await pagination.isVisible()) {
      // Should show page numbers
      const pageNumbers = page.locator('.page-number, .pagination button');
      if (await pageNumbers.count() > 1) {
        // Click next page
        const nextBtn = page.locator('button:has-text("Next"), .next-page');
        if (await nextBtn.isVisible()) {
          await nextBtn.click();

          // Should load new results
          await page.waitForTimeout(1000);
          await expect(page.locator('.quiz-card, .quiz-item')).toBeVisible();
        }
      }
    }
  });

  test('should show recent searches', async ({ page }) => {
    await page.goto('/security-quizzes');

    const searchInput = page.locator('input[type="search"]');
    if (await searchInput.isVisible()) {
      // Perform a search
      await searchInput.fill('network');
      await searchInput.press('Enter');
      await page.waitForTimeout(1000);

      // Clear and focus search again
      await searchInput.clear();
      await searchInput.click();

      // Look for recent searches dropdown
      const recentSearches = page.locator('.recent-searches, .search-history');
      if (await recentSearches.isVisible()) {
        await expect(recentSearches).toContainText('network');
      }
    }
  });

  test('should suggest search terms', async ({ page }) => {
    await page.goto('/security-quizzes');

    const searchInput = page.locator('input[type="search"]');
    if (await searchInput.isVisible()) {
      // Start typing
      await searchInput.fill('sec');

      // Wait for suggestions
      await page.waitForTimeout(500);

      // Look for autocomplete suggestions
      const suggestions = page.locator('.search-suggestions, .autocomplete');
      if (await suggestions.isVisible()) {
        await expect(suggestions).toBeVisible();

        // Should be able to click a suggestion
        const firstSuggestion = suggestions.locator('li, .suggestion-item').first();
        if (await firstSuggestion.isVisible()) {
          await firstSuggestion.click();

          // Should populate search and show results
          await page.waitForTimeout(1000);
          await expect(page.locator('.quiz-card, .quiz-item')).toBeVisible();
        }
      }
    }
  });

  test('should handle advanced search', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Look for advanced search toggle
    const advancedSearchBtn = page.locator('button:has-text("Advanced"), .advanced-search-toggle');
    if (await advancedSearchBtn.isVisible()) {
      await advancedSearchBtn.click();

      // Should show advanced search form
      await expect(page.locator('.advanced-search, .search-form')).toBeVisible();

      // Should have multiple search fields
      const searchFields = page.locator('.advanced-search input, .search-form input');
      if (await searchFields.count() > 1) {
        // Fill in multiple criteria
        await searchFields.nth(0).fill('security');

        const categorySelect = page.locator('.advanced-search select');
        if (await categorySelect.isVisible()) {
          await categorySelect.selectOption('Web Application Security');
        }

        // Submit advanced search
        const searchBtn = page.locator('button:has-text("Search"), button[type="submit"]');
        await searchBtn.click();

        // Should show filtered results
        await page.waitForTimeout(1000);
        await expect(page.locator('.quiz-card, .quiz-item')).toBeVisible();
      }
    }
  });

  test('should save search preferences', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Set some preferences
    const difficultyFilter = page.locator('select[name*="difficulty"]');
    if (await difficultyFilter.isVisible()) {
      await difficultyFilter.selectOption('Intermediate');

      // Reload page
      await page.reload();

      // Check if preference was saved
      const selectedValue = await difficultyFilter.inputValue();
      if (selectedValue) {
        expect(selectedValue).toBe('Intermediate');
      }
    }
  });
});
