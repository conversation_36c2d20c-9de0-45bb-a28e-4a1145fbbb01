import { test, expect } from '@playwright/test';

test.describe('Diagnostic Tests - Real Issues', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
  });

  test('DIAGNOSTIC: Check quiz list page functionality', async ({ page }) => {
    console.log('🔍 Testing Quiz List Page...');

    // Navigate to quiz list
    await page.goto('/dashboard/quizzes');
    await page.waitForTimeout(2000);

    // Check page title
    const title = await page.title();
    console.log('Quiz list page title:', title);

    // Check for 404 error
    const is404 = await page.locator('text=404, text=Not Found, text=Page not found').isVisible();
    if (is404) {
      console.log('❌ /dashboard/quizzes returns 404 - route not implemented');
      return;
    }

    console.log('✅ Quiz list page loads');

    // Check for quiz cards
    const quizCards = page.locator('.quiz-card, .quiz-item, [data-testid="quiz-card"]');
    const cardCount = await quizCards.count();
    console.log('Quiz cards found:', cardCount);

    if (cardCount === 0) {
      console.log('⚠️ No quiz cards found - either no quizzes exist or UI not implemented');

      // Check page content
      const pageContent = await page.locator('body').textContent();
      console.log('Page content preview:', pageContent?.substring(0, 300));
    } else {
      console.log('✅ Quiz cards found');

      // Try to click on first quiz card
      const firstCard = quizCards.first();
      const cardLink = firstCard.locator('a').first();

      if (await cardLink.isVisible()) {
        console.log('✅ Quiz card links found');

        const href = await cardLink.getAttribute('href');
        console.log('First quiz card link:', href);

        // Test clicking on the card
        await cardLink.click();
        await page.waitForTimeout(2000);

        const newUrl = page.url();
        console.log('URL after clicking quiz card:', newUrl);

        // Check if we got to edit page or 404
        const is404Edit = await page.locator('text=404, text=Not Found').isVisible();
        if (is404Edit) {
          console.log('❌ Quiz edit page returns 404');
        } else {
          console.log('✅ Quiz edit page loads');
        }
      } else {
        console.log('❌ Quiz cards exist but no clickable links found');
      }
    }

    // This test always passes - it's for diagnosis
    expect(true).toBe(true);
  });

  test('DIAGNOSTIC: Check quiz edit route directly', async ({ page }) => {
    console.log('🔍 Testing Quiz Edit Route Directly...');

    // First create a quiz to get a valid ID
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Diagnostic Test Quiz');
    await page.fill('textarea[id="description"]', 'Testing edit route');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    const currentUrl = page.url();
    const quizId = currentUrl.match(/\/dashboard\/quizzes\/(.*)\/edit/)?.[1];
    console.log('Created quiz with ID:', quizId);

    // Check if current edit page works
    const is404Current = await page.locator('text=404, text=Not Found').isVisible();
    if (is404Current) {
      console.log('❌ Current edit page shows 404');
    } else {
      console.log('✅ Current edit page loads');
    }

    // Test navigating away and back
    await page.goto('/dashboard');
    await page.waitForTimeout(1000);

    // Try to navigate back to edit page
    const editUrl = `/dashboard/quizzes/${quizId}/edit`;
    console.log('Testing direct navigation to:', editUrl);

    await page.goto(editUrl);
    await page.waitForTimeout(2000);

    const is404Direct = await page.locator('text=404, text=Not Found').isVisible();
    if (is404Direct) {
      console.log('❌ Direct navigation to edit page returns 404');

      // Check page content
      const pageContent = await page.locator('body').textContent();
      console.log('404 page content:', pageContent?.substring(0, 200));
    } else {
      console.log('✅ Direct navigation to edit page works');
    }

    expect(true).toBe(true);
  });

  test('DIAGNOSTIC: Check question management UI', async ({ page }) => {
    console.log('🔍 Testing Question Management UI...');

    // Create a quiz first
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Question UI Test');
    await page.fill('textarea[id="description"]', 'Testing question UI');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    // Check if edit page loads
    const is404 = await page.locator('text=404, text=Not Found').isVisible();
    if (is404) {
      console.log('❌ Edit page shows 404 - cannot test question UI');
      return;
    }

    console.log('✅ Edit page loads');

    // Look for Questions tab/button
    const questionsTab = page.locator('button:has-text("Questions"), .questions-tab, [data-testid="questions-tab"]');
    const questionsTabExists = await questionsTab.isVisible();
    console.log('Questions tab exists:', questionsTabExists);

    if (questionsTabExists) {
      await questionsTab.click();
      await page.waitForTimeout(1000);

      // Look for Add Question button
      const addQuestionBtn = page.locator('button:has-text("Add Question"), .add-question, [data-testid="add-question"]');
      const addQuestionExists = await addQuestionBtn.isVisible();
      console.log('Add Question button exists:', addQuestionExists);

      if (addQuestionExists) {
        await addQuestionBtn.click();
        await page.waitForTimeout(1000);

        // Look for question form elements
        const questionTypeSelect = page.locator('select[id="questionType"]');
        const questionTextArea = page.locator('textarea[id="questionText"]');
        const pointsInput = page.locator('input[id="points"]');

        console.log('Question type select exists:', await questionTypeSelect.isVisible());
        console.log('Question text area exists:', await questionTextArea.isVisible());
        console.log('Points input exists:', await pointsInput.isVisible());

        if (await questionTypeSelect.isVisible()) {
          // Try to select multiple choice
          await questionTypeSelect.selectOption('multiple_choice');
          await page.waitForTimeout(1000);

          // Look for option inputs
          const optionInputs = page.locator('input[placeholder*="option"], .option-input');
          const optionCount = await optionInputs.count();
          console.log('Option inputs found:', optionCount);

          if (optionCount > 0) {
            console.log('✅ Question form UI appears to be implemented');
          } else {
            console.log('❌ Question form missing option inputs');
          }
        } else {
          console.log('❌ Question form not properly implemented');
        }
      } else {
        console.log('❌ Add Question button not found');
      }
    } else {
      console.log('❌ Questions tab not found');

      // Log available buttons
      const allButtons = await page.locator('button').allTextContents();
      console.log('Available buttons:', allButtons);
    }

    expect(true).toBe(true);
  });

  test('DIAGNOSTIC: Check question addition workflow', async ({ page }) => {
    console.log('🔍 Testing Question Addition Workflow...');

    // Create a quiz first
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Question Addition Debug');
    await page.fill('textarea[id="description"]', 'Testing question addition');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    console.log('✅ Quiz created and edit page loaded');

    // Navigate to Questions tab
    await page.click('button:has-text("Questions")');
    await page.waitForTimeout(1000);

    console.log('✅ Questions tab clicked');

    // Check initial state
    const initialQuestionCount = await page.locator('text=Questions (').textContent();
    console.log('Initial question count text:', initialQuestionCount);

    // Click Add Question tab
    await page.click('button:has-text("Add Question")');
    await page.waitForTimeout(1000);

    console.log('✅ Add Question tab clicked');

    // Fill in question details
    await page.selectOption('select[id="questionType"]', 'multiple_choice');
    await page.fill('textarea[id="questionText"]', 'Debug test question');
    await page.fill('input[id="points"]', '2');

    console.log('✅ Question details filled');

    // Fill in options
    const optionInputs = page.locator('input[placeholder="Enter option text"]');
    const optionCount = await optionInputs.count();
    console.log('Option inputs found:', optionCount);

    if (optionCount >= 2) {
      await optionInputs.nth(0).fill('Option A Debug');
      await optionInputs.nth(1).fill('Option B Debug');
      console.log('✅ Options filled');

      // Mark first option as correct
      const correctRadio = page.locator('input[type="radio"][name="correctOption"]');
      const radioCount = await correctRadio.count();
      console.log('Correct answer radios found:', radioCount);

      if (radioCount > 0) {
        await correctRadio.first().check();
        console.log('✅ Correct answer marked');
      }
    }

    // Listen for network requests
    page.on('response', response => {
      if (response.url().includes('/api/quizzes/') && response.url().includes('/questions')) {
        console.log('📡 API Response:', response.status(), response.url());
      }
    });

    // Save the question
    console.log('🔄 Clicking Add Question button...');
    await page.click('button:has-text("Add Question")');
    await page.waitForTimeout(3000);

    // Check if we're on existing questions tab
    const currentTab = await page.locator('.tabs-list button[data-state="active"]').textContent();
    console.log('Current active tab:', currentTab);

    // Check for questions in the list
    const questionsList = page.locator('text=Questions (');
    const questionsText = await questionsList.textContent();
    console.log('Questions count after addition:', questionsText);

    // Look for the specific question text
    const questionVisible = await page.locator('text=Debug test question').isVisible();
    console.log('Question visible in list:', questionVisible);

    // Check for any error messages
    const errorMessages = await page.locator('text=Error, text=Failed').count();
    console.log('Error messages found:', errorMessages);

    // Check for success messages
    const successMessages = await page.locator('text=success, text=added').count();
    console.log('Success messages found:', successMessages);

    expect(true).toBe(true);
  });
});
