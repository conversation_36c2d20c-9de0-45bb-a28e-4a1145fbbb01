import { test, expect } from '@playwright/test';

test.describe('Admin Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');

    // Navigate to admin dashboard
    await page.goto('/dashboard/admin');
  });

  test('should display admin dashboard', async ({ page }) => {
    // Should show some page content (admin dashboard might not exist yet)
    await expect(page.locator('h1')).toBeVisible();

    // Should show some navigation or content
    const content = page.locator('main, .content, nav, .navigation');
    const contentCount = await content.count();
    expect(contentCount).toBeGreaterThan(0);
  });

  test('should manage users', async ({ page }) => {
    // Look for user management links
    const userLinks = page.locator('a:has-text("Users"), a:has-text("User Management"), a[href*="users"]');
    const userLinkCount = await userLinks.count();

    if (userLinkCount > 0) {
      await userLinks.first().click();
      await page.waitForTimeout(1000);

      // Should show some content (user list, table, or form)
      const userContent = page.locator('.user-list, .users-table, table, form, .content');
      const contentCount = await userContent.count();
      expect(contentCount).toBeGreaterThan(0);
    } else {
      // User management might not be implemented yet - that's ok
      console.log('User management not found - feature may not be implemented yet');
    }
  });

  test('should create new user', async ({ page }) => {
    // Navigate to user management
    await page.goto('/dashboard/admin/users');

    // Look for create user button
    const createUserBtn = page.locator('button:has-text("Create User"), button:has-text("Add User")');
    if (await createUserBtn.isVisible()) {
      await createUserBtn.click();

      // Should show create user form
      await expect(page.locator('form, .user-form')).toBeVisible();

      // Fill in user details
      const nameInput = page.locator('input[id="name"], input[name="name"]');
      const emailInput = page.locator('input[id="email"], input[name="email"]');
      const roleSelect = page.locator('select[id="role"], select[name="role"]');

      if (await nameInput.isVisible()) {
        await nameInput.fill('Test User');
        await emailInput.fill(`testuser${Date.now()}@example.com`);

        if (await roleSelect.isVisible()) {
          await roleSelect.selectOption('user');
        }

        // Submit form
        const submitBtn = page.locator('button[type="submit"], button:has-text("Create")');
        await submitBtn.click();

        // Should show success message or redirect
        await expect(page.locator('text=User created, text=Success')).toBeVisible();
      }
    }
  });

  test('should manage quiz settings', async ({ page }) => {
    // Look for quiz management links
    const quizLinks = page.locator('a:has-text("Quiz"), a:has-text("Quizzes"), a[href*="quizzes"]');
    const quizLinkCount = await quizLinks.count();

    if (quizLinkCount > 0) {
      await quizLinks.first().click();
      await page.waitForTimeout(1000);

      // Should show some content
      const content = page.locator('.quiz-management, .admin-quizzes, .content, main');
      const contentCount = await content.count();
      expect(contentCount).toBeGreaterThan(0);
    } else {
      console.log('Quiz management not found - feature may not be implemented yet');
    }
  });

  test('should configure system settings', async ({ page }) => {
    // Look for settings section
    const settingsLinks = page.locator('a:has-text("Settings"), a:has-text("Configuration"), a[href*="settings"]');
    const settingsCount = await settingsLinks.count();

    if (settingsCount > 0) {
      await settingsLinks.first().click();
      await page.waitForTimeout(1000);

      // Should show some content
      const content = page.locator('form, .settings-form, .content, main');
      const contentCount = await content.count();
      expect(contentCount).toBeGreaterThan(0);
    } else {
      console.log('Settings not found - feature may not be implemented yet');
    }
  });

  test('should view system logs', async ({ page }) => {
    // Look for logs section
    const logsLinks = page.locator('a:has-text("Logs"), a:has-text("Activity"), a[href*="logs"]');
    const logsCount = await logsLinks.count();

    if (logsCount > 0) {
      await logsLinks.first().click();
      await page.waitForTimeout(1000);

      // Should show some content
      const content = page.locator('.logs-table, .activity-log, table, .content, main');
      const contentCount = await content.count();
      expect(contentCount).toBeGreaterThan(0);
    } else {
      console.log('Logs not found - feature may not be implemented yet');
    }
  });

  test('should manage categories and tags', async ({ page }) => {
    // Look for category management
    const categoryLinks = page.locator('a:has-text("Categories"), a:has-text("Tags"), a[href*="categories"]');
    const categoryCount = await categoryLinks.count();

    if (categoryCount > 0) {
      await categoryLinks.first().click();
      await page.waitForTimeout(1000);

      // Should show some content
      const content = page.locator('.category-management, .tags-management, .content, main');
      const contentCount = await content.count();
      expect(contentCount).toBeGreaterThan(0);
    } else {
      console.log('Category management not found - feature may not be implemented yet');
    }
  });

  test('should backup and restore data', async ({ page }) => {
    // Look for backup section
    const backupLinks = page.locator('a:has-text("Backup"), a:has-text("Export"), a[href*="backup"]');
    const backupCount = await backupLinks.count();

    if (backupCount > 0) {
      await backupLinks.first().click();
      await page.waitForTimeout(1000);

      // Should show some content
      const content = page.locator('.backup-section, .export-section, .content, main');
      const contentCount = await content.count();
      expect(contentCount).toBeGreaterThan(0);
    } else {
      console.log('Backup functionality not found - feature may not be implemented yet');
    }
  });

  test('should monitor system health', async ({ page }) => {
    // Look for system health section
    const healthLinks = page.locator('a:has-text("Health"), a:has-text("Status"), a[href*="health"]');
    const healthCount = await healthLinks.count();

    if (healthCount > 0) {
      await healthLinks.first().click();
      await page.waitForTimeout(1000);

      // Should show some content
      const content = page.locator('.system-health, .status-dashboard, .content, main');
      const contentCount = await content.count();
      expect(contentCount).toBeGreaterThan(0);
    } else {
      console.log('System health monitoring not found - feature may not be implemented yet');
    }
  });

  test('should manage permissions and roles', async ({ page }) => {
    // Look for permissions section
    const permissionLinks = page.locator('a:has-text("Permissions"), a:has-text("Roles"), a[href*="permissions"]');
    const permissionCount = await permissionLinks.count();

    if (permissionCount > 0) {
      await permissionLinks.first().click();
      await page.waitForTimeout(1000);

      // Should show some content
      const content = page.locator('.permissions-management, .roles-management, .content, main');
      const contentCount = await content.count();
      expect(contentCount).toBeGreaterThan(0);
    } else {
      console.log('Permissions management not found - feature may not be implemented yet');
    }
  });

  test('should handle bulk operations', async ({ page }) => {
    // Navigate to user management for bulk operations
    await page.goto('/dashboard/admin/users');

    // Look for checkboxes to select multiple items
    const checkboxes = page.locator('input[type="checkbox"]');
    if (await checkboxes.count() > 2) {
      // Select multiple items
      await checkboxes.nth(1).check();
      await checkboxes.nth(2).check();

      // Look for bulk action dropdown
      const bulkActionSelect = page.locator('select[name*="bulk"], .bulk-actions select');
      if (await bulkActionSelect.isVisible()) {
        await bulkActionSelect.selectOption('delete');

        const confirmBtn = page.locator('button:has-text("Apply"), button:has-text("Execute")');
        if (await confirmBtn.isVisible()) {
          await confirmBtn.click();

          // Should show confirmation dialog
          await expect(page.locator('.confirmation-dialog, .modal')).toBeVisible();
        }
      }
    }
  });
});

test.describe('Admin Access Control', () => {
  test('should deny access to non-admin users', async ({ page }) => {
    // Login as regular user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');

    // Try to access admin dashboard
    await page.goto('/dashboard/admin');

    // Should be redirected or show access denied
    const currentUrl = page.url();
    const isAccessDenied = currentUrl.includes('/dashboard') && !currentUrl.includes('/admin');
    const hasAccessDeniedMessage = await page.locator('text=Access denied, text=Unauthorized').isVisible();

    expect(isAccessDenied || hasAccessDeniedMessage).toBe(true);
  });
});
