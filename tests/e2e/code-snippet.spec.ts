import { test, expect } from '@playwright/test';

test.describe('Code Snippet Functionality', () => {
  test('should display code snippets with syntax highlighting', async ({ page }) => {
    // Navigate to the code security quiz
    await page.goto('/quiz/683660ad0c42de016c5eb070');
    await page.waitForLoadState('networkidle');

    // Wait for the quiz to load
    await page.waitForSelector('[data-testid="question-renderer"], .question-container', { timeout: 10000 });

    // Look for code snippet elements
    const codeSnippet = page.locator('.react-syntax-highlighter, pre code, .code-snippet');
    
    // Check if code snippet is visible
    if (await codeSnippet.first().isVisible()) {
      await expect(codeSnippet.first()).toBeVisible();
      console.log('✅ Code snippet found and visible');

      // Check for syntax highlighting (should have colored elements)
      const highlightedElements = page.locator('.react-syntax-highlighter .token, .hljs-keyword, .hljs-string');
      if (await highlightedElements.first().isVisible()) {
        await expect(highlightedElements.first()).toBeVisible();
        console.log('✅ Syntax highlighting detected');
      }

      // Check for copy button
      const copyButton = page.locator('button[title="Copy code"], button:has-text("Copy")');
      if (await copyButton.first().isVisible()) {
        await expect(copyButton.first()).toBeVisible();
        console.log('✅ Copy button found');
      }

      // Check for language badge
      const languageBadge = page.locator('.badge, .language-badge');
      if (await languageBadge.first().isVisible()) {
        await expect(languageBadge.first()).toBeVisible();
        console.log('✅ Language badge found');
      }

      // Check for filename display
      const filename = page.locator('text="user_auth.py", text=".py", text=".js"');
      if (await filename.first().isVisible()) {
        await expect(filename.first()).toBeVisible();
        console.log('✅ Filename display found');
      }
    } else {
      console.log('ℹ️  No code snippet found on this question, checking next question...');
      
      // Try to navigate to next question if available
      const nextButton = page.locator('button:has-text("Next"), button:has-text("Continue")');
      if (await nextButton.isVisible()) {
        await nextButton.click();
        await page.waitForTimeout(1000);
        
        // Check again for code snippet
        const nextCodeSnippet = page.locator('.react-syntax-highlighter, pre code, .code-snippet');
        if (await nextCodeSnippet.first().isVisible()) {
          await expect(nextCodeSnippet.first()).toBeVisible();
          console.log('✅ Code snippet found on next question');
        }
      }
    }
  });

  test('should handle code snippet copy functionality', async ({ page }) => {
    // Navigate to the code security quiz
    await page.goto('/quiz/683660ad0c42de016c5eb070');
    await page.waitForLoadState('networkidle');

    // Wait for the quiz to load
    await page.waitForSelector('[data-testid="question-renderer"], .question-container', { timeout: 10000 });

    // Look for copy button
    const copyButton = page.locator('button[title="Copy code"], button:has-text("Copy")');
    
    if (await copyButton.first().isVisible()) {
      // Grant clipboard permissions
      await page.context().grantPermissions(['clipboard-read', 'clipboard-write']);
      
      // Click copy button
      await copyButton.first().click();
      
      // Check for success message (toast or similar)
      const successMessage = page.locator('text="copied", text="Copied", .toast, .notification');
      if (await successMessage.first().isVisible({ timeout: 3000 })) {
        await expect(successMessage.first()).toBeVisible();
        console.log('✅ Copy success message displayed');
      }
    } else {
      console.log('ℹ️  No copy button found, skipping copy test');
    }
  });

  test('should display code snippet metadata correctly', async ({ page }) => {
    // Navigate to the code security quiz
    await page.goto('/quiz/683660ad0c42de016c5eb070');
    await page.waitForLoadState('networkidle');

    // Wait for the quiz to load
    await page.waitForSelector('[data-testid="question-renderer"], .question-container', { timeout: 10000 });

    // Check for code snippet card structure
    const codeCard = page.locator('.code-snippet, [class*="Card"]').filter({ has: page.locator('pre, code') });
    
    if (await codeCard.first().isVisible()) {
      // Check for header with filename and language
      const cardHeader = codeCard.first().locator('[class*="CardHeader"], .card-header');
      if (await cardHeader.isVisible()) {
        await expect(cardHeader).toBeVisible();
        console.log('✅ Code snippet header found');
      }

      // Check for language badge
      const languageBadge = codeCard.first().locator('.badge, [class*="Badge"]');
      if (await languageBadge.first().isVisible()) {
        await expect(languageBadge.first()).toBeVisible();
        console.log('✅ Language badge in code snippet found');
      }

      // Check for caption if present
      const caption = codeCard.first().locator('.caption, [class*="caption"], .text-muted-foreground');
      if (await caption.first().isVisible()) {
        await expect(caption.first()).toBeVisible();
        console.log('✅ Code snippet caption found');
      }
    } else {
      console.log('ℹ️  No code snippet card found');
    }
  });

  test('should handle questions without code snippets', async ({ page }) => {
    // Navigate to the code security quiz
    await page.goto('/quiz/683660ad0c42de016c5eb070');
    await page.waitForLoadState('networkidle');

    // Wait for the quiz to load
    await page.waitForSelector('[data-testid="question-renderer"], .question-container', { timeout: 10000 });

    // Navigate through questions to find one without code snippet
    let foundQuestionWithoutCode = false;
    let attempts = 0;
    const maxAttempts = 6; // We have 6 questions

    while (!foundQuestionWithoutCode && attempts < maxAttempts) {
      const codeSnippet = page.locator('.react-syntax-highlighter, pre code, .code-snippet');
      
      if (!(await codeSnippet.first().isVisible())) {
        foundQuestionWithoutCode = true;
        console.log('✅ Found question without code snippet');
        
        // Verify the question still renders properly
        const questionText = page.locator('[data-testid="question-text"], .question-text, .prose');
        await expect(questionText.first()).toBeVisible();
        console.log('✅ Question without code snippet renders correctly');
        
        break;
      }

      // Try to go to next question
      const nextButton = page.locator('button:has-text("Next"), button:has-text("Continue")');
      if (await nextButton.isVisible()) {
        await nextButton.click();
        await page.waitForTimeout(1000);
        attempts++;
      } else {
        break;
      }
    }

    if (!foundQuestionWithoutCode) {
      console.log('ℹ️  All questions appear to have code snippets');
    }
  });
});
