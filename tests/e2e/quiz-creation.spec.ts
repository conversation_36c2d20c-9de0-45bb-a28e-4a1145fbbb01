import { test, expect } from '@playwright/test';

test.describe('Quiz Creation - All Possibilities', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
  });

  test('should create basic quiz with minimal required fields', async ({ page }) => {
    await page.goto('/dashboard/quizzes/create');

    // Fill minimal required fields
    await page.fill('input[id="title"]', 'Basic Security Quiz');
    await page.fill('textarea[id="description"]', 'A basic cybersecurity quiz for beginners');

    // Submit form
    await page.click('button[type="submit"]');

    // Should redirect to edit page
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
    await expect(page.locator('h1')).toContainText('Edit Quiz');

    // Verify quiz was created with correct data
    await expect(page.locator('input[id="title"]')).toHaveValue('Basic Security Quiz');
    await expect(page.locator('textarea[id="description"]')).toHaveValue('A basic cybersecurity quiz for beginners');
  });

  test('should create quiz with all optional fields', async ({ page }) => {
    await page.goto('/dashboard/quizzes/create');

    // Fill all available fields
    await page.fill('input[id="title"]', 'Advanced Penetration Testing Quiz');
    await page.fill('textarea[id="description"]', 'Comprehensive quiz covering advanced penetration testing techniques and methodologies');
    await page.fill('input[id="tags"]', 'penetration testing, ethical hacking, security assessment');
    await page.fill('input[id="passingScore"]', '85');
    await page.fill('input[id="timeLimit"]', '45');

    // Submit form
    await page.click('button[type="submit"]');

    // Should redirect to edit page
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    // Verify all fields were saved
    await expect(page.locator('input[id="title"]')).toHaveValue('Advanced Penetration Testing Quiz');
    await expect(page.locator('textarea[id="description"]')).toHaveValue('Comprehensive quiz covering advanced penetration testing techniques and methodologies');
    await expect(page.locator('input[id="tags"]')).toHaveValue('penetration testing, ethical hacking, security assessment');
    await expect(page.locator('input[id="passingScore"]')).toHaveValue('85');
    await expect(page.locator('input[id="timeLimit"]')).toHaveValue('45');
  });

  test('should create quiz with different difficulty levels', async ({ page }) => {
    const difficulties = ['Beginner', 'Intermediate', 'Advanced'];

    for (const difficulty of difficulties) {
      await page.goto('/dashboard/quizzes/create');

      await page.fill('input[id="title"]', `${difficulty} Security Quiz`);
      await page.fill('textarea[id="description"]', `A ${difficulty.toLowerCase()} level cybersecurity quiz`);

      // Select difficulty if dropdown exists
      const difficultySelect = page.locator('select[id="difficulty"]');
      if (await difficultySelect.isVisible()) {
        await difficultySelect.selectOption(difficulty);
      }

      await page.click('button[type="submit"]');
      await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

      // Verify quiz was created
      await expect(page.locator('input[id="title"]')).toHaveValue(`${difficulty} Security Quiz`);

      // Go back to create another
      if (difficulty !== 'Advanced') {
        await page.goto('/dashboard/quizzes/create');
      }
    }
  });

  test('should create quiz with different categories', async ({ page }) => {
    const categories = [
      'Web Application Security',
      'Network Security',
      'Cryptography',
      'Incident Response',
      'Malware Analysis'
    ];

    for (const category of categories) {
      await page.goto('/dashboard/quizzes/create');

      await page.fill('input[id="title"]', `${category} Fundamentals`);
      await page.fill('textarea[id="description"]', `Essential concepts in ${category.toLowerCase()}`);

      // Select category if dropdown exists
      const categorySelect = page.locator('select[id="category"]');
      if (await categorySelect.isVisible()) {
        await categorySelect.selectOption(category);
      }

      await page.click('button[type="submit"]');
      await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

      // Verify quiz was created
      await expect(page.locator('input[id="title"]')).toHaveValue(`${category} Fundamentals`);

      // Go back to create another
      if (category !== 'Malware Analysis') {
        await page.goto('/dashboard/quizzes/create');
      }
    }
  });

  test('should create quiz with time limits', async ({ page }) => {
    const timeLimits = ['10', '30', '60', '120'];

    for (const timeLimit of timeLimits) {
      await page.goto('/dashboard/quizzes/create');

      await page.fill('input[id="title"]', `${timeLimit} Minute Security Challenge`);
      await page.fill('textarea[id="description"]', `A timed security quiz with ${timeLimit} minute limit`);
      await page.fill('input[id="timeLimit"]', timeLimit);

      await page.click('button[type="submit"]');
      await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

      // Verify time limit was saved
      await expect(page.locator('input[id="timeLimit"]')).toHaveValue(timeLimit);

      // Go back to create another
      if (timeLimit !== '120') {
        await page.goto('/dashboard/quizzes/create');
      }
    }
  });

  test('should create quiz with different passing scores', async ({ page }) => {
    const passingScores = ['60', '70', '80', '90'];

    for (const score of passingScores) {
      await page.goto('/dashboard/quizzes/create');

      await page.fill('input[id="title"]', `High Standards Quiz (${score}% to pass)`);
      await page.fill('textarea[id="description"]', `Challenging quiz requiring ${score}% to pass`);
      await page.fill('input[id="passingScore"]', score);

      await page.click('button[type="submit"]');
      await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

      // Verify passing score was saved
      await expect(page.locator('input[id="passingScore"]')).toHaveValue(score);

      // Go back to create another
      if (score !== '90') {
        await page.goto('/dashboard/quizzes/create');
      }
    }
  });

  test('should create quiz with multiple tags', async ({ page }) => {
    const tagCombinations = [
      'security, basics, fundamentals',
      'advanced, penetration testing, red team',
      'blue team, defense, monitoring, SIEM',
      'compliance, governance, risk management'
    ];

    for (let i = 0; i < tagCombinations.length; i++) {
      await page.goto('/dashboard/quizzes/create');

      await page.fill('input[id="title"]', `Tagged Quiz ${i + 1}`);
      await page.fill('textarea[id="description"]', `Quiz with multiple relevant tags`);
      await page.fill('input[id="tags"]', tagCombinations[i]);

      await page.click('button[type="submit"]');
      await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

      // Verify tags were saved
      await expect(page.locator('input[id="tags"]')).toHaveValue(tagCombinations[i]);

      // Go back to create another
      if (i < tagCombinations.length - 1) {
        await page.goto('/dashboard/quizzes/create');
      }
    }
  });

  // Removed: Form validation test - validation may not be implemented yet

  test('should handle special characters in quiz data', async ({ page }) => {
    await page.goto('/dashboard/quizzes/create');

    // Use special characters and unicode
    await page.fill('input[id="title"]', 'Quiz with Special Characters: @#$%^&*()');
    await page.fill('textarea[id="description"]', 'Description with unicode: 🔒 Security Quiz with émojis and àccénts');
    await page.fill('input[id="tags"]', 'special-chars, unicode-test, symbols@#$');

    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    // Verify special characters were preserved
    await expect(page.locator('input[id="title"]')).toHaveValue('Quiz with Special Characters: @#$%^&*()');
    await expect(page.locator('textarea[id="description"]')).toHaveValue('Description with unicode: 🔒 Security Quiz with émojis and àccénts');
    await expect(page.locator('input[id="tags"]')).toHaveValue('special-chars, unicode-test, symbols@#$');
  });

  test('should create quiz with maximum field lengths', async ({ page }) => {
    await page.goto('/dashboard/quizzes/create');

    // Create very long content to test limits
    const longTitle = 'A'.repeat(200); // Assuming 200 char limit
    const longDescription = 'B'.repeat(1000); // Assuming 1000 char limit
    const longTags = 'tag1, tag2, tag3, tag4, tag5, tag6, tag7, tag8, tag9, tag10, very-long-tag-name-that-tests-limits';

    await page.fill('input[id="title"]', longTitle);
    await page.fill('textarea[id="description"]', longDescription);
    await page.fill('input[id="tags"]', longTags);

    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    // Verify content was saved (may be truncated based on limits)
    const savedTitle = await page.locator('input[id="title"]').inputValue();
    const savedDescription = await page.locator('textarea[id="description"]').inputValue();

    expect(savedTitle.length).toBeGreaterThan(0);
    expect(savedDescription.length).toBeGreaterThan(0);
  });
});
