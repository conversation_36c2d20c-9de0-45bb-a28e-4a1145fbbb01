import { test, expect } from '@playwright/test';
import { TestDataSeeder } from '../utils/test-data-seeder';
import { AuthHelper } from '../utils/auth-helper';

test.describe('Simple Authentication Test', () => {
  let seeder: TestDataSeeder;

  test.beforeAll(async () => {
    seeder = TestDataSeeder.getInstance();
  });

  test.afterAll(async () => {
    await seeder.cleanupSeededData();
    await seeder.disconnect();
  });

  test('should create test user and login successfully', async ({ page }) => {
    // Create a test user
    const testUser = await seeder.seedTestUser({
      email: `test-${Date.now()}@example.com`,
      password: 'testpassword123'
    });

    // Create auth helper
    const auth = new AuthHelper(page);

    // Navigate to login page
    await page.goto('/auth/login');
    await page.waitForLoadState('networkidle');

    // Fill in credentials
    await page.fill('input[id="email"]', testUser.email);
    await page.fill('input[id="password"]', testUser.password);

    // Submit the form
    await page.click('button[type="submit"]');

    // Wait for redirect to dashboard
    await page.waitForURL(/\/dashboard/, { timeout: 15000 });

    // Verify login was successful by checking URL and Sign Out button
    await expect(page).toHaveURL(/\/dashboard/);
    await expect(page.locator('button:has-text("Sign Out")')).toBeVisible({ timeout: 5000 });

    // Test logout
    await page.click('button:has-text("Sign Out")');
    await page.waitForURL('/', { timeout: 10000 });
    await expect(page.locator('a:has-text("Sign In")')).toBeVisible();
  });

  test('should create admin user and access admin panel', async ({ page }) => {
    // Create a test admin
    const testAdmin = await seeder.seedTestAdmin();

    // Navigate to login page
    await page.goto('/auth/login');
    await page.waitForLoadState('networkidle');

    // Fill in credentials
    await page.fill('input[id="email"]', testAdmin.email);
    await page.fill('input[id="password"]', testAdmin.password);

    // Submit the form
    await page.click('button[type="submit"]');

    // Wait for redirect to dashboard
    await page.waitForURL(/\/dashboard/, { timeout: 15000 });

    // Verify login was successful by checking URL and Sign Out button
    await expect(page).toHaveURL(/\/dashboard/);
    await expect(page.locator('button:has-text("Sign Out")')).toBeVisible({ timeout: 5000 });

    // Navigate to admin panel
    await page.goto('/dashboard/admin');
    await expect(page.locator('h1')).toBeVisible({ timeout: 5000 });

    // Logout
    await page.click('button:has-text("Sign Out")');
    await page.waitForURL('/', { timeout: 10000 });
  });

  test('should handle invalid credentials', async ({ page }) => {
    await page.goto('/auth/login');
    await page.waitForLoadState('networkidle');

    // Try invalid credentials
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'wrongpassword');
    await page.click('button[type="submit"]');

    // Should show error message
    await expect(page.locator('text="Invalid email or password"')).toBeVisible({ timeout: 5000 });

    // Should still be on login page
    await expect(page).toHaveURL(/\/auth\/login/);
  });
});
