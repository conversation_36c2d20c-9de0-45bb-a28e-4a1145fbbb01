import { test, expect } from '@playwright/test';
import { TestDataSeeder, cleanupAllTestData } from '../utils/test-data-seeder';
import { createAuthHelper } from '../utils/auth-helper';

test.describe('Accessibility Tests', () => {
  let seeder: TestDataSeeder;

  test.beforeAll(async () => {
    seeder = TestDataSeeder.getInstance();
  });

  test.afterAll(async () => {
    await cleanupAllTestData();
    await seeder.disconnect();
  });

  test.beforeEach(async ({ page }) => {
    // Create test user and login
    const testUser = await seeder.seedTestUser();
    const auth = createAuthHelper(page);

    try {
      await auth.loginUser(testUser);
    } catch (error) {
      // If login fails, just navigate to home page for accessibility tests
      await page.goto('/');
      await page.waitForLoadState('networkidle');
    }
  });

  test('should have proper heading hierarchy', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Check for h1 element
    const h1Elements = page.locator('h1');
    await expect(h1Elements).toHaveCount(1);

    // Check heading hierarchy (h1 -> h2 -> h3, etc.)
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').allTextContents();
    expect(headings.length).toBeGreaterThan(0);

    // First heading should be h1
    const firstHeading = page.locator('h1, h2, h3, h4, h5, h6').first();
    const tagName = await firstHeading.evaluate(el => el.tagName.toLowerCase());
    expect(tagName).toBe('h1');
  });

  test('should have proper form labels', async ({ page }) => {
    // Test login form
    await page.goto('/auth/login');

    // All form inputs should have labels
    const inputs = page.locator('input[type="email"], input[type="password"], input[type="text"]');
    const inputCount = await inputs.count();

    for (let i = 0; i < inputCount; i++) {
      const input = inputs.nth(i);
      const inputId = await input.getAttribute('id');

      if (inputId) {
        // Should have corresponding label
        const label = page.locator(`label[for="${inputId}"]`);
        await expect(label).toBeVisible();
      }
    }
  });

  test('should have proper button accessibility', async ({ page }) => {
    await page.goto('/security-quizzes');

    // All buttons should have accessible text
    const buttons = page.locator('button');
    const buttonCount = await buttons.count();

    for (let i = 0; i < Math.min(buttonCount, 10); i++) {
      const button = buttons.nth(i);
      const text = await button.textContent();
      const ariaLabel = await button.getAttribute('aria-label');
      const title = await button.getAttribute('title');

      // Button should have text, aria-label, or title
      expect(text || ariaLabel || title).toBeTruthy();
    }
  });

  test('should have proper link accessibility', async ({ page }) => {
    await page.goto('/security-quizzes');

    // All links should have accessible text
    const links = page.locator('a');
    const linkCount = await links.count();

    for (let i = 0; i < Math.min(linkCount, 10); i++) {
      const link = links.nth(i);
      const text = await link.textContent();
      const ariaLabel = await link.getAttribute('aria-label');
      const title = await link.getAttribute('title');

      // Link should have text, aria-label, or title
      expect(text?.trim() || ariaLabel || title).toBeTruthy();
    }
  });

  test('should have proper image alt text', async ({ page }) => {
    await page.goto('/security-quizzes');

    // All images should have alt text
    const images = page.locator('img');
    const imageCount = await images.count();

    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i);
      const alt = await img.getAttribute('alt');
      const ariaLabel = await img.getAttribute('aria-label');

      // Image should have alt text or aria-label
      expect(alt !== null || ariaLabel !== null).toBe(true);
    }
  });

  test('should support keyboard navigation', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Test tab navigation
    await page.keyboard.press('Tab');

    // Should focus on first focusable element
    const focusedElement = page.locator(':focus');
    await expect(focusedElement).toBeVisible();

    // Test multiple tab presses
    for (let i = 0; i < 5; i++) {
      await page.keyboard.press('Tab');
      const currentFocus = page.locator(':focus');
      await expect(currentFocus).toBeVisible();
    }
  });

  test('should have proper focus indicators', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Tab to first interactive element
    await page.keyboard.press('Tab');

    // Check if focused element has visible focus indicator
    const focusedElement = page.locator(':focus');
    const styles = await focusedElement.evaluate(el => {
      const computed = window.getComputedStyle(el);
      return {
        outline: computed.outline,
        outlineWidth: computed.outlineWidth,
        boxShadow: computed.boxShadow
      };
    });

    // Should have some form of focus indicator
    const hasFocusIndicator = styles.outline !== 'none' ||
                             styles.outlineWidth !== '0px' ||
                             styles.boxShadow !== 'none';
    expect(hasFocusIndicator).toBe(true);
  });

  test('should have proper color contrast', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Check text elements for color contrast
    const textElements = page.locator('p, h1, h2, h3, h4, h5, h6, span, div').first();

    const colors = await textElements.evaluate(el => {
      const computed = window.getComputedStyle(el);
      return {
        color: computed.color,
        backgroundColor: computed.backgroundColor
      };
    });

    // Basic check - should have defined colors
    expect(colors.color).toBeTruthy();
    expect(colors.backgroundColor).toBeTruthy();
  });

  test('should have proper ARIA attributes', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Check for proper ARIA usage
    const ariaElements = page.locator('[aria-label], [aria-describedby], [aria-expanded], [role]');
    const ariaCount = await ariaElements.count();

    if (ariaCount > 0) {
      // Check first few ARIA elements
      for (let i = 0; i < Math.min(ariaCount, 5); i++) {
        const element = ariaElements.nth(i);
        const ariaLabel = await element.getAttribute('aria-label');
        const role = await element.getAttribute('role');

        // ARIA attributes should have meaningful values
        if (ariaLabel) {
          expect(ariaLabel.trim()).toBeTruthy();
        }
        if (role) {
          expect(role.trim()).toBeTruthy();
        }
      }
    }
  });

  test('should support screen reader navigation', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Check for landmark elements
    const landmarks = page.locator('main, nav, header, footer, aside, section[aria-label]');
    const landmarkCount = await landmarks.count();

    // Should have at least main content area
    expect(landmarkCount).toBeGreaterThan(0);

    // Check for skip links
    const skipLinks = page.locator('a[href="#main"], a[href="#content"], .skip-link');
    if (await skipLinks.count() > 0) {
      await expect(skipLinks.first()).toBeVisible();
    }
  });

  test('should handle high contrast mode', async ({ page }) => {
    // Simulate high contrast mode
    await page.addStyleTag({
      content: `
        @media (prefers-contrast: high) {
          * {
            background: white !important;
            color: black !important;
          }
        }
      `
    });

    await page.goto('/security-quizzes');

    // Should still be functional and readable
    await expect(page.locator('h1')).toBeVisible();
    const quizElements = page.locator('.quiz-card, .quiz-item');
    const count = await quizElements.count();
    expect(count).toBeGreaterThan(0);
  });

  test('should support reduced motion preferences', async ({ page }) => {
    // Simulate reduced motion preference
    await page.addStyleTag({
      content: `
        @media (prefers-reduced-motion: reduce) {
          * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }
        }
      `
    });

    await page.goto('/security-quizzes');

    // Should still function without animations
    await expect(page.locator('h1')).toBeVisible();
    const quizElements2 = page.locator('.quiz-card, .quiz-item');
    const count2 = await quizElements2.count();
    expect(count2).toBeGreaterThan(0);
  });

  test('should have proper form validation messages', async ({ page }) => {
    await page.goto('/auth/login');

    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Check for validation messages
    const validationMessages = page.locator('[aria-invalid="true"], .error-message, .validation-error');
    if (await validationMessages.count() > 0) {
      // Validation messages should be accessible
      const firstMessage = validationMessages.first();
      const text = await firstMessage.textContent();
      expect(text?.trim()).toBeTruthy();
    }
  });

  test('should support zoom up to 200%', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Simulate 200% zoom
    await page.setViewportSize({ width: 640, height: 360 }); // Half size = 200% zoom effect

    // Should still be usable
    await expect(page.locator('h1')).toBeVisible();

    // Navigation should still work
    const firstQuiz = page.locator('.quiz-card, .quiz-item').first();
    if (await firstQuiz.isVisible()) {
      await expect(firstQuiz).toBeVisible();
    }
  });

  test('should have proper table accessibility', async ({ page }) => {
    // Navigate to a page that might have tables (analytics)
    await page.goto('/dashboard/analytics');

    const tables = page.locator('table');
    const tableCount = await tables.count();

    if (tableCount > 0) {
      const firstTable = tables.first();

      // Should have table headers
      const headers = firstTable.locator('th');
      if (await headers.count() > 0) {
        await expect(headers.first()).toBeVisible();
      }

      // Should have proper table structure
      const caption = firstTable.locator('caption');
      const summary = await firstTable.getAttribute('summary');

      // Table should have caption or summary for accessibility
      if (await caption.count() > 0 || summary) {
        expect(true).toBe(true); // Has accessibility feature
      }
    }
  });

  test('should handle error states accessibly', async ({ page }) => {
    // Try to access a non-existent page
    await page.goto('/nonexistent-page');

    // Should show accessible error message
    const errorMessage = page.locator('h1, .error-message, .not-found');
    if (await errorMessage.count() > 0) {
      const text = await errorMessage.first().textContent();
      expect(text?.trim()).toBeTruthy();
    }
  });
});
