import { test, expect, devices } from '@playwright/test';

test.describe('Mobile Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 }); // iPhone SE size
  });

  test('should display mobile-friendly login', async ({ page }) => {
    await page.goto('/auth/login');
    
    // Should show login form
    await expect(page.locator('form')).toBeVisible();
    
    // Form should be properly sized for mobile
    const form = page.locator('form');
    const formBox = await form.boundingBox();
    
    if (formBox) {
      // Form should not exceed viewport width
      expect(formBox.width).toBeLessThanOrEqual(375);
    }
    
    // Input fields should be touch-friendly
    const inputs = page.locator('input');
    const inputCount = await inputs.count();
    
    for (let i = 0; i < inputCount; i++) {
      const input = inputs.nth(i);
      const inputBox = await input.boundingBox();
      
      if (inputBox) {
        // Touch targets should be at least 44px high
        expect(inputBox.height).toBeGreaterThanOrEqual(40);
      }
    }
  });

  test('should have mobile navigation', async ({ page }) => {
    // Login first
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
    
    // Look for mobile menu button
    const mobileMenuBtn = page.locator('.mobile-menu, .hamburger, button[aria-label*="menu"], .menu-toggle');
    if (await mobileMenuBtn.isVisible()) {
      await mobileMenuBtn.click();
      
      // Should show navigation menu
      await expect(page.locator('nav, .navigation, .mobile-nav')).toBeVisible();
    }
  });

  test('should display quiz cards responsively', async ({ page }) => {
    // Login and navigate to quizzes
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
    
    await page.goto('/security-quizzes');
    
    // Should show quiz cards
    const quizCards = page.locator('.quiz-card, .quiz-item');
    await expect(quizCards).toHaveCount.greaterThan(0);
    
    // Cards should stack vertically on mobile
    const firstCard = quizCards.first();
    const secondCard = quizCards.nth(1);
    
    if (await secondCard.isVisible()) {
      const firstBox = await firstCard.boundingBox();
      const secondBox = await secondCard.boundingBox();
      
      if (firstBox && secondBox) {
        // Second card should be below first card (stacked)
        expect(secondBox.y).toBeGreaterThan(firstBox.y + firstBox.height - 10);
      }
    }
  });

  test('should handle touch interactions', async ({ page }) => {
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
    
    await page.goto('/security-quizzes');
    
    // Test touch tap on quiz card
    const firstQuiz = page.locator('.quiz-card, .quiz-item').first();
    if (await firstQuiz.isVisible()) {
      // Simulate touch tap
      await firstQuiz.tap();
      
      // Should navigate to quiz details or start quiz
      await page.waitForTimeout(1000);
      
      // URL should change or modal should appear
      const currentUrl = page.url();
      const modal = page.locator('.modal, .dialog');
      
      expect(currentUrl.includes('/security-quizzes') || await modal.isVisible()).toBe(true);
    }
  });

  test('should support swipe gestures', async ({ page }) => {
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
    
    await page.goto('/security-quizzes');
    
    // Look for swipeable content (carousels, etc.)
    const swipeableContent = page.locator('.carousel, .swiper, .slider');
    if (await swipeableContent.isVisible()) {
      const contentBox = await swipeableContent.boundingBox();
      
      if (contentBox) {
        // Simulate swipe gesture
        await page.mouse.move(contentBox.x + contentBox.width * 0.8, contentBox.y + contentBox.height / 2);
        await page.mouse.down();
        await page.mouse.move(contentBox.x + contentBox.width * 0.2, contentBox.y + contentBox.height / 2);
        await page.mouse.up();
        
        // Content should respond to swipe
        await page.waitForTimeout(500);
        await expect(swipeableContent).toBeVisible();
      }
    }
  });

  test('should optimize for different mobile sizes', async ({ page }) => {
    const mobileSizes = [
      { width: 320, height: 568, name: 'iPhone 5' },
      { width: 375, height: 667, name: 'iPhone SE' },
      { width: 414, height: 896, name: 'iPhone 11' },
      { width: 360, height: 640, name: 'Android' }
    ];
    
    for (const size of mobileSizes) {
      await page.setViewportSize({ width: size.width, height: size.height });
      await page.goto('/security-quizzes');
      
      // Should display content properly
      await expect(page.locator('h1')).toBeVisible();
      
      // Content should not overflow
      const body = page.locator('body');
      const bodyBox = await body.boundingBox();
      
      if (bodyBox) {
        expect(bodyBox.width).toBeLessThanOrEqual(size.width + 20); // Allow small margin
      }
    }
  });

  test('should handle mobile form inputs', async ({ page }) => {
    await page.goto('/auth/register');
    
    // Test mobile keyboard types
    const emailInput = page.locator('input[type="email"], input[id="email"]');
    if (await emailInput.isVisible()) {
      const inputType = await emailInput.getAttribute('type');
      const inputMode = await emailInput.getAttribute('inputmode');
      
      // Should use appropriate input type for mobile keyboards
      expect(inputType === 'email' || inputMode === 'email').toBe(true);
    }
    
    // Test form validation on mobile
    await page.click('button[type="submit"]');
    
    // Should show validation messages
    const validationMessages = page.locator('.error-message, [aria-invalid="true"]');
    if (await validationMessages.count() > 0) {
      await expect(validationMessages.first()).toBeVisible();
    }
  });

  test('should support mobile accessibility', async ({ page }) => {
    await page.goto('/auth/login');
    
    // Test touch target sizes
    const buttons = page.locator('button');
    const buttonCount = await buttons.count();
    
    for (let i = 0; i < Math.min(buttonCount, 5); i++) {
      const button = buttons.nth(i);
      const buttonBox = await button.boundingBox();
      
      if (buttonBox) {
        // Touch targets should be at least 44x44px
        expect(buttonBox.height).toBeGreaterThanOrEqual(40);
        expect(buttonBox.width).toBeGreaterThanOrEqual(40);
      }
    }
  });

  test('should handle mobile quiz taking', async ({ page }) => {
    // Login and start a quiz
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
    
    await page.goto('/security-quizzes');
    
    const firstQuiz = page.locator('.quiz-card, .quiz-item').first();
    if (await firstQuiz.isVisible()) {
      await firstQuiz.click();
      
      const startBtn = page.locator('button:has-text("Start Quiz"), button:has-text("Begin")');
      if (await startBtn.isVisible()) {
        await startBtn.click();
        
        // Should show mobile-friendly quiz interface
        await expect(page.locator('.question-container, .question')).toBeVisible();
        
        // Answer options should be touch-friendly
        const options = page.locator('input[type="radio"], .option');
        if (await options.count() > 0) {
          const optionBox = await options.first().boundingBox();
          
          if (optionBox) {
            expect(optionBox.height).toBeGreaterThanOrEqual(40);
          }
        }
      }
    }
  });

  test('should handle mobile orientation changes', async ({ page }) => {
    // Start in portrait
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/security-quizzes');
    
    await expect(page.locator('h1')).toBeVisible();
    
    // Switch to landscape
    await page.setViewportSize({ width: 667, height: 375 });
    await page.waitForTimeout(500);
    
    // Should still display content properly
    await expect(page.locator('h1')).toBeVisible();
    
    // Content should adapt to landscape
    const quizCards = page.locator('.quiz-card, .quiz-item');
    if (await quizCards.count() > 1) {
      const firstCard = quizCards.first();
      const secondCard = quizCards.nth(1);
      
      const firstBox = await firstCard.boundingBox();
      const secondBox = await secondCard.boundingBox();
      
      if (firstBox && secondBox) {
        // In landscape, cards might be side by side
        const isSideBySide = Math.abs(firstBox.y - secondBox.y) < 50;
        const isStacked = secondBox.y > firstBox.y + firstBox.height - 50;
        
        expect(isSideBySide || isStacked).toBe(true);
      }
    }
  });

  test('should optimize mobile performance', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/security-quizzes');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Mobile should load within 5 seconds
    expect(loadTime).toBeLessThan(5000);
    
    // Should show content
    await expect(page.locator('.quiz-card, .quiz-item')).toHaveCount.greaterThan(0);
  });

  test('should handle mobile search', async ({ page }) => {
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
    
    await page.goto('/security-quizzes');
    
    // Look for search functionality
    const searchInput = page.locator('input[type="search"], input[placeholder*="search"]');
    if (await searchInput.isVisible()) {
      // Should be properly sized for mobile
      const searchBox = await searchInput.boundingBox();
      
      if (searchBox) {
        expect(searchBox.height).toBeGreaterThanOrEqual(40);
      }
      
      // Test search functionality
      await searchInput.fill('security');
      await searchInput.press('Enter');
      
      await page.waitForTimeout(1000);
      
      // Should show search results
      const results = page.locator('.quiz-card, .quiz-item, .no-results');
      await expect(results).toHaveCount.greaterThan(0);
    }
  });

  test('should support mobile gestures in quiz', async ({ page }) => {
    // Login and start a quiz
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
    
    await page.goto('/security-quizzes');
    
    const firstQuiz = page.locator('.quiz-card, .quiz-item').first();
    if (await firstQuiz.isVisible()) {
      await firstQuiz.click();
      
      const startBtn = page.locator('button:has-text("Start Quiz")');
      if (await startBtn.isVisible()) {
        await startBtn.click();
        
        // Test swipe to next question (if supported)
        const questionContainer = page.locator('.question-container, .quiz-content');
        if (await questionContainer.isVisible()) {
          const containerBox = await questionContainer.boundingBox();
          
          if (containerBox) {
            // Simulate swipe left gesture
            await page.mouse.move(containerBox.x + containerBox.width * 0.8, containerBox.y + containerBox.height / 2);
            await page.mouse.down();
            await page.mouse.move(containerBox.x + containerBox.width * 0.2, containerBox.y + containerBox.height / 2);
            await page.mouse.up();
            
            await page.waitForTimeout(500);
            
            // Should still show quiz content
            await expect(questionContainer).toBeVisible();
          }
        }
      }
    }
  });
});
