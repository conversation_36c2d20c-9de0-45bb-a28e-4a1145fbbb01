import { test, expect } from '@playwright/test';

test.describe('Quiz Taking Experience', () => {
  test.beforeEach(async ({ page }) => {
    // Login as regular user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
  });

  test('should display available quizzes', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Should show quiz cards
    const quizCards = page.locator('.quiz-card');
    const count = await quizCards.count();
    expect(count).toBeGreaterThan(0);

    // Each quiz card should have essential information
    const firstQuiz = page.locator('.quiz-card').first();
    await expect(firstQuiz.locator('h3')).toBeVisible(); // Title
    await expect(firstQuiz.locator('text=questions')).toBeVisible(); // Question count
    await expect(firstQuiz.locator('text=Beginner|Intermediate|Advanced')).toBeVisible(); // Difficulty
  });

  test('should start a quiz', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Click on first quiz
    await page.click('.quiz-card:first-child');

    // Should show quiz details page
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('button:has-text("Start Quiz")')).toBeVisible();

    // Start the quiz
    await page.click('button:has-text("Start Quiz")');

    // Should navigate to quiz taking interface
    await page.waitForURL(/\/security-quizzes\/.*\/take/);
    await expect(page.locator('.question-container')).toBeVisible();
  });

  test('should answer multiple choice questions', async ({ page }) => {
    // Navigate to a quiz with multiple choice questions
    await page.goto('/security-quizzes');
    await page.click('.quiz-card:first-child');
    await page.click('button:has-text("Start Quiz")');

    await page.waitForURL(/\/security-quizzes\/.*\/take/);

    // Answer first question
    await page.click('input[type="radio"]:first-child');

    // Should enable next button
    await expect(page.locator('button:has-text("Next")')).toBeEnabled();

    // Go to next question
    await page.click('button:has-text("Next")');

    // Should show next question or results
    await expect(page.locator('.question-container, .results-container')).toBeVisible();
  });

  test('should answer true/false questions', async ({ page }) => {
    // Find a quiz with true/false questions
    await page.goto('/security-quizzes');

    // Look for a quiz that might have true/false questions
    await page.click('.quiz-card:first-child');
    await page.click('button:has-text("Start Quiz")');

    await page.waitForURL(/\/security-quizzes\/.*\/take/);

    // If this is a true/false question
    const trueFalseOptions = page.locator('input[value="true"], input[value="false"]');
    if (await trueFalseOptions.count() > 0) {
      await page.click('input[value="true"]');
      await expect(page.locator('button:has-text("Next")')).toBeEnabled();
    }
  });

  test('should show progress indicator', async ({ page }) => {
    await page.goto('/security-quizzes');
    await page.click('.quiz-card:first-child');
    await page.click('button:has-text("Start Quiz")');

    await page.waitForURL(/\/security-quizzes\/.*\/take/);

    // Should show progress bar or indicator
    await expect(page.locator('.progress-bar, .question-counter')).toBeVisible();

    // Should show current question number
    await expect(page.locator('text=Question 1')).toBeVisible();
  });

  test('should allow navigation between questions', async ({ page }) => {
    await page.goto('/security-quizzes');
    await page.click('.quiz-card:first-child');
    await page.click('button:has-text("Start Quiz")');

    await page.waitForURL(/\/security-quizzes\/.*\/take/);

    // Answer first question
    await page.click('input[type="radio"]:first-child');
    await page.click('button:has-text("Next")');

    // Should be able to go back
    if (await page.locator('button:has-text("Previous")').isVisible()) {
      await page.click('button:has-text("Previous")');
      await expect(page.locator('text=Question 1')).toBeVisible();
    }
  });

  test('should show hints when available', async ({ page }) => {
    await page.goto('/security-quizzes');
    await page.click('.quiz-card:first-child');
    await page.click('button:has-text("Start Quiz")');

    await page.waitForURL(/\/security-quizzes\/.*\/take/);

    // Look for hint button
    const hintButton = page.locator('button:has-text("Show Hint")');
    if (await hintButton.isVisible()) {
      await hintButton.click();
      await expect(page.locator('.hint-content')).toBeVisible();
    }
  });

  test('should submit quiz and show results', async ({ page }) => {
    await page.goto('/security-quizzes');
    await page.click('.quiz-card:first-child');
    await page.click('button:has-text("Start Quiz")');

    await page.waitForURL(/\/security-quizzes\/.*\/take/);

    // Answer all questions (simplified - just click first option for each)
    let questionCount = 0;
    while (questionCount < 10) { // Limit to prevent infinite loop
      // Answer current question
      const radioButtons = page.locator('input[type="radio"]');
      if (await radioButtons.count() > 0) {
        await radioButtons.first().click();
      }

      // Check if this is the last question
      const submitButton = page.locator('button:has-text("Submit Quiz")');
      if (await submitButton.isVisible()) {
        await submitButton.click();
        break;
      }

      // Go to next question
      const nextButton = page.locator('button:has-text("Next")');
      if (await nextButton.isVisible()) {
        await nextButton.click();
        questionCount++;
      } else {
        break;
      }
    }

    // Should show results page
    await expect(page.locator('.results-container, h1:has-text("Results")')).toBeVisible();
    await expect(page.locator('text=Score:')).toBeVisible();
  });

  test('should save quiz progress', async ({ page }) => {
    await page.goto('/security-quizzes');
    await page.click('.quiz-card:first-child');
    await page.click('button:has-text("Start Quiz")');

    await page.waitForURL(/\/security-quizzes\/.*\/take/);

    // Answer first question
    await page.click('input[type="radio"]:first-child');

    // Refresh page to simulate interruption
    await page.reload();

    // Should restore progress or ask to continue
    await expect(page.locator('text=Continue Quiz, text=Resume')).toBeVisible();
  });

  test('should handle quiz timer if present', async ({ page }) => {
    await page.goto('/security-quizzes');
    await page.click('.quiz-card:first-child');
    await page.click('button:has-text("Start Quiz")');

    await page.waitForURL(/\/security-quizzes\/.*\/take/);

    // Look for timer
    const timer = page.locator('.timer, .countdown');
    if (await timer.isVisible()) {
      // Timer should be counting down
      const initialTime = await timer.textContent();
      await page.waitForTimeout(2000);
      const laterTime = await timer.textContent();
      expect(initialTime).not.toBe(laterTime);
    }
  });

  test('should show explanations after answering', async ({ page }) => {
    await page.goto('/security-quizzes');
    await page.click('.quiz-card:first-child');
    await page.click('button:has-text("Start Quiz")');

    await page.waitForURL(/\/security-quizzes\/.*\/take/);

    // Answer question
    await page.click('input[type="radio"]:first-child');

    // Look for show answer button
    const showAnswerButton = page.locator('button:has-text("Show Answer")');
    if (await showAnswerButton.isVisible()) {
      await showAnswerButton.click();
      await expect(page.locator('.explanation, .feedback')).toBeVisible();
    }
  });

  test('should handle different question types in same quiz', async ({ page }) => {
    await page.goto('/security-quizzes');
    await page.click('.quiz-card:first-child');
    await page.click('button:has-text("Start Quiz")');

    await page.waitForURL(/\/security-quizzes\/.*\/take/);

    // Go through multiple questions to test different types
    for (let i = 0; i < 3; i++) {
      // Check question type and answer accordingly
      const multipleChoice = page.locator('input[type="radio"]');
      const trueFalse = page.locator('input[value="true"], input[value="false"]');
      const shortAnswer = page.locator('input[type="text"], textarea');

      if (await multipleChoice.count() > 0) {
        await multipleChoice.first().click();
      } else if (await trueFalse.count() > 0) {
        await trueFalse.first().click();
      } else if (await shortAnswer.count() > 0) {
        await shortAnswer.first().fill('Test answer');
      }

      // Try to go to next question
      const nextButton = page.locator('button:has-text("Next")');
      if (await nextButton.isVisible()) {
        await nextButton.click();
      } else {
        break;
      }
    }
  });

  test('should validate required answers', async ({ page }) => {
    await page.goto('/security-quizzes');
    await page.click('.quiz-card:first-child');
    await page.click('button:has-text("Start Quiz")');

    await page.waitForURL(/\/security-quizzes\/.*\/take/);

    // Try to proceed without answering
    const nextButton = page.locator('button:has-text("Next")');
    if (await nextButton.isVisible()) {
      await nextButton.click();

      // Should show validation message or prevent navigation
      await expect(page.locator('text=Please answer, text=required')).toBeVisible();
    }
  });
});
