import { test, expect } from '@playwright/test';

test.describe('Authentication', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing sessions
    await page.context().clearCookies();
  });

  test('should redirect unauthenticated users to login', async ({ page }) => {
    // Try to access protected dashboard
    await page.goto('/dashboard');

    // Should redirect to login page
    await page.waitForURL('/auth/login');
    expect(page.url()).toContain('/auth/login');
  });

  test('should allow admin login', async ({ page }) => {
    await page.goto('/auth/login');

    // Fill in admin credentials
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'admin123');

    // Submit form
    await page.click('button[type="submit"]');

    // Should redirect to dashboard
    await page.waitForURL('/dashboard');
    expect(page.url()).toContain('/dashboard');

    // Should show admin features
    await expect(page.locator('text=Create Quiz')).toBeVisible();
  });

  test('should allow user login', async ({ page }) => {
    await page.goto('/auth/login');

    // Fill in user credentials
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');

    // Submit form
    await page.click('button[type="submit"]');

    // Should redirect to dashboard
    await page.waitForURL('/dashboard');
    expect(page.url()).toContain('/dashboard');

    // Should NOT show admin features
    await expect(page.locator('text=Create Quiz')).not.toBeVisible();
  });

  test('should show error for invalid credentials', async ({ page }) => {
    await page.goto('/auth/login');

    // Fill in invalid credentials
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'wrongpassword');

    // Submit form
    await page.click('button[type="submit"]');

    // Should show error message
    await expect(page.locator('text=Invalid email or password')).toBeVisible();

    // Should stay on login page
    expect(page.url()).toContain('/auth/login');
  });

  test('should allow user registration', async ({ page }) => {
    await page.goto('/auth/register');

    // Fill in registration form
    await page.fill('input[id="name"]', 'Test User');
    await page.fill('input[id="email"]', `test${Date.now()}@example.com`);
    await page.fill('input[id="password"]', 'testpassword123');
    await page.fill('input[id="confirmPassword"]', 'testpassword123');

    // Submit form
    await page.click('button[type="submit"]');

    // Should redirect to dashboard or show success message
    await page.waitForURL('/dashboard');
    expect(page.url()).toContain('/dashboard');
  });

  test('should validate registration form', async ({ page }) => {
    await page.goto('/auth/register');

    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Should show validation errors
    await expect(page.locator('input[id="email"]:invalid')).toBeVisible();
    await expect(page.locator('input[id="password"]:invalid')).toBeVisible();
  });

  test('should validate password confirmation', async ({ page }) => {
    await page.goto('/auth/register');

    // Fill in mismatched passwords
    await page.fill('input[id="name"]', 'Test User');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'password123');
    await page.fill('input[id="confirmPassword"]', 'differentpassword');

    // Submit form
    await page.click('button[type="submit"]');

    // Should show password mismatch error
    await expect(page.locator('text=Passwords do not match')).toBeVisible();
  });

  test('should allow logout', async ({ page }) => {
    // Login first
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'admin123');
    await page.click('button[type="submit"]');

    await page.waitForURL('/dashboard');

    // Click logout button
    await page.click('button:has-text("Logout")');

    // Should redirect to home page
    await page.waitForURL('/');
    expect(page.url()).toBe('http://localhost:3000/');

    // Should not be able to access dashboard
    await page.goto('/dashboard');
    await page.waitForURL('/auth/login');
  });

  test('should persist session across page reloads', async ({ page }) => {
    // Login
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'admin123');
    await page.click('button[type="submit"]');

    await page.waitForURL('/dashboard');

    // Reload page
    await page.reload();

    // Should still be logged in
    expect(page.url()).toContain('/dashboard');
    await expect(page.locator('text=Create Quiz')).toBeVisible();
  });

  test('should handle session expiration', async ({ page }) => {
    // Login
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'admin123');
    await page.click('button[type="submit"]');

    await page.waitForURL('/dashboard');

    // Simulate session expiration by clearing cookies
    await page.context().clearCookies();

    // Try to access protected resource
    await page.goto('/dashboard/quizzes/create');

    // Should redirect to login
    await page.waitForURL('/auth/login');
  });

  test('should restrict admin features to admin users', async ({ page }) => {
    // Login as regular user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');

    await page.waitForURL('/dashboard');

    // Try to access quiz creation (admin only)
    await page.goto('/dashboard/quizzes/create');

    // Should be redirected or show access denied
    await expect(page.locator('text=Access denied')).toBeVisible();
  });
});
