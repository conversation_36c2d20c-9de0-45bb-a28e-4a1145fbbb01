import { test, expect } from '@playwright/test';

test.describe('<PERSON>rro<PERSON>', () => {
  test('should handle 404 errors gracefully', async ({ page }) => {
    // Navigate to non-existent page
    await page.goto('/nonexistent-page');
    
    // Should show 404 page
    const notFoundElements = page.locator('h1:has-text("404"), h1:has-text("Not Found"), .not-found');
    await expect(notFoundElements.first()).toBeVisible();
    
    // Should have navigation back to home
    const homeLink = page.locator('a[href="/"], a:has-text("Home"), a:has-text("Back")');
    if (await homeLink.isVisible()) {
      await expect(homeLink).toBeVisible();
    }
  });

  test('should handle network errors', async ({ page }) => {
    // Simulate network failure
    await page.route('**/api/**', route => route.abort());
    
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    
    // Should show error message
    const errorMessage = page.locator('.error-message, .alert-error, text=error, text=failed');
    if (await errorMessage.count() > 0) {
      await expect(errorMessage.first()).toBeVisible();
    }
  });

  test('should handle server errors (500)', async ({ page }) => {
    // Simulate server error
    await page.route('**/api/**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal Server Error' })
      });
    });
    
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    
    // Should handle server error gracefully
    const errorIndicator = page.locator('.error, .alert, text=error, text=something went wrong');
    if (await errorIndicator.count() > 0) {
      await expect(errorIndicator.first()).toBeVisible();
    }
  });

  test('should handle authentication errors', async ({ page }) => {
    await page.goto('/auth/login');
    
    // Try invalid credentials
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'wrongpassword');
    await page.click('button[type="submit"]');
    
    // Should show authentication error
    await expect(page.locator('text=Invalid email or password')).toBeVisible();
    
    // Should stay on login page
    expect(page.url()).toContain('/auth/login');
  });

  test('should handle session expiration', async ({ page }) => {
    // Login first
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
    
    // Clear session cookies to simulate expiration
    await page.context().clearCookies();
    
    // Try to access protected resource
    await page.goto('/dashboard/quizzes');
    
    // Should redirect to login
    await page.waitForURL('/auth/login');
    expect(page.url()).toContain('/auth/login');
  });

  test('should handle form validation errors', async ({ page }) => {
    await page.goto('/auth/register');
    
    // Submit empty form
    await page.click('button[type="submit"]');
    
    // Should show validation errors
    const validationErrors = page.locator('.error-message, [aria-invalid="true"], .field-error');
    if (await validationErrors.count() > 0) {
      await expect(validationErrors.first()).toBeVisible();
    }
    
    // Test password mismatch
    await page.fill('input[id="name"]', 'Test User');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'password123');
    await page.fill('input[id="confirmPassword"]', 'differentpassword');
    await page.click('button[type="submit"]');
    
    // Should show password mismatch error
    const passwordError = page.locator('text=Passwords do not match, text=password, .password-error');
    if (await passwordError.count() > 0) {
      await expect(passwordError.first()).toBeVisible();
    }
  });

  test('should handle quiz loading errors', async ({ page }) => {
    // Login first
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
    
    // Simulate quiz loading error
    await page.route('**/api/quizzes/**', route => route.abort());
    
    await page.goto('/security-quizzes');
    
    // Should show error state
    const errorState = page.locator('.error-state, .loading-error, text=failed to load, text=error loading');
    if (await errorState.count() > 0) {
      await expect(errorState.first()).toBeVisible();
    }
  });

  test('should handle file upload errors', async ({ page }) => {
    // Login as admin
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
    
    // Navigate to quiz creation
    await page.goto('/dashboard/quizzes/create');
    
    // Look for file upload input
    const fileInput = page.locator('input[type="file"]');
    if (await fileInput.isVisible()) {
      // Simulate upload error
      await page.route('**/api/upload/**', route => {
        route.fulfill({
          status: 413,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'File too large' })
        });
      });
      
      // Try to upload a file
      await fileInput.setInputFiles({
        name: 'test.txt',
        mimeType: 'text/plain',
        buffer: Buffer.from('test content')
      });
      
      // Should show upload error
      const uploadError = page.locator('.upload-error, .file-error, text=file too large, text=upload failed');
      if (await uploadError.count() > 0) {
        await expect(uploadError.first()).toBeVisible();
      }
    }
  });

  test('should handle database connection errors', async ({ page }) => {
    // Simulate database error
    await page.route('**/api/**', route => {
      route.fulfill({
        status: 503,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Service Unavailable' })
      });
    });
    
    await page.goto('/security-quizzes');
    
    // Should show service unavailable message
    const serviceError = page.locator('.service-error, text=service unavailable, text=temporarily unavailable');
    if (await serviceError.count() > 0) {
      await expect(serviceError.first()).toBeVisible();
    }
  });

  test('should handle permission errors', async ({ page }) => {
    // Login as regular user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
    
    // Try to access admin area
    await page.goto('/dashboard/admin');
    
    // Should show access denied or redirect
    const accessDenied = page.locator('text=Access denied, text=Unauthorized, text=Permission denied');
    const isRedirected = !page.url().includes('/admin');
    
    expect(await accessDenied.count() > 0 || isRedirected).toBe(true);
  });

  test('should handle timeout errors', async ({ page }) => {
    // Simulate slow response
    await page.route('**/api/**', async route => {
      await new Promise(resolve => setTimeout(resolve, 10000)); // 10 second delay
      route.continue();
    });
    
    // Set shorter timeout for this test
    page.setDefaultTimeout(5000);
    
    try {
      await page.goto('/auth/login');
      await page.fill('input[id="email"]', '<EMAIL>');
      await page.fill('input[id="password"]', 'user123');
      await page.click('button[type="submit"]');
      
      // Should timeout and show error
      await page.waitForSelector('.timeout-error, .loading-error', { timeout: 6000 });
    } catch (error) {
      // Timeout is expected behavior
      expect(error.message).toContain('timeout');
    }
  });

  test('should handle JavaScript errors gracefully', async ({ page }) => {
    // Listen for console errors
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Listen for page errors
    const pageErrors: string[] = [];
    page.on('pageerror', error => {
      pageErrors.push(error.message);
    });
    
    await page.goto('/security-quizzes');
    
    // Inject a JavaScript error
    await page.evaluate(() => {
      // This should cause an error but not break the page
      try {
        (window as any).nonExistentFunction();
      } catch (e) {
        console.error('Test error:', e);
      }
    });
    
    // Page should still be functional
    await expect(page.locator('h1')).toBeVisible();
    
    // Should have caught the error
    expect(consoleErrors.length).toBeGreaterThan(0);
  });

  test('should provide error recovery options', async ({ page }) => {
    // Simulate network error
    await page.route('**/api/quizzes', route => route.abort());
    
    await page.goto('/security-quizzes');
    
    // Look for retry button
    const retryButton = page.locator('button:has-text("Retry"), button:has-text("Try Again"), .retry-button');
    if (await retryButton.isVisible()) {
      // Remove network error simulation
      await page.unroute('**/api/quizzes');
      
      // Click retry
      await retryButton.click();
      
      // Should recover and show content
      await page.waitForTimeout(2000);
      const quizCards = page.locator('.quiz-card, .quiz-item');
      if (await quizCards.count() > 0) {
        await expect(quizCards.first()).toBeVisible();
      }
    }
  });

  test('should handle malformed data gracefully', async ({ page }) => {
    // Simulate malformed API response
    await page.route('**/api/quizzes', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: '{"invalid": json malformed'
      });
    });
    
    await page.goto('/security-quizzes');
    
    // Should handle malformed data without crashing
    const errorState = page.locator('.error-state, .data-error, text=error loading data');
    if (await errorState.count() > 0) {
      await expect(errorState.first()).toBeVisible();
    } else {
      // Should at least show the page structure
      await expect(page.locator('h1')).toBeVisible();
    }
  });

  test('should handle concurrent request errors', async ({ page }) => {
    // Login first
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
    
    // Simulate errors for multiple concurrent requests
    let requestCount = 0;
    await page.route('**/api/**', route => {
      requestCount++;
      if (requestCount % 2 === 0) {
        route.abort();
      } else {
        route.continue();
      }
    });
    
    // Navigate to page that makes multiple API calls
    await page.goto('/dashboard/analytics');
    
    // Should handle partial failures gracefully
    await page.waitForTimeout(3000);
    
    // Page should still be functional
    await expect(page.locator('h1')).toBeVisible();
  });

  test('should show user-friendly error messages', async ({ page }) => {
    await page.goto('/auth/login');
    
    // Test various error scenarios
    const errorScenarios = [
      { email: '', password: '', expectedError: 'required' },
      { email: 'invalid-email', password: 'password', expectedError: 'valid email' },
      { email: '<EMAIL>', password: '123', expectedError: 'password' }
    ];
    
    for (const scenario of errorScenarios) {
      await page.fill('input[id="email"]', scenario.email);
      await page.fill('input[id="password"]', scenario.password);
      await page.click('button[type="submit"]');
      
      // Should show user-friendly error message
      const errorMessage = page.locator('.error-message, .field-error, .validation-error');
      if (await errorMessage.count() > 0) {
        const errorText = await errorMessage.first().textContent();
        expect(errorText?.toLowerCase()).toContain(scenario.expectedError);
      }
      
      // Clear fields for next test
      await page.fill('input[id="email"]', '');
      await page.fill('input[id="password"]', '');
    }
  });
});
