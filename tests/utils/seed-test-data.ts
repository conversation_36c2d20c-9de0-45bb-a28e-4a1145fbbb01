/**
 * Test Data Seeding Utility
 * 
 * This utility helps seed test data for E2E tests including:
 * - Test users (admin and regular users)
 * - <PERSON><PERSON> quizzes with questions
 * - Categories and tags
 */

import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

export interface TestUser {
  email: string;
  password: string;
  name: string;
  role: 'admin' | 'user';
}

export interface TestQuiz {
  title: string;
  description: string;
  category?: string;
  difficulty?: string;
  timeLimit?: number;
  passingScore?: number;
  tags?: string[];
  questions?: TestQuestion[];
}

export interface TestQuestion {
  type: 'multiple_choice' | 'true_false' | 'short_answer' | 'essay' | 'matching' | 'fill_in_the_blank';
  text: string;
  points: number;
  options?: string[];
  correctAnswer?: any;
  explanation?: string;
  hint?: string;
}

export const TEST_USERS: TestUser[] = [
  {
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Test Admin',
    role: 'admin'
  },
  {
    email: '<EMAIL>',
    password: 'user123',
    name: 'Test User',
    role: 'user'
  },
  {
    email: '<EMAIL>',
    password: 'editor123',
    name: 'Test Editor',
    role: 'admin'
  }
];

export const TEST_QUIZZES: TestQuiz[] = [
  {
    title: 'Web Application Security Basics',
    description: 'Fundamental concepts in web application security',
    category: 'Web Application Security',
    difficulty: 'Beginner',
    timeLimit: 30,
    passingScore: 70,
    tags: ['web security', 'basics', 'OWASP'],
    questions: [
      {
        type: 'multiple_choice',
        text: 'What does XSS stand for?',
        points: 2,
        options: ['Cross-Site Scripting', 'eXtended Security System', 'XML Security Standard', 'eXternal Script Source'],
        correctAnswer: 0,
        explanation: 'XSS stands for Cross-Site Scripting, a common web vulnerability.',
        hint: 'Think about scripts running across different sites.'
      },
      {
        type: 'true_false',
        text: 'HTTPS encrypts all data transmission between client and server.',
        points: 1,
        correctAnswer: true,
        explanation: 'HTTPS provides end-to-end encryption for all data transmitted.',
        hint: 'Consider what the S in HTTPS stands for.'
      },
      {
        type: 'short_answer',
        text: 'What is the most common web application vulnerability according to OWASP Top 10?',
        points: 3,
        correctAnswer: ['Injection', 'SQL Injection', 'injection', 'sql injection'],
        explanation: 'Injection attacks, particularly SQL injection, are consistently ranked as the top web vulnerability.',
        hint: 'Think about malicious code being inserted into applications.'
      }
    ]
  },
  {
    title: 'Network Security Fundamentals',
    description: 'Basic concepts in network security and protocols',
    category: 'Network Security',
    difficulty: 'Intermediate',
    timeLimit: 45,
    passingScore: 75,
    tags: ['networking', 'protocols', 'security'],
    questions: [
      {
        type: 'multiple_choice',
        text: 'Which port is commonly used for HTTPS traffic?',
        points: 2,
        options: ['80', '443', '8080', '22'],
        correctAnswer: 1,
        explanation: 'Port 443 is the standard port for HTTPS traffic.',
        hint: 'It\'s not the same as HTTP port.'
      },
      {
        type: 'true_false',
        text: 'A firewall can prevent all types of cyber attacks.',
        points: 1,
        correctAnswer: false,
        explanation: 'Firewalls are important but cannot prevent all types of attacks, especially application-layer attacks.',
        hint: 'Consider the limitations of network-level protection.'
      }
    ]
  },
  {
    title: 'Cryptography Essentials',
    description: 'Introduction to cryptographic concepts and algorithms',
    category: 'Cryptography',
    difficulty: 'Advanced',
    timeLimit: 60,
    passingScore: 80,
    tags: ['cryptography', 'encryption', 'algorithms'],
    questions: [
      {
        type: 'multiple_choice',
        text: 'Which of the following is a symmetric encryption algorithm?',
        points: 3,
        options: ['RSA', 'AES', 'ECC', 'DSA'],
        correctAnswer: 1,
        explanation: 'AES (Advanced Encryption Standard) is a symmetric encryption algorithm.',
        hint: 'Symmetric means the same key is used for encryption and decryption.'
      },
      {
        type: 'short_answer',
        text: 'What does PKI stand for?',
        points: 2,
        correctAnswer: ['Public Key Infrastructure', 'public key infrastructure'],
        explanation: 'PKI stands for Public Key Infrastructure, a framework for managing digital certificates.',
        hint: 'Think about the infrastructure that manages public keys.'
      }
    ]
  }
];

export async function seedTestUsers(): Promise<void> {
  console.log('Seeding test users...');
  
  for (const user of TEST_USERS) {
    try {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: user.email }
      });
      
      if (existingUser) {
        console.log(`User ${user.email} already exists, skipping...`);
        continue;
      }
      
      // Hash password
      const hashedPassword = await bcrypt.hash(user.password, 12);
      
      // Create user
      await prisma.user.create({
        data: {
          email: user.email,
          password: hashedPassword,
          name: user.name,
          role: user.role
        }
      });
      
      console.log(`Created user: ${user.email}`);
    } catch (error) {
      console.error(`Error creating user ${user.email}:`, error);
    }
  }
}

export async function seedTestQuizzes(): Promise<void> {
  console.log('Seeding test quizzes...');
  
  // Get admin user to assign as creator
  const adminUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  });
  
  if (!adminUser) {
    console.error('Admin user not found. Please seed users first.');
    return;
  }
  
  for (const quiz of TEST_QUIZZES) {
    try {
      // Check if quiz already exists
      const existingQuiz = await prisma.quiz.findFirst({
        where: { title: quiz.title }
      });
      
      if (existingQuiz) {
        console.log(`Quiz "${quiz.title}" already exists, skipping...`);
        continue;
      }
      
      // Create quiz
      const createdQuiz = await prisma.quiz.create({
        data: {
          title: quiz.title,
          description: quiz.description,
          category: quiz.category || 'General',
          difficulty: quiz.difficulty || 'Beginner',
          timeLimit: quiz.timeLimit,
          passingScore: quiz.passingScore || 70,
          tags: quiz.tags?.join(', ') || '',
          createdById: adminUser.id,
          published: true
        }
      });
      
      // Create questions if provided
      if (quiz.questions) {
        for (let i = 0; i < quiz.questions.length; i++) {
          const question = quiz.questions[i];
          
          await prisma.question.create({
            data: {
              quizId: createdQuiz.id,
              type: question.type,
              text: question.text,
              points: question.points,
              options: question.options ? JSON.stringify(question.options) : null,
              correctAnswer: JSON.stringify(question.correctAnswer),
              explanation: question.explanation,
              hint: question.hint,
              order: i + 1
            }
          });
        }
      }
      
      console.log(`Created quiz: "${quiz.title}" with ${quiz.questions?.length || 0} questions`);
    } catch (error) {
      console.error(`Error creating quiz "${quiz.title}":`, error);
    }
  }
}

export async function cleanupTestData(): Promise<void> {
  console.log('Cleaning up test data...');
  
  try {
    // Delete test questions
    await prisma.question.deleteMany({
      where: {
        quiz: {
          createdBy: {
            email: {
              in: TEST_USERS.map(u => u.email)
            }
          }
        }
      }
    });
    
    // Delete test quizzes
    await prisma.quiz.deleteMany({
      where: {
        createdBy: {
          email: {
            in: TEST_USERS.map(u => u.email)
          }
        }
      }
    });
    
    // Delete test users
    await prisma.user.deleteMany({
      where: {
        email: {
          in: TEST_USERS.map(u => u.email)
        }
      }
    });
    
    console.log('Test data cleanup completed');
  } catch (error) {
    console.error('Error cleaning up test data:', error);
  }
}

export async function seedAllTestData(): Promise<void> {
  try {
    await seedTestUsers();
    await seedTestQuizzes();
    console.log('All test data seeded successfully!');
  } catch (error) {
    console.error('Error seeding test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// CLI usage
if (require.main === module) {
  const command = process.argv[2];
  
  switch (command) {
    case 'seed':
      seedAllTestData();
      break;
    case 'cleanup':
      cleanupTestData().then(() => prisma.$disconnect());
      break;
    case 'users':
      seedTestUsers().then(() => prisma.$disconnect());
      break;
    case 'quizzes':
      seedTestQuizzes().then(() => prisma.$disconnect());
      break;
    default:
      console.log('Usage: ts-node seed-test-data.ts [seed|cleanup|users|quizzes]');
      break;
  }
}
