import { PrismaClient } from '@/generated/prisma';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

export interface TestUser {
  id: string;
  email: string;
  password: string;
  role: 'USER' | 'ADMIN';
}

export interface TestQuiz {
  id: string;
  title: string;
  description: string;
  content: any;
  authorId: string;
}

export class TestDataSeeder {
  private static instance: TestDataSeeder;
  private seededData: {
    users: TestUser[];
    quizzes: TestQuiz[];
  } = {
    users: [],
    quizzes: []
  };

  static getInstance(): TestDataSeeder {
    if (!TestDataSeeder.instance) {
      TestDataSeeder.instance = new TestDataSeeder();
    }
    return TestDataSeeder.instance;
  }

  async seedTestUser(userData: Partial<TestUser> = {}): Promise<TestUser> {
    const defaultUser = {
      email: `test-${Date.now()}@example.com`,
      password: 'testpassword123',
      role: 'USER' as const,
      name: 'Test User'
    };

    const user = { ...defaultUser, ...userData };
    const hashedPassword = await bcrypt.hash(user.password, 12);

    const createdUser = await prisma.user.create({
      data: {
        email: user.email,
        password: hashedPassword,
        role: user.role,
        name: user.name
      }
    });

    const testUser: TestUser = {
      id: createdUser.id,
      email: user.email,
      password: user.password, // Store original password for testing
      role: user.role
    };

    this.seededData.users.push(testUser);
    return testUser;
  }

  async seedTestAdmin(): Promise<TestUser> {
    return this.seedTestUser({
      email: `admin-${Date.now()}@example.com`,
      role: 'ADMIN'
    });
  }

  async seedTestQuiz(quizData: Partial<TestQuiz> = {}, authorId?: string): Promise<TestQuiz> {
    if (!authorId) {
      const admin = await this.seedTestAdmin();
      authorId = admin.id;
    }

    const defaultQuiz = {
      title: `Test Quiz ${Date.now()}`,
      description: 'A test quiz for E2E testing',
      content: {
        quiz_id: `test-quiz-${Date.now()}`,
        title: { en: `Test Quiz ${Date.now()}` },
        description: { en: 'A test quiz for E2E testing' },
        version: '1.0.0',
        language: 'en',
        questions: [
          {
            question_id: 'q1',
            type: 'multiple_choice',
            question_text: { en: 'What is 2 + 2?' },
            options: [
              { id: 'a', text: { en: '3' }, is_correct: false },
              { id: 'b', text: { en: '4' }, is_correct: true },
              { id: 'c', text: { en: '5' }, is_correct: false }
            ],
            points: 1,
            explanation: { en: '2 + 2 equals 4' }
          }
        ],
        settings: {
          time_limit: null,
          shuffle_questions: false,
          shuffle_options: false,
          show_results: true,
          allow_review: true
        }
      }
    };

    const quiz = { ...defaultQuiz, ...quizData };

    const createdQuiz = await prisma.quiz.create({
      data: {
        title: quiz.title,
        description: quiz.description,
        content: quiz.content,
        authorId: authorId,
        isPublished: true
      }
    });

    const testQuiz: TestQuiz = {
      id: createdQuiz.id,
      title: quiz.title,
      description: quiz.description,
      content: quiz.content,
      authorId: authorId
    };

    this.seededData.quizzes.push(testQuiz);
    return testQuiz;
  }

  async seedMultipleQuizzes(count: number = 3, authorId?: string): Promise<TestQuiz[]> {
    const quizzes: TestQuiz[] = [];

    for (let i = 0; i < count; i++) {
      const quiz = await this.seedTestQuiz({
        title: `Test Quiz ${i + 1} - ${Date.now()}`,
        description: `Test quiz number ${i + 1} for E2E testing`
      }, authorId);
      quizzes.push(quiz);
    }

    return quizzes;
  }

  async cleanupSeededData(): Promise<void> {
    // Delete quizzes first (due to foreign key constraints)
    for (const quiz of this.seededData.quizzes) {
      await prisma.quiz.delete({
        where: { id: quiz.id }
      }).catch(() => {
        // Ignore errors if already deleted
      });
    }

    // Delete users
    for (const user of this.seededData.users) {
      await prisma.user.delete({
        where: { id: user.id }
      }).catch(() => {
        // Ignore errors if already deleted
      });
    }

    // Reset seeded data
    this.seededData = { users: [], quizzes: [] };
  }

  getSeededUsers(): TestUser[] {
    return [...this.seededData.users];
  }

  getSeededQuizzes(): TestQuiz[] {
    return [...this.seededData.quizzes];
  }

  async disconnect(): Promise<void> {
    await prisma.$disconnect();
  }
}

// Helper functions for common test scenarios
export async function createTestUserAndLogin() {
  const seeder = TestDataSeeder.getInstance();
  return await seeder.seedTestUser();
}

export async function createTestAdminAndLogin() {
  const seeder = TestDataSeeder.getInstance();
  return await seeder.seedTestAdmin();
}

export async function createTestQuizWithAdmin() {
  const seeder = TestDataSeeder.getInstance();
  const admin = await seeder.seedTestAdmin();
  const quiz = await seeder.seedTestQuiz({}, admin.id);
  return { admin, quiz };
}

export async function cleanupAllTestData() {
  const seeder = TestDataSeeder.getInstance();
  await seeder.cleanupSeededData();
}
