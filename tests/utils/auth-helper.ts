import { Page, expect } from '@playwright/test';
import { TestUser } from './test-data-seeder';

export class AuthHelper {
  constructor(private page: Page) {}

  async loginUser(user: TestUser): Promise<void> {
    // Navigate to login page (correct URL)
    await this.page.goto('/auth/login');

    // Wait for the page to load
    await this.page.waitForLoadState('networkidle');

    // Fill in credentials using correct selectors
    await this.page.fill('input[id="email"]', user.email);
    await this.page.fill('input[id="password"]', user.password);

    // Submit the form
    await this.page.click('button[type="submit"]');

    // Wait for redirect to dashboard
    await this.page.waitForURL(/\/dashboard/, { timeout: 15000 });

    // Verify login was successful by checking URL and Sign Out button
    await expect(this.page).toHaveURL(/\/dashboard/);
    await expect(this.page.locator('button:has-text("Sign Out")')).toBeVisible({ timeout: 5000 });
  }

  async loginAsAdmin(adminUser: TestUser): Promise<void> {
    await this.loginUser(adminUser);

    // Verify admin access by checking for admin-specific elements
    await this.page.goto('/admin');
    await expect(this.page.locator('h1, [data-testid="admin-dashboard"]')).toBeVisible({ timeout: 5000 });
  }

  async logout(): Promise<void> {
    // Look for the Sign Out button in the navbar (based on Navbar.tsx)
    const signOutButton = this.page.locator('button:has-text("Sign Out")');

    if (await signOutButton.isVisible()) {
      await signOutButton.click();
      // Wait for redirect to home page
      await this.page.waitForURL('/', { timeout: 10000 });
    } else {
      // Alternative: navigate to logout endpoint
      await this.page.goto('/api/auth/signout');
      await this.page.waitForLoadState('networkidle');
    }
  }

  async ensureLoggedOut(): Promise<void> {
    // Navigate to home page
    await this.page.goto('/');

    // Check if user is logged in by looking for Sign Out button
    const signOutButton = this.page.locator('button:has-text("Sign Out")');

    if (await signOutButton.isVisible()) {
      await this.logout();
    }

    // Verify we're logged out by checking for Sign In button
    await expect(this.page.locator('a:has-text("Sign In")').first()).toBeVisible({ timeout: 5000 });
  }

  async isLoggedIn(): Promise<boolean> {
    const signOutButton = this.page.locator('button:has-text("Sign Out")');
    return await signOutButton.isVisible();
  }

  async isAdmin(): Promise<boolean> {
    if (!(await this.isLoggedIn())) {
      return false;
    }

    try {
      await this.page.goto('/admin');
      const adminContent = this.page.locator('h1, [data-testid="admin-dashboard"]');
      return await adminContent.isVisible({ timeout: 2000 });
    } catch {
      return false;
    }
  }

  async waitForAuthentication(timeout: number = 10000): Promise<void> {
    await this.page.waitForFunction(
      () => {
        // Check for authentication indicators
        const signOutButton = document.querySelector('button:has-text("Sign Out")');
        const signInButton = document.querySelector('a:has-text("Sign In")');

        // Return true if we can determine auth state (either logged in or logged out)
        return signOutButton !== null || signInButton !== null;
      },
      { timeout }
    );
  }
}

// Helper function to create auth helper
export function createAuthHelper(page: Page): AuthHelper {
  return new AuthHelper(page);
}

// Common authentication patterns for tests
export async function withAuthenticatedUser<T>(
  page: Page,
  user: TestUser,
  testFn: () => Promise<T>
): Promise<T> {
  const auth = new AuthHelper(page);
  await auth.loginUser(user);

  try {
    return await testFn();
  } finally {
    await auth.logout();
  }
}

export async function withAuthenticatedAdmin<T>(
  page: Page,
  adminUser: TestUser,
  testFn: () => Promise<T>
): Promise<T> {
  const auth = new AuthHelper(page);
  await auth.loginAsAdmin(adminUser);

  try {
    return await testFn();
  } finally {
    await auth.logout();
  }
}
