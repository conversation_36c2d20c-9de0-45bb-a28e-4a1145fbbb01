import { Page, expect } from '@playwright/test';
import { TestUser } from './test-data-seeder';

export class AuthHelper {
  constructor(private page: Page) {}

  async loginUser(user: TestUser): Promise<void> {
    // Navigate to login page
    await this.page.goto('/auth/signin');
    
    // Wait for the page to load
    await this.page.waitForLoadState('networkidle');
    
    // Fill in credentials
    await this.page.fill('input[name="email"]', user.email);
    await this.page.fill('input[name="password"]', user.password);
    
    // Submit the form
    await this.page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard or home
    await this.page.waitForURL(/\/(dashboard|$)/, { timeout: 10000 });
    
    // Verify login was successful
    await expect(this.page.locator('[data-testid="user-menu"], .user-avatar, [aria-label*="user"]')).toBeVisible({ timeout: 5000 });
  }

  async loginAsAdmin(adminUser: TestUser): Promise<void> {
    await this.loginUser(adminUser);
    
    // Verify admin access by checking for admin-specific elements
    await this.page.goto('/admin');
    await expect(this.page.locator('h1, [data-testid="admin-dashboard"]')).toBeVisible({ timeout: 5000 });
  }

  async logout(): Promise<void> {
    // Try to find and click logout button
    const userMenu = this.page.locator('[data-testid="user-menu"], .user-avatar, [aria-label*="user"]');
    
    if (await userMenu.isVisible()) {
      await userMenu.click();
      
      // Look for logout button in dropdown
      const logoutButton = this.page.locator('text="Logout", text="Sign out", [data-testid="logout"]');
      if (await logoutButton.isVisible()) {
        await logoutButton.click();
      }
    }
    
    // Alternative: navigate to logout endpoint
    await this.page.goto('/api/auth/signout');
    await this.page.waitForLoadState('networkidle');
  }

  async ensureLoggedOut(): Promise<void> {
    // Navigate to home page
    await this.page.goto('/');
    
    // Check if user is logged in
    const userMenu = this.page.locator('[data-testid="user-menu"], .user-avatar, [aria-label*="user"]');
    
    if (await userMenu.isVisible()) {
      await this.logout();
    }
    
    // Verify we're logged out
    await expect(this.page.locator('text="Sign in", text="Login", [href*="signin"]')).toBeVisible({ timeout: 5000 });
  }

  async isLoggedIn(): Promise<boolean> {
    const userMenu = this.page.locator('[data-testid="user-menu"], .user-avatar, [aria-label*="user"]');
    return await userMenu.isVisible();
  }

  async isAdmin(): Promise<boolean> {
    if (!(await this.isLoggedIn())) {
      return false;
    }
    
    try {
      await this.page.goto('/admin');
      const adminContent = this.page.locator('h1, [data-testid="admin-dashboard"]');
      return await adminContent.isVisible({ timeout: 2000 });
    } catch {
      return false;
    }
  }

  async waitForAuthentication(timeout: number = 10000): Promise<void> {
    await this.page.waitForFunction(
      () => {
        // Check for authentication indicators
        const userMenu = document.querySelector('[data-testid="user-menu"], .user-avatar, [aria-label*="user"]');
        const loginButton = document.querySelector('text="Sign in", text="Login", [href*="signin"]');
        
        // Return true if we can determine auth state (either logged in or logged out)
        return userMenu !== null || loginButton !== null;
      },
      { timeout }
    );
  }
}

// Helper function to create auth helper
export function createAuthHelper(page: Page): AuthHelper {
  return new AuthHelper(page);
}

// Common authentication patterns for tests
export async function withAuthenticatedUser<T>(
  page: Page, 
  user: TestUser, 
  testFn: () => Promise<T>
): Promise<T> {
  const auth = new AuthHelper(page);
  await auth.loginUser(user);
  
  try {
    return await testFn();
  } finally {
    await auth.logout();
  }
}

export async function withAuthenticatedAdmin<T>(
  page: Page, 
  adminUser: TestUser, 
  testFn: () => Promise<T>
): Promise<T> {
  const auth = new AuthHelper(page);
  await auth.loginAsAdmin(adminUser);
  
  try {
    return await testFn();
  } finally {
    await auth.logout();
  }
}
