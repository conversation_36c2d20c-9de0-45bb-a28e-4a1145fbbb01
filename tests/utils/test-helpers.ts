import { render, RenderOptions } from '@testing-library/react';
import { ReactElement } from 'react';
import { SessionProvider } from 'next-auth/react';

// Mock session data
export const mockAdminSession = {
  user: {
    id: 'admin1',
    email: '<EMAIL>',
    name: 'Admin User',
    role: 'admin',
  },
  expires: '2024-12-31',
};

export const mockUserSession = {
  user: {
    id: 'user1',
    email: '<EMAIL>',
    name: 'Regular User',
    role: 'user',
  },
  expires: '2024-12-31',
};

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  session?: any;
}

export function renderWithProviders(
  ui: ReactElement,
  { session = null, ...renderOptions }: CustomRenderOptions = {}
) {
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <SessionProvider session={session}>
        {children}
      </SessionProvider>
    );
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// Mock quiz data
export const mockQuiz = {
  id: 'quiz1',
  title: 'Test Quiz',
  description: 'A test quiz for cybersecurity',
  category: 'Web Application Security',
  difficulty: 'Beginner',
  createdBy: 'admin1',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  questions: [],
  _count: { questions: 0 },
};

export const mockQuestion = {
  id: 'question1',
  questionId: 'q1',
  type: 'multiple_choice',
  text: 'What is XSS?',
  points: 2,
  options: JSON.stringify([
    'Cross-Site Scripting',
    'Cross-Site Request Forgery',
    'SQL Injection',
    'Buffer Overflow'
  ]),
  correctAnswer: JSON.stringify(0),
  feedback: JSON.stringify({
    correct: 'Correct! XSS stands for Cross-Site Scripting.',
    incorrect: 'Not quite right. Try again.'
  }),
  hint: null,
  quizId: 'quiz1',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
};

// API response helpers
export function createMockResponse(data: any, status = 200) {
  return {
    ok: status >= 200 && status < 300,
    status,
    json: async () => data,
    text: async () => JSON.stringify(data),
  } as Response;
}

// Database mock helpers
export function createMockDb() {
  return {
    quiz: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    question: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    quizAttempt: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
  };
}

// Form data helpers
export const sampleQuizData = {
  title: 'Sample Quiz',
  description: 'A sample quiz for testing',
  category: 'Web Application Security',
  difficulty: 'Beginner',
};

export const sampleQuestionData = {
  type: 'multiple_choice',
  text: 'What is the most common web vulnerability?',
  points: 2,
  options: [
    'SQL Injection',
    'Cross-Site Scripting (XSS)',
    'Cross-Site Request Forgery (CSRF)',
    'Buffer Overflow'
  ],
  correctAnswer: 0,
  feedback: {
    correct: 'Correct! SQL Injection is indeed very common.',
    incorrect: 'Not quite right. Try again.'
  },
};

// Wait helpers for async operations
export function waitFor(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Local storage mock
export function mockLocalStorage() {
  const store: { [key: string]: string } = {};
  
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach(key => delete store[key]);
    }),
  };
}

// Console mock helpers
export function mockConsole() {
  const originalConsole = { ...console };
  
  beforeEach(() => {
    console.log = jest.fn();
    console.error = jest.fn();
    console.warn = jest.fn();
  });
  
  afterEach(() => {
    Object.assign(console, originalConsole);
  });
}

// Network request mock
export function mockFetch() {
  const mockFetch = jest.fn();
  global.fetch = mockFetch;
  return mockFetch;
}

// File upload mock
export function createMockFile(name: string, content: string, type = 'text/plain') {
  return new File([content], name, { type });
}

// Error boundary test helper
export function throwError() {
  throw new Error('Test error');
}

// Accessibility test helpers
export function checkAccessibility(element: HTMLElement) {
  // Check for basic accessibility attributes
  const checks = {
    hasAriaLabel: element.hasAttribute('aria-label'),
    hasAriaLabelledBy: element.hasAttribute('aria-labelledby'),
    hasRole: element.hasAttribute('role'),
    hasTabIndex: element.hasAttribute('tabindex'),
  };
  
  return checks;
}

// Performance test helpers
export function measureRenderTime(renderFn: () => void) {
  const start = performance.now();
  renderFn();
  const end = performance.now();
  return end - start;
}
