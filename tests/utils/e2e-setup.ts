import { test as base, expect } from '@playwright/test';
import { TestDataSeeder, TestUser, TestQuiz } from './test-data-seeder';
import { AuthHelper } from './auth-helper';

// Extend the base test to include our custom fixtures
export const test = base.extend<{
  seeder: TestDataSeeder;
  auth: AuthHelper;
  testUser: TestUser;
  testAdmin: TestUser;
  authenticatedUser: TestUser;
  authenticatedAdmin: TestUser;
}>({
  // Seeder fixture - provides test data management
  seeder: async ({}, use) => {
    const seeder = TestDataSeeder.getInstance();
    await use(seeder);
    // Cleanup happens in global teardown
  },

  // Auth helper fixture
  auth: async ({ page }, use) => {
    const auth = new AuthHelper(page);
    await use(auth);
  },

  // Test user fixture - creates a regular user
  testUser: async ({ seeder }, use) => {
    const user = await seeder.seedTestUser();
    await use(user);
  },

  // Test admin fixture - creates an admin user
  testAdmin: async ({ seeder }, use) => {
    const admin = await seeder.seedTestAdmin();
    await use(admin);
  },

  // Authenticated user fixture - creates and logs in a regular user
  authenticatedUser: async ({ page, auth, seeder }, use) => {
    const user = await seeder.seedTestUser();
    await auth.loginUser(user);
    await use(user);
    await auth.logout();
  },

  // Authenticated admin fixture - creates and logs in an admin user
  authenticatedAdmin: async ({ page, auth, seeder }, use) => {
    const admin = await seeder.seedTestAdmin();
    await auth.loginAsAdmin(admin);
    await use(admin);
    await auth.logout();
  },
});

// Re-export expect for convenience
export { expect } from '@playwright/test';

// Helper functions for common test patterns
export async function createTestQuizWithUser(seeder: TestDataSeeder, userId?: string) {
  if (!userId) {
    const admin = await seeder.seedTestAdmin();
    userId = admin.id;
  }

  return await seeder.seedTestQuiz({
    title: `E2E Test Quiz ${Date.now()}`,
    description: 'A quiz created for E2E testing'
  }, userId);
}

export async function waitForPageLoad(page: any, timeout: number = 10000) {
  await page.waitForLoadState('networkidle', { timeout });
}

export async function navigateAndWait(page: any, url: string, timeout: number = 10000) {
  await page.goto(url);
  await waitForPageLoad(page, timeout);
}

// Common assertions for authentication state
export async function assertLoggedIn(page: any) {
  await expect(page.locator('button:has-text("Sign Out")')).toBeVisible({ timeout: 5000 });
}

export async function assertLoggedOut(page: any) {
  await expect(page.locator('a:has-text("Sign In")')).toBeVisible({ timeout: 5000 });
}

export async function assertOnDashboard(page: any) {
  await expect(page).toHaveURL(/\/dashboard/);
  await expect(page.locator('text="Welcome"')).toBeVisible({ timeout: 5000 });
}

export async function assertOnHomePage(page: any) {
  await expect(page).toHaveURL('/');
}

// Quiz-specific helpers
export async function navigateToQuiz(page: any, quizId: string) {
  await navigateAndWait(page, `/quiz/${quizId}`);
}

export async function navigateToQuizEditor(page: any, quizId: string) {
  await navigateAndWait(page, `/dashboard/quizzes/${quizId}/edit`);
}

export async function navigateToQuizPreview(page: any, quizId: string) {
  await navigateAndWait(page, `/dashboard/quizzes/${quizId}/preview`);
}

// Form helpers
export async function fillQuizForm(page: any, quizData: { title: string; description: string }) {
  await page.fill('input[name="title"]', quizData.title);
  await page.fill('textarea[name="description"]', quizData.description);
}

export async function submitForm(page: any, buttonText: string = 'Submit') {
  await page.click(`button:has-text("${buttonText}")`);
}

// Wait for specific elements
export async function waitForQuizCard(page: any, timeout: number = 10000) {
  await page.waitForSelector('.quiz-card, .quiz-item', { timeout });
}

export async function waitForQuestionRenderer(page: any, timeout: number = 10000) {
  await page.waitForSelector('[data-testid="question-renderer"], .question-container', { timeout });
}

// Error handling helpers
export async function expectNoErrors(page: any) {
  const errorMessages = page.locator('.error-message, .alert-error, [role="alert"]');
  const count = await errorMessages.count();
  expect(count).toBe(0);
}

export async function expectSuccessMessage(page: any, message?: string) {
  const successElement = page.locator('.success-message, .alert-success, [role="status"]');
  await expect(successElement).toBeVisible({ timeout: 5000 });

  if (message) {
    await expect(successElement).toContainText(message);
  }
}

// Cleanup helpers
export async function cleanupTestData() {
  const seeder = TestDataSeeder.getInstance();
  await seeder.cleanupSeededData();
}

// Global test hooks
export async function globalSetup() {
  // Any global setup needed for E2E tests
  console.log('Setting up E2E test environment...');
}

export async function globalTeardown() {
  // Cleanup all test data
  await cleanupTestData();

  const seeder = TestDataSeeder.getInstance();
  await seeder.disconnect();

  console.log('E2E test environment cleaned up.');
}
