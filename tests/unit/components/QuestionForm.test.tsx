import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Simple test component to test basic functionality
const TestQuestionForm = ({ onSubmit, onCancel }: { onSubmit: (data: any) => void; onCancel: () => void }) => {
  return (
    <form role="form" onSubmit={(e) => {
      e.preventDefault();
      const formData = new FormData(e.currentTarget);
      const questionType = formData.get('questionType') as string;
      if (questionType) {
        onSubmit({ type: questionType, text: 'Test question' });
      }
    }}>
      <label htmlFor="questionType">Question Type</label>
      <select id="questionType" name="questionType" required>
        <option value="">Select type</option>
        <option value="multiple_choice">Multiple Choice</option>
        <option value="true_false">True/False</option>
      </select>

      <button type="submit">Submit</button>
      <button type="button" onClick={onCancel}>Cancel</button>
    </form>
  );
};

describe('TestQuestionForm', () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders question type selector', () => {
    render(
      <TestQuestionForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.getByLabelText(/question type/i)).toBeInTheDocument();
  });

  it('calls onSubmit when form is submitted', async () => {
    const user = userEvent.setup();

    render(
      <TestQuestionForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const select = screen.getByLabelText(/question type/i);
    await user.selectOptions(select, 'multiple_choice');

    const submitButton = screen.getByText('Submit');
    await user.click(submitButton);

    expect(mockOnSubmit).toHaveBeenCalledWith({
      type: 'multiple_choice',
      text: 'Test question'
    });
  });

  it('calls onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup();

    render(
      <TestQuestionForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const cancelButton = screen.getByText(/cancel/i);
    await user.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('validates required fields', () => {
    render(
      <TestQuestionForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    // Try to submit without selecting question type
    const form = screen.getByRole('form');
    fireEvent.submit(form);

    // Should not call onSubmit
    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('allows selecting different question types', async () => {
    const user = userEvent.setup();

    render(
      <TestQuestionForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const select = screen.getByLabelText(/question type/i) as HTMLSelectElement;

    // Select multiple choice
    await user.selectOptions(select, 'multiple_choice');
    expect(select.value).toBe('multiple_choice');

    // Change to true/false
    await user.selectOptions(select, 'true_false');
    expect(select.value).toBe('true_false');
  });
});
