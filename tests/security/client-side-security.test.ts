/**
 * Client-Side Security Tests
 * 
 * These tests verify that the client-side application doesn't expose
 * sensitive information and properly handles security measures.
 */

import { test, expect } from '@playwright/test';
import { TestDataSeeder } from '../utils/test-data-seeder';

test.describe('Client-Side Security Tests', () => {
  let seeder: TestDataSeeder;
  let testQuizId: string;

  test.beforeAll(async () => {
    seeder = TestDataSeeder.getInstance();
    
    // Create a test quiz
    const testQuiz = await seeder.seedTestQuiz({
      title: 'Client Security Test Quiz',
      description: 'Quiz for testing client-side security',
      content: {
        quiz_id: 'client-security-test',
        title: { en: 'Client Security Test Quiz' },
        description: { en: 'Quiz for testing client-side security' },
        version: '1.0.0',
        language: 'en',
        questions: [
          {
            question_id: 'security_mc',
            type: 'multiple_choice',
            question_text: { en: 'Which is a security vulnerability?' },
            options: [
              { id: 'a', text: { en: 'SQL Injection' }, is_correct: true },
              { id: 'b', text: { en: 'Proper Input Validation' }, is_correct: false },
              { id: 'c', text: { en: 'HTTPS' }, is_correct: false }
            ],
            points: 1
          }
        ],
        settings: {
          time_limit: null,
          shuffle_questions: false,
          shuffle_options: false,
          show_results: true,
          allow_review: true
        }
      }
    });
    
    testQuizId = testQuiz.id;
  });

  test.afterAll(async () => {
    await seeder.cleanupSeededData();
    await seeder.disconnect();
  });

  test('should not expose answers in browser console', async ({ page }) => {
    // Navigate to quiz page
    await page.goto(`/quiz/${testQuizId}`);
    await page.waitForLoadState('networkidle');

    // Check console for any exposed answer data
    const consoleLogs: string[] = [];
    page.on('console', msg => {
      consoleLogs.push(msg.text());
    });

    // Wait for page to fully load
    await page.waitForTimeout(2000);

    // Check that no console logs contain answer data
    const suspiciousLogs = consoleLogs.filter(log => 
      log.includes('is_correct') || 
      log.includes('correct_answer') || 
      log.includes('correctAnswer') ||
      log.includes('correct_answers') ||
      log.includes('correctAnswers')
    );

    expect(suspiciousLogs.length).toBe(0);
  });

  test('should not expose answers in page source', async ({ page }) => {
    await page.goto(`/quiz/${testQuizId}`);
    await page.waitForLoadState('networkidle');

    const pageContent = await page.content();
    
    // Check that page source doesn't contain answer flags
    expect(pageContent).not.toContain('is_correct":true');
    expect(pageContent).not.toContain('is_correct": true');
    expect(pageContent).not.toContain('"correct_answer":true');
    expect(pageContent).not.toContain('"correct_answer": true');
    expect(pageContent).not.toContain('correctAnswer');
    expect(pageContent).not.toContain('correctAnswers');
  });

  test('should not expose answers in network requests', async ({ page }) => {
    const networkRequests: any[] = [];
    
    // Capture all network requests
    page.on('response', response => {
      networkRequests.push({
        url: response.url(),
        status: response.status(),
        headers: response.headers()
      });
    });

    await page.goto(`/quiz/${testQuizId}`);
    await page.waitForLoadState('networkidle');

    // Check quiz-related API calls
    const quizRequests = networkRequests.filter(req => 
      req.url.includes('/api/quiz') || req.url.includes('/quiz')
    );

    expect(quizRequests.length).toBeGreaterThan(0);

    // For each quiz request, verify response doesn't contain answers
    for (const request of quizRequests) {
      if (request.status === 200) {
        try {
          const response = await page.request.get(request.url);
          const data = await response.json();
          
          // Check that response doesn't contain answer data
          const responseText = JSON.stringify(data);
          expect(responseText).not.toContain('is_correct":true');
          expect(responseText).not.toContain('correct_answer":true');
          expect(responseText).not.toContain('correctAnswer');
          expect(responseText).not.toContain('correctAnswers');
        } catch (error) {
          // Some requests might not be JSON, that's okay
        }
      }
    }
  });

  test('should not allow answer manipulation via browser DevTools', async ({ page }) => {
    await page.goto(`/quiz/${testQuizId}`);
    await page.waitForLoadState('networkidle');

    // Try to inject answer data via console
    const injectionResult = await page.evaluate(() => {
      try {
        // Try to access React component state
        const reactRoot = document.querySelector('[data-reactroot]') || document.querySelector('#__next');
        if (reactRoot && (reactRoot as any)._reactInternalFiber) {
          return 'React fiber accessible';
        }
        
        // Try to access window variables
        if ((window as any).quizAnswers || (window as any).correctAnswers) {
          return 'Global answer variables found';
        }
        
        // Try to modify localStorage
        localStorage.setItem('quiz_answers', JSON.stringify({ test: 'answer' }));
        
        return 'No obvious vulnerabilities';
      } catch (error) {
        return 'Access restricted';
      }
    });

    // We don't want to find obvious vulnerabilities
    expect(injectionResult).not.toContain('React fiber accessible');
    expect(injectionResult).not.toContain('Global answer variables found');
  });

  test('should handle quiz submission securely', async ({ page }) => {
    // Login first
    const testUser = await seeder.seedTestUser();
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', testUser.email);
    await page.fill('input[id="password"]', testUser.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard/);

    // Navigate to quiz
    await page.goto(`/quiz/${testQuizId}`);
    await page.waitForLoadState('networkidle');

    // Capture network requests during quiz submission
    const submissionRequests: any[] = [];
    page.on('request', request => {
      if (request.url().includes('/submit')) {
        submissionRequests.push({
          url: request.url(),
          method: request.method(),
          postData: request.postData()
        });
      }
    });

    // Answer the question
    await page.click('input[type="radio"][value="a"]');
    
    // Submit quiz (if submit button exists)
    const submitButton = page.locator('button:has-text("Submit"), button:has-text("Finish")');
    if (await submitButton.isVisible()) {
      await submitButton.click();
      
      // Wait for submission to complete
      await page.waitForTimeout(2000);
      
      // Verify submission was made to secure endpoint
      expect(submissionRequests.length).toBeGreaterThan(0);
      
      const submission = submissionRequests[0];
      expect(submission.method).toBe('POST');
      expect(submission.url).toContain('/submit');
      
      // Verify submission data doesn't contain sensitive info
      if (submission.postData) {
        const postData = JSON.parse(submission.postData);
        expect(postData.answers).toBeDefined();
        expect(postData.timeSpent).toBeDefined();
        // Should not contain answer keys or validation data
        expect(postData.correctAnswers).toBeUndefined();
        expect(postData.validation).toBeUndefined();
      }
    }
  });

  test('should not expose database queries or internal errors', async ({ page }) => {
    const errorMessages: string[] = [];
    
    // Capture console errors
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errorMessages.push(msg.text());
      }
    });

    // Try to access invalid quiz
    await page.goto('/quiz/invalid-quiz-id');
    
    // Wait for error handling
    await page.waitForTimeout(2000);

    // Check that error messages don't expose internal details
    const suspiciousErrors = errorMessages.filter(error => 
      error.includes('SELECT') || 
      error.includes('INSERT') || 
      error.includes('UPDATE') || 
      error.includes('DELETE') ||
      error.includes('prisma') ||
      error.includes('mongodb') ||
      error.includes('database')
    );

    expect(suspiciousErrors.length).toBe(0);
  });

  test('should implement proper CSRF protection', async ({ page }) => {
    // Login first
    const testUser = await seeder.seedTestUser();
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', testUser.email);
    await page.fill('input[id="password"]', testUser.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard/);

    // Try to submit quiz without proper headers (simulating CSRF attack)
    const response = await page.request.post(`/api/quizzes/${testQuizId}/submit`, {
      data: {
        answers: { 'security_mc': 'a' },
        timeSpent: 60
      },
      headers: {
        'Origin': 'https://malicious-site.com',
        'Referer': 'https://malicious-site.com'
      }
    });

    // Should either work (if CSRF protection not yet implemented) or be blocked
    // This test documents current behavior and will need updating when CSRF protection is added
    if (response.status() === 403) {
      const errorData = await response.json();
      expect(errorData.error).toContain('CSRF');
    } else {
      // Currently no CSRF protection - this is a known security gap
      console.log('CSRF protection not yet implemented');
    }
  });

  test('should sanitize user input in quiz content', async ({ page }) => {
    // This test checks that any user-generated content is properly sanitized
    await page.goto(`/quiz/${testQuizId}`);
    await page.waitForLoadState('networkidle');

    // Check that question text doesn't contain unescaped HTML
    const questionElements = await page.locator('.question-text, .prose, [data-testid="question-text"]').all();
    
    for (const element of questionElements) {
      const innerHTML = await element.innerHTML();
      
      // Should not contain unescaped script tags or dangerous HTML
      expect(innerHTML).not.toContain('<script>');
      expect(innerHTML).not.toContain('javascript:');
      expect(innerHTML).not.toContain('onload=');
      expect(innerHTML).not.toContain('onerror=');
    }
  });

  test('should not expose sensitive configuration', async ({ page }) => {
    await page.goto(`/quiz/${testQuizId}`);
    await page.waitForLoadState('networkidle');

    // Check that sensitive configuration is not exposed in client
    const configData = await page.evaluate(() => {
      return {
        env: (window as any).process?.env,
        config: (window as any).__CONFIG__,
        secrets: (window as any).__SECRETS__,
        apiKeys: (window as any).__API_KEYS__
      };
    });

    expect(configData.env).toBeUndefined();
    expect(configData.config).toBeUndefined();
    expect(configData.secrets).toBeUndefined();
    expect(configData.apiKeys).toBeUndefined();
  });
});
