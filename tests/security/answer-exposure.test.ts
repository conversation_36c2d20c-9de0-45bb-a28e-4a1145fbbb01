/**
 * Security Tests: Answer Exposure Prevention
 * 
 * These tests verify that quiz answers are not exposed to the client
 * and that proper server-side validation is in place.
 */

import { test, expect } from '@playwright/test';
import { TestDataSeeder } from '../utils/test-data-seeder';

test.describe('Answer Exposure Security Tests', () => {
  let seeder: TestDataSeeder;
  let testQuizId: string;

  test.beforeAll(async () => {
    seeder = TestDataSeeder.getInstance();
    
    // Create a test quiz with known answers
    const testQuiz = await seeder.seedTestQuiz({
      title: 'Security Test Quiz',
      description: 'Quiz for testing answer exposure',
      content: {
        quiz_id: 'security-test-quiz',
        title: { en: 'Security Test Quiz' },
        description: { en: 'Quiz for testing answer exposure' },
        version: '1.0.0',
        language: 'en',
        questions: [
          {
            question_id: 'mc_test',
            type: 'multiple_choice',
            question_text: { en: 'What is 2+2?' },
            options: [
              { id: 'a', text: { en: '3' }, is_correct: false },
              { id: 'b', text: { en: '4' }, is_correct: true },
              { id: 'c', text: { en: '5' }, is_correct: false }
            ],
            points: 1
          },
          {
            question_id: 'tf_test',
            type: 'true_false',
            question_text: { en: 'The sky is blue.' },
            correct_answer: true,
            points: 1
          },
          {
            question_id: 'sa_test',
            type: 'short_answer',
            question_text: { en: 'What is the capital of France?' },
            correct_answers: ['Paris', 'paris'],
            points: 1
          }
        ],
        settings: {
          time_limit: null,
          shuffle_questions: false,
          shuffle_options: false,
          show_results: true,
          allow_review: true
        }
      }
    });
    
    testQuizId = testQuiz.id;
  });

  test.afterAll(async () => {
    await seeder.cleanupSeededData();
    await seeder.disconnect();
  });

  test('should not expose correct answers in quiz API response', async ({ request }) => {
    // Test the quiz API endpoint
    const response = await request.get(`/api/quizzes/${testQuizId}`);
    expect(response.status()).toBe(200);
    
    const quizData = await response.json();
    
    // Verify quiz data is returned
    expect(quizData).toBeDefined();
    expect(quizData.questions).toBeDefined();
    expect(Array.isArray(quizData.questions)).toBe(true);
    
    // Check each question for answer exposure
    for (const question of quizData.questions) {
      switch (question.type) {
        case 'multiple_choice':
          // Options should exist but without is_correct flags
          expect(question.options).toBeDefined();
          expect(Array.isArray(question.options)).toBe(true);
          
          for (const option of question.options) {
            expect(option.id).toBeDefined();
            expect(option.text).toBeDefined();
            // SECURITY: is_correct should not be present
            expect(option.is_correct).toBeUndefined();
          }
          
          // These fields should not exist
          expect(question.correctAnswer).toBeUndefined();
          expect(question.correct_answer).toBeUndefined();
          break;
          
        case 'true_false':
          // Correct answer should not be exposed
          expect(question.correctAnswer).toBeUndefined();
          expect(question.correct_answer).toBeUndefined();
          break;
          
        case 'short_answer':
          // Correct answers should not be exposed
          expect(question.correctAnswers).toBeUndefined();
          expect(question.correct_answers).toBeUndefined();
          break;
      }
      
      // Common fields that should not be exposed
      expect(question.stems).toBeUndefined();
      expect(question.correctPairs).toBeUndefined();
      expect(question.textTemplate).toBeUndefined();
      expect(question.blanks).toBeUndefined();
    }
  });

  test('should not expose answers in questions API endpoint', async ({ request }) => {
    const response = await request.get(`/api/quizzes/${testQuizId}/questions`);
    expect(response.status()).toBe(200);
    
    const questions = await response.json();
    expect(Array.isArray(questions)).toBe(true);
    
    // Verify no answer data is exposed
    for (const question of questions) {
      // Check that answer fields are not present
      expect(question.options).toBeUndefined();
      expect(question.correctAnswer).toBeUndefined();
      expect(question.correctAnswers).toBeUndefined();
      expect(question.correct_answer).toBeUndefined();
      expect(question.correct_answers).toBeUndefined();
      expect(question.stems).toBeUndefined();
      expect(question.correctPairs).toBeUndefined();
      expect(question.blanks).toBeUndefined();
    }
  });

  test('should require authentication for quiz submission', async ({ request }) => {
    const response = await request.post(`/api/quizzes/${testQuizId}/submit`, {
      data: {
        answers: { 'mc_test': 'b' },
        timeSpent: 60
      }
    });
    
    // Should return 401 Unauthorized
    expect(response.status()).toBe(401);
  });

  test('should validate answers server-side', async ({ page }) => {
    // This test requires authentication, so we'll use a browser context
    await page.goto('/auth/login');
    
    // Login with test user
    const testUser = await seeder.seedTestUser();
    await page.fill('input[id="email"]', testUser.email);
    await page.fill('input[id="password"]', testUser.password);
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForURL(/\/dashboard/);
    
    // Test server-side validation by submitting answers
    const response = await page.request.post(`/api/quizzes/${testQuizId}/submit`, {
      data: {
        answers: {
          'mc_test': 'b',  // Correct answer
          'tf_test': true, // Correct answer
          'sa_test': 'Paris' // Correct answer
        },
        timeSpent: 120
      }
    });
    
    expect(response.status()).toBe(200);
    
    const result = await response.json();
    expect(result.success).toBe(true);
    expect(result.score).toBeGreaterThan(0);
    expect(result.maxScore).toBeDefined();
    expect(result.percentage).toBeDefined();
    expect(result.passed).toBeDefined();
    
    // Verify that correct answers are not returned to client
    expect(result.questionResults).toBeDefined();
    for (const questionResult of result.questionResults) {
      expect(questionResult.questionId).toBeDefined();
      expect(questionResult.correct).toBeDefined();
      expect(questionResult.points).toBeDefined();
      // SECURITY: Correct answers should not be exposed
      expect(questionResult.correctAnswer).toBeUndefined();
      expect(questionResult.userAnswer).toBeUndefined();
    }
  });

  test('should prevent rapid quiz submissions', async ({ page }) => {
    // Login
    const testUser = await seeder.seedTestUser();
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', testUser.email);
    await page.fill('input[id="password"]', testUser.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard/);
    
    // Submit quiz once
    const firstResponse = await page.request.post(`/api/quizzes/${testQuizId}/submit`, {
      data: {
        answers: { 'mc_test': 'b' },
        timeSpent: 60
      }
    });
    expect(firstResponse.status()).toBe(200);
    
    // Try to submit again immediately
    const secondResponse = await page.request.post(`/api/quizzes/${testQuizId}/submit`, {
      data: {
        answers: { 'mc_test': 'a' },
        timeSpent: 30
      }
    });
    
    // Should be rate limited
    expect(secondResponse.status()).toBe(429);
    
    const errorData = await secondResponse.json();
    expect(errorData.error).toContain('wait');
  });

  test('should detect suspicious activity', async ({ page }) => {
    // Login
    const testUser = await seeder.seedTestUser();
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', testUser.email);
    await page.fill('input[id="password"]', testUser.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard/);
    
    // Submit quiz with suspiciously fast time
    const response = await page.request.post(`/api/quizzes/${testQuizId}/submit`, {
      data: {
        answers: {
          'mc_test': 'b',
          'tf_test': true,
          'sa_test': 'Paris'
        },
        timeSpent: 5 // Very fast completion
      }
    });
    
    expect(response.status()).toBe(200);
    
    const result = await response.json();
    expect(result.success).toBe(true);
    
    // The suspicious activity should be logged in metadata (not exposed to client)
    // We can't directly test this without database access, but the submission should still work
  });

  test('should handle invalid quiz submission data', async ({ page }) => {
    // Login
    const testUser = await seeder.seedTestUser();
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', testUser.email);
    await page.fill('input[id="password"]', testUser.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard/);
    
    // Test invalid answers format
    const invalidResponse1 = await page.request.post(`/api/quizzes/${testQuizId}/submit`, {
      data: {
        answers: "invalid", // Should be object
        timeSpent: 60
      }
    });
    expect(invalidResponse1.status()).toBe(400);
    
    // Test invalid time spent
    const invalidResponse2 = await page.request.post(`/api/quizzes/${testQuizId}/submit`, {
      data: {
        answers: { 'mc_test': 'b' },
        timeSpent: -1 // Invalid time
      }
    });
    expect(invalidResponse2.status()).toBe(400);
    
    // Test missing data
    const invalidResponse3 = await page.request.post(`/api/quizzes/${testQuizId}/submit`, {
      data: {}
    });
    expect(invalidResponse3.status()).toBe(400);
  });

  test('should not allow access to non-existent quiz', async ({ page }) => {
    // Login
    const testUser = await seeder.seedTestUser();
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', testUser.email);
    await page.fill('input[id="password"]', testUser.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard/);
    
    // Try to submit to non-existent quiz
    const response = await page.request.post('/api/quizzes/nonexistent/submit', {
      data: {
        answers: { 'test': 'answer' },
        timeSpent: 60
      }
    });
    
    expect(response.status()).toBe(404);
  });
});
