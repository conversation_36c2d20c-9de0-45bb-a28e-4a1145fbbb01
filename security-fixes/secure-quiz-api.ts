/**
 * SECURE QUIZ API IMPLEMENTATION
 * 
 * This file demonstrates how to implement secure quiz functionality
 * that prevents answer exposure and implements proper server-side validation.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { rateLimit } from '@/lib/rate-limit';
import { validateCSRF } from '@/lib/csrf';

// ============================================================================
// SECURE QUIZ RETRIEVAL (WITHOUT ANSWERS)
// ============================================================================

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id: quizId } = await params;

    // Get quiz without sensitive answer data
    const quiz = await db.quiz.findUnique({
      where: { id: quizId },
      include: {
        questions: {
          select: {
            id: true,
            questionId: true,
            type: true,
            text: true,
            points: true,
            media: true,
            codeSnippet: true,
            hint: true,
            // SECURITY: Exclude all answer-related fields
            options: false,
            correctAnswer: false,
            correctAnswers: false,
            stems: false,
            correctPairs: false,
            textTemplate: false,
            blanks: false,
          }
        }
      }
    });

    if (!quiz) {
      return NextResponse.json({ error: 'Quiz not found' }, { status: 404 });
    }

    // Check access permissions
    if (!quiz.isPublished && (!session || quiz.creatorId !== session.user.id)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Sanitize questions to remove any remaining answer data
    const sanitizedQuestions = quiz.questions.map(question => {
      const sanitized = { ...question };
      
      // Remove any answer-related fields that might have leaked through
      delete sanitized.options;
      delete sanitized.correctAnswer;
      delete sanitized.correctAnswers;
      delete sanitized.correct_answer;
      delete sanitized.correct_answers;
      delete sanitized.stems;
      delete sanitized.correctPairs;
      delete sanitized.textTemplate;
      delete sanitized.blanks;
      
      // For multiple choice, return options without is_correct flag
      if (question.type === 'multiple_choice') {
        const originalOptions = JSON.parse(question.options || '[]');
        sanitized.options = originalOptions.map((opt: any) => ({
          id: opt.id,
          text: opt.text,
          // SECURITY: Never include is_correct
        }));
      }
      
      return sanitized;
    });

    return NextResponse.json({
      ...quiz,
      questions: sanitizedQuestions
    });

  } catch (error) {
    console.error('Error fetching quiz:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// ============================================================================
// SECURE QUIZ SESSION MANAGEMENT
// ============================================================================

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: quizId } = await params;
    const { action } = await req.json();

    if (action === 'start') {
      return await startQuizSession(quizId, session.user.id);
    } else if (action === 'submit') {
      return await submitQuizAnswers(req, quizId, session.user.id);
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });

  } catch (error) {
    console.error('Error in quiz session:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// ============================================================================
// START QUIZ SESSION
// ============================================================================

async function startQuizSession(quizId: string, userId: string) {
  // Check rate limiting
  const rateLimitResult = await rateLimit.check(userId, 'quiz_start', 5, 3600); // 5 attempts per hour
  if (!rateLimitResult.success) {
    return NextResponse.json({ 
      error: 'Rate limit exceeded. Please try again later.' 
    }, { status: 429 });
  }

  // Verify quiz exists and is accessible
  const quiz = await db.quiz.findUnique({
    where: { id: quizId, isPublished: true }
  });

  if (!quiz) {
    return NextResponse.json({ error: 'Quiz not found' }, { status: 404 });
  }

  // Check if user has already completed this quiz recently
  const recentAttempt = await db.quizAttempt.findFirst({
    where: {
      userId,
      quizId,
      submittedAt: {
        gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 24 hours
      }
    }
  });

  if (recentAttempt) {
    return NextResponse.json({ 
      error: 'You have already taken this quiz recently. Please wait 24 hours.' 
    }, { status: 429 });
  }

  // Create quiz session
  const session = await db.quizSession.create({
    data: {
      userId,
      quizId,
      startedAt: new Date(),
      expiresAt: new Date(Date.now() + (quiz.timeLimit || 60) * 60 * 1000),
      sessionToken: generateSecureToken()
    }
  });

  return NextResponse.json({
    sessionId: session.id,
    sessionToken: session.sessionToken,
    expiresAt: session.expiresAt,
    timeLimit: quiz.timeLimit
  });
}

// ============================================================================
// SUBMIT QUIZ ANSWERS (SERVER-SIDE VALIDATION)
// ============================================================================

async function submitQuizAnswers(req: NextRequest, quizId: string, userId: string) {
  // Validate CSRF token
  await validateCSRF(req);

  const { answers, sessionToken, timeSpent } = await req.json();

  // Verify quiz session
  const quizSession = await db.quizSession.findFirst({
    where: {
      userId,
      quizId,
      sessionToken,
      expiresAt: { gte: new Date() }
    }
  });

  if (!quizSession) {
    return NextResponse.json({ 
      error: 'Invalid or expired quiz session' 
    }, { status: 401 });
  }

  // Get quiz with correct answers (SERVER-SIDE ONLY)
  const quiz = await getQuizWithAnswers(quizId);
  if (!quiz) {
    return NextResponse.json({ error: 'Quiz not found' }, { status: 404 });
  }

  // Validate and score answers server-side
  const scoringResult = await validateAndScoreAnswers(quiz, answers, timeSpent);

  // Store quiz attempt
  const attempt = await db.quizAttempt.create({
    data: {
      userId,
      quizId,
      sessionId: quizSession.id,
      answers: JSON.stringify(answers),
      score: scoringResult.score,
      maxScore: scoringResult.maxScore,
      timeSpent,
      submittedAt: new Date(),
      passed: scoringResult.score >= (quiz.passingScore || 0),
      metadata: JSON.stringify({
        questionResults: scoringResult.questionResults,
        suspiciousActivity: scoringResult.suspiciousActivity
      })
    }
  });

  // Invalidate quiz session
  await db.quizSession.update({
    where: { id: quizSession.id },
    data: { completedAt: new Date() }
  });

  // Log audit trail
  await db.auditLog.create({
    data: {
      userId,
      action: 'QUIZ_SUBMITTED',
      resource: `quiz:${quizId}`,
      metadata: JSON.stringify({
        attemptId: attempt.id,
        score: scoringResult.score,
        timeSpent,
        suspicious: scoringResult.suspiciousActivity.length > 0
      })
    }
  });

  // Return results (without correct answers)
  return NextResponse.json({
    score: scoringResult.score,
    maxScore: scoringResult.maxScore,
    percentage: Math.round((scoringResult.score / scoringResult.maxScore) * 100),
    passed: scoringResult.score >= (quiz.passingScore || 0),
    timeSpent,
    attemptId: attempt.id
  });
}

// ============================================================================
// SERVER-SIDE ANSWER VALIDATION AND SCORING
// ============================================================================

async function validateAndScoreAnswers(quiz: any, userAnswers: any, timeSpent: number) {
  let totalScore = 0;
  let maxScore = 0;
  const questionResults: any[] = [];
  const suspiciousActivity: string[] = [];

  // Check for suspicious timing
  if (timeSpent < 30) { // Less than 30 seconds for entire quiz
    suspiciousActivity.push('EXTREMELY_FAST_COMPLETION');
  }

  for (const question of quiz.questions) {
    maxScore += question.points;
    const userAnswer = userAnswers[question.questionId];
    
    if (userAnswer === undefined || userAnswer === null) {
      questionResults.push({
        questionId: question.questionId,
        correct: false,
        points: 0,
        reason: 'NO_ANSWER'
      });
      continue;
    }

    const isCorrect = validateAnswer(question, userAnswer);
    const points = isCorrect ? question.points : 0;
    totalScore += points;

    questionResults.push({
      questionId: question.questionId,
      correct: isCorrect,
      points,
      userAnswer: userAnswer // Store for review, but don't return to client
    });
  }

  return {
    score: totalScore,
    maxScore,
    questionResults,
    suspiciousActivity
  };
}

// ============================================================================
// SECURE ANSWER VALIDATION
// ============================================================================

function validateAnswer(question: any, userAnswer: any): boolean {
  switch (question.type) {
    case 'multiple_choice':
      const correctOptions = question.options.filter((opt: any) => opt.is_correct);
      if (question.single_correct_answer) {
        return correctOptions.some((opt: any) => opt.id === userAnswer);
      } else {
        const correctIds = correctOptions.map((opt: any) => opt.id);
        const userIds = Array.isArray(userAnswer) ? userAnswer : [userAnswer];
        return correctIds.length === userIds.length && 
               correctIds.every((id: string) => userIds.includes(id));
      }

    case 'true_false':
      return question.correct_answer === userAnswer;

    case 'short_answer':
      const correctAnswers = question.correct_answers || [];
      const userText = userAnswer.toString().toLowerCase().trim();
      return correctAnswers.some((correct: string) => 
        correct.toLowerCase().trim() === userText
      );

    default:
      return false;
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

async function getQuizWithAnswers(quizId: string) {
  // This function should only be called server-side for validation
  return await db.quiz.findUnique({
    where: { id: quizId },
    include: {
      questions: true // Include all question data including answers
    }
  });
}

function generateSecureToken(): string {
  return require('crypto').randomBytes(32).toString('hex');
}

// ============================================================================
// DATABASE SCHEMA ADDITIONS NEEDED
// ============================================================================

/*
Add these models to your Prisma schema:

model QuizSession {
  id           String    @id @default(auto()) @map("_id") @db.ObjectId
  userId       String    @db.ObjectId
  quizId       String    @db.ObjectId
  sessionToken String    @unique
  startedAt    DateTime
  expiresAt    DateTime
  completedAt  DateTime?
  
  user         User      @relation(fields: [userId], references: [id])
  quiz         Quiz      @relation(fields: [quizId], references: [id])
  attempts     QuizAttempt[]
  
  @@map("quiz_sessions")
}

model QuizAttempt {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  userId      String    @db.ObjectId
  quizId      String    @db.ObjectId
  sessionId   String    @db.ObjectId
  answers     String    // JSON string of user answers
  score       Float
  maxScore    Float
  timeSpent   Int       // seconds
  submittedAt DateTime
  passed      Boolean
  metadata    String?   // JSON string for additional data
  
  user        User        @relation(fields: [userId], references: [id])
  quiz        Quiz        @relation(fields: [quizId], references: [id])
  session     QuizSession @relation(fields: [sessionId], references: [id])
  
  @@map("quiz_attempts")
}

model AuditLog {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String?  @db.ObjectId
  action    String
  resource  String
  metadata  String?  // JSON string
  timestamp DateTime @default(now())
  
  user      User?    @relation(fields: [userId], references: [id])
  
  @@map("audit_logs")
}
*/
