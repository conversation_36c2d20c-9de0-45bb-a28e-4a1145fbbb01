# QuizFlow JSON (QFJSON) Format Specification v1.1

## Overview

QuizFlow JSON (QFJSON) is a comprehensive, standardized format for creating educational quizzes and assessments. This specification defines the structure, data types, and validation rules for creating quiz files that are compatible with the QuizFlow platform.

## Schema Information

- **Format Version**: 1.1
- **Schema URL**: `https://quizflow.org/schemas/quizflow_schema_v1.1.json`
- **File Extension**: `.json`
- **MIME Type**: `application/json`

## Root Structure

Every QFJSON file must contain a root `quiz` object:

```json
{
  "quiz": {
    "$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
    "metadata": { ... },
    "questions": [ ... ],
    "question_pools": [ ... ],
    "selection_rules": [ ... ]
  }
}
```

## Metadata Object

The metadata object contains essential information about the quiz:

### Required Fields

| Field | Type | Description |
|-------|------|-------------|
| `format_version` | string | QFJSON format version (e.g., "1.1") |
| `quiz_id` | string | Unique identifier for the quiz |
| `title` | string \| LanguageMap | Quiz title |

### Optional Fields

| Field | Type | Description |
|-------|------|-------------|
| `description` | string \| LanguageMap | Quiz description |
| `author` | string | Quiz author name |
| `creation_date` | string | ISO 8601 timestamp |
| `tags` | string[] | Array of tags for categorization |
| `passing_score_percentage` | number | Minimum score to pass (0-100) |
| `time_limit_minutes` | number | Time limit in minutes |
| `markup_format` | enum | "markdown", "html", or "plain_text" |
| `locale` | string | BCP 47 language tag (e.g., "en-US") |

### Example Metadata

```json
{
  "metadata": {
    "format_version": "1.1",
    "quiz_id": "web-security-fundamentals",
    "title": "Web Application Security Fundamentals",
    "description": "Test your knowledge of web application security concepts and vulnerabilities.",
    "author": "QuizFlow Security Team",
    "creation_date": "2024-01-15T10:00:00Z",
    "tags": ["web-security", "owasp", "cybersecurity"],
    "passing_score_percentage": 75,
    "time_limit_minutes": 30,
    "markup_format": "markdown",
    "locale": "en-US"
  }
}
```

## Question Types

QFJSON supports six question types, each with specific properties:

### 1. Multiple Choice Questions

```json
{
  "question_id": "unique_question_id",
  "type": "multiple_choice",
  "text": "What is the most common web vulnerability?",
  "points": 2,
  "difficulty": "intermediate",
  "single_correct_answer": true,
  "options": [
    {
      "id": "opt1",
      "text": "SQL Injection",
      "is_correct": true,
      "feedback": "Correct! SQL injection is consistently ranked #1 in OWASP Top 10."
    },
    {
      "id": "opt2", 
      "text": "Cross-Site Scripting (XSS)",
      "is_correct": false,
      "feedback": "XSS is common but not the most prevalent vulnerability."
    }
  ],
  "scoring_method": "all_or_nothing",
  "explanation": "SQL injection allows attackers to manipulate database queries..."
}
```

### 2. True/False Questions

```json
{
  "question_id": "tf_question_1",
  "type": "true_false",
  "text": "HTTPS encrypts all data transmitted between client and server.",
  "points": 1,
  "difficulty": "beginner",
  "correct_answer": true,
  "explanation": "HTTPS uses TLS/SSL to encrypt all communication..."
}
```

### 3. Short Answer Questions

```json
{
  "question_id": "short_answer_1",
  "type": "short_answer", 
  "text": "What command is used to scan for open ports on a target system?",
  "points": 2,
  "difficulty": "intermediate",
  "correct_answers": ["nmap", "nmap scan", "port scan"],
  "case_sensitive": false,
  "trim_whitespace": true,
  "exact_match": false
}
```

### 4. Matching Questions

```json
{
  "question_id": "matching_1",
  "type": "matching",
  "text": "Match the vulnerability types with their descriptions:",
  "points": 4,
  "stems": [
    {"id": "stem1", "text": "SQL Injection"},
    {"id": "stem2", "text": "Cross-Site Scripting"}
  ],
  "options": [
    {"id": "opt1", "text": "Malicious script execution in browser"},
    {"id": "opt2", "text": "Database query manipulation"}
  ],
  "correct_pairs": [
    {"stem_id": "stem1", "option_id": "opt2"},
    {"stem_id": "stem2", "option_id": "opt1"}
  ]
}
```

### 5. Fill in the Blank Questions

```json
{
  "question_id": "fill_blank_1",
  "type": "fill_in_the_blank",
  "text_template": "The _____ protocol operates on port _____ and provides secure communication.",
  "points": 2,
  "blanks": [
    {
      "id": "blank1",
      "correct_answers": ["HTTPS", "TLS", "SSL"],
      "case_sensitive": false
    },
    {
      "id": "blank2", 
      "correct_answers": ["443"],
      "case_sensitive": false
    }
  ]
}
```

### 6. Essay Questions

```json
{
  "question_id": "essay_1",
  "type": "essay",
  "text": "Explain the concept of defense in depth and provide three examples.",
  "points": 10,
  "min_word_count": 100,
  "max_word_count": 500,
  "guidelines": "Provide clear examples and explain how each layer contributes to overall security."
}
```

## Common Question Properties

All question types support these common properties:

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `question_id` | string | Yes | Unique identifier |
| `type` | string | Yes | Question type |
| `text` | string \| LanguageMap | Yes | Question text |
| `points` | number | Yes | Point value |
| `difficulty` | enum | No | "beginner", "intermediate", "advanced" |
| `feedback_correct` | string | No | Feedback for correct answers |
| `feedback_incorrect` | string | No | Feedback for incorrect answers |
| `explanation` | string \| LanguageMap | No | Detailed explanation |
| `media` | MediaItem[] | No | Associated media files |
| `hint` | HintItem[] | No | Hints for the question |
| `depends_on` | Dependency | No | Question dependencies |

## Advanced Features

### Media Support

```json
{
  "media": [
    {
      "type": "image",
      "url": "https://example.com/diagram.png",
      "alt_text": "Network topology diagram",
      "caption": "Example network configuration"
    }
  ]
}
```

### Hints with Delays

```json
{
  "hint": [
    {
      "text": "Think about common web vulnerabilities",
      "delay_seconds": 30
    },
    {
      "text": "Consider the OWASP Top 10 list",
      "delay_seconds": 60
    }
  ]
}
```

### Question Dependencies

```json
{
  "depends_on": {
    "question_id": "previous_question",
    "condition": "correct",
    "value": "specific_answer"
  }
}
```

## Question Pools and Selection Rules

For dynamic quiz generation:

### Question Pools

```json
{
  "question_pools": [
    {
      "pool_id": "web_security_basics",
      "title": "Web Security Fundamentals",
      "description": "Basic web security concepts",
      "questions": [ ... ]
    }
  ]
}
```

### Selection Rules

```json
{
  "selection_rules": [
    {
      "pool_id": "web_security_basics",
      "select_count": 5,
      "randomize": true,
      "shuffle_order": true
    }
  ]
}
```

## Internationalization

QFJSON supports multiple languages through LanguageMap objects:

```json
{
  "title": {
    "default": "Web Security Quiz",
    "en-US": "Web Security Quiz",
    "fr-FR": "Quiz de Sécurité Web",
    "es-ES": "Cuestionario de Seguridad Web"
  }
}
```

## Validation Rules

1. **Required Fields**: All required fields must be present
2. **Unique IDs**: All question_ids and option_ids must be unique within the quiz
3. **Point Values**: Must be positive numbers
4. **Percentages**: Must be between 0-100
5. **Time Limits**: Must be positive integers
6. **URLs**: Must be valid HTTP/HTTPS URLs
7. **Dates**: Must follow ISO 8601 format

## Best Practices

1. **Descriptive IDs**: Use meaningful question_ids and option_ids
2. **Consistent Scoring**: Use consistent point values across similar questions
3. **Clear Feedback**: Provide helpful feedback for both correct and incorrect answers
4. **Appropriate Difficulty**: Match difficulty levels to question complexity
5. **Rich Content**: Use explanations to provide educational value
6. **Accessibility**: Include alt_text for images and clear question text

## Example Complete Quiz

```json
{
  "quiz": {
    "$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
    "metadata": {
      "format_version": "1.1",
      "quiz_id": "cybersecurity-basics",
      "title": "Cybersecurity Fundamentals",
      "description": "Test your knowledge of basic cybersecurity concepts",
      "author": "Security Team",
      "creation_date": "2024-01-15T10:00:00Z",
      "tags": ["cybersecurity", "fundamentals"],
      "passing_score_percentage": 70,
      "time_limit_minutes": 20,
      "markup_format": "markdown",
      "locale": "en-US"
    },
    "questions": [
      {
        "question_id": "basic_security_q1",
        "type": "multiple_choice",
        "text": "What does CIA stand for in cybersecurity?",
        "points": 2,
        "difficulty": "beginner",
        "single_correct_answer": true,
        "options": [
          {
            "id": "opt1",
            "text": "Confidentiality, Integrity, Availability",
            "is_correct": true,
            "feedback": "Correct! These are the three pillars of information security."
          },
          {
            "id": "opt2",
            "text": "Central Intelligence Agency",
            "is_correct": false,
            "feedback": "That's a different CIA - we're talking about security principles."
          }
        ],
        "explanation": "The CIA triad represents the three fundamental principles of information security..."
      }
    ]
  }
}
```

This specification provides a complete reference for creating QFJSON-compliant quiz files for the QuizFlow platform.
