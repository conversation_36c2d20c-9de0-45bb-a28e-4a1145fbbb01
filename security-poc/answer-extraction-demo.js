/**
 * PROOF OF CONCEPT: QuizFlow Answer Extraction
 * 
 * This script demonstrates how easily quiz answers can be extracted
 * from the QuizFlow application due to client-side answer exposure.
 * 
 * ⚠️  FOR EDUCATIONAL/SECURITY TESTING PURPOSES ONLY
 */

// Method 1: Extract answers from API response
async function extractAnswersFromAPI(quizId) {
  console.log('🔍 Method 1: Extracting answers from API response...');
  
  try {
    const response = await fetch(`/api/quizzes/${quizId}`);
    const quizData = await response.json();
    
    const extractedAnswers = {};
    
    quizData.questions.forEach((question, index) => {
      const questionId = question.questionId || question.question_id;
      
      switch (question.type) {
        case 'multiple_choice':
          // Extract correct options
          const correctOptions = question.options?.filter(opt => opt.is_correct) || [];
          extractedAnswers[questionId] = {
            type: 'multiple_choice',
            correctAnswers: correctOptions.map(opt => opt.id),
            correctTexts: correctOptions.map(opt => opt.text)
          };
          break;
          
        case 'true_false':
          extractedAnswers[questionId] = {
            type: 'true_false',
            correctAnswer: question.correct_answer || question.correctAnswer
          };
          break;
          
        case 'short_answer':
          extractedAnswers[questionId] = {
            type: 'short_answer',
            correctAnswers: question.correct_answers || question.correctAnswers || []
          };
          break;
          
        default:
          extractedAnswers[questionId] = {
            type: question.type,
            note: 'Answer extraction method varies by type'
          };
      }
    });
    
    console.log('✅ Successfully extracted answers:', extractedAnswers);
    return extractedAnswers;
    
  } catch (error) {
    console.error('❌ Failed to extract answers from API:', error);
    return null;
  }
}

// Method 2: Extract answers from React component state
function extractAnswersFromReactState() {
  console.log('🔍 Method 2: Extracting answers from React component state...');
  
  try {
    // Find React fiber nodes
    const reactFiber = document.querySelector('[data-reactroot]')?._reactInternalFiber ||
                      document.querySelector('#__next')?._reactInternalFiber;
    
    if (!reactFiber) {
      console.log('❌ React fiber not found');
      return null;
    }
    
    // Traverse React component tree to find quiz data
    function findQuizData(fiber) {
      if (!fiber) return null;
      
      // Check if this component has quiz data
      if (fiber.memoizedProps?.quiz || fiber.memoizedState?.questions) {
        return fiber.memoizedProps?.quiz || fiber.memoizedState;
      }
      
      // Recursively check children
      let child = fiber.child;
      while (child) {
        const result = findQuizData(child);
        if (result) return result;
        child = child.sibling;
      }
      
      return null;
    }
    
    const quizData = findQuizData(reactFiber);
    
    if (quizData) {
      console.log('✅ Found quiz data in React state:', quizData);
      return quizData;
    } else {
      console.log('❌ No quiz data found in React state');
      return null;
    }
    
  } catch (error) {
    console.error('❌ Failed to extract from React state:', error);
    return null;
  }
}

// Method 3: Extract answers from browser DevTools Network tab
function extractAnswersFromNetworkTab() {
  console.log('🔍 Method 3: Instructions for Network tab extraction...');
  
  const instructions = `
  📋 Manual Steps to Extract Answers:
  
  1. Open Browser DevTools (F12)
  2. Go to Network tab
  3. Refresh the quiz page
  4. Look for API calls to:
     - /api/quizzes/[quiz-id]
     - /api/quizzes/[quiz-id]/questions
  5. Click on the API call
  6. View the Response tab
  7. All correct answers are visible in the JSON response
  
  🎯 Look for these fields:
  - options[].is_correct: true
  - correct_answer: boolean
  - correct_answers: array
  - correctAnswer: value
  - correctAnswers: array
  `;
  
  console.log(instructions);
  return instructions;
}

// Method 4: Extract answers from localStorage/sessionStorage
function extractAnswersFromStorage() {
  console.log('🔍 Method 4: Checking browser storage...');
  
  const storageData = {
    localStorage: {},
    sessionStorage: {}
  };
  
  // Check localStorage
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (key.includes('quiz') || key.includes('answer'))) {
      try {
        storageData.localStorage[key] = JSON.parse(localStorage.getItem(key));
      } catch {
        storageData.localStorage[key] = localStorage.getItem(key);
      }
    }
  }
  
  // Check sessionStorage
  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    if (key && (key.includes('quiz') || key.includes('answer'))) {
      try {
        storageData.sessionStorage[key] = JSON.parse(sessionStorage.getItem(key));
      } catch {
        storageData.sessionStorage[key] = sessionStorage.getItem(key);
      }
    }
  }
  
  console.log('📦 Storage data:', storageData);
  return storageData;
}

// Method 5: Intercept and log all network requests
function interceptNetworkRequests() {
  console.log('🔍 Method 5: Setting up network request interception...');
  
  // Intercept fetch requests
  const originalFetch = window.fetch;
  window.fetch = async function(...args) {
    const response = await originalFetch.apply(this, args);
    
    // Check if this is a quiz-related request
    const url = args[0];
    if (typeof url === 'string' && (url.includes('/api/quiz') || url.includes('/quiz'))) {
      console.log('🌐 Intercepted quiz request:', url);
      
      // Clone response to read it without consuming the original
      const clonedResponse = response.clone();
      try {
        const data = await clonedResponse.json();
        console.log('📄 Response data:', data);
        
        // Check for answer data
        if (data.questions) {
          console.log('🎯 FOUND QUIZ ANSWERS IN RESPONSE!');
          extractAnswersFromData(data);
        }
      } catch (error) {
        console.log('❌ Could not parse response as JSON');
      }
    }
    
    return response;
  };
  
  console.log('✅ Network interception active. Take the quiz to see intercepted data.');
}

// Helper function to extract answers from any data structure
function extractAnswersFromData(data) {
  const answers = {};
  
  if (data.questions) {
    data.questions.forEach(question => {
      const qId = question.questionId || question.question_id || question.id;
      
      if (question.options) {
        const correct = question.options.filter(opt => opt.is_correct);
        if (correct.length > 0) {
          answers[qId] = correct.map(opt => ({ id: opt.id, text: opt.text }));
        }
      }
      
      if (question.correct_answer !== undefined) {
        answers[qId] = question.correct_answer;
      }
      
      if (question.correct_answers) {
        answers[qId] = question.correct_answers;
      }
    });
  }
  
  console.log('🎯 EXTRACTED ANSWERS:', answers);
  return answers;
}

// Main demonstration function
async function demonstrateAnswerExtraction() {
  console.log('🚨 QUIZFLOW SECURITY DEMONSTRATION');
  console.log('=====================================');
  console.log('This script demonstrates multiple ways to extract quiz answers');
  console.log('⚠️  FOR EDUCATIONAL/SECURITY TESTING PURPOSES ONLY\n');
  
  // Get quiz ID from current URL
  const urlParts = window.location.pathname.split('/');
  const quizId = urlParts[urlParts.indexOf('quiz') + 1];
  
  if (!quizId) {
    console.log('❌ No quiz ID found in URL. Please run this on a quiz page.');
    return;
  }
  
  console.log(`🎯 Target Quiz ID: ${quizId}\n`);
  
  // Try all extraction methods
  const results = {
    apiExtraction: await extractAnswersFromAPI(quizId),
    reactState: extractAnswersFromReactState(),
    networkInstructions: extractAnswersFromNetworkTab(),
    storageData: extractAnswersFromStorage()
  };
  
  // Set up network interception for future requests
  interceptNetworkRequests();
  
  console.log('\n🎯 SUMMARY OF EXTRACTION RESULTS:');
  console.log('==================================');
  
  Object.entries(results).forEach(([method, result]) => {
    if (result && Object.keys(result).length > 0) {
      console.log(`✅ ${method}: SUCCESS - Data extracted`);
    } else {
      console.log(`❌ ${method}: No data found`);
    }
  });
  
  console.log('\n🚨 SECURITY IMPACT:');
  console.log('===================');
  console.log('• Quiz integrity completely compromised');
  console.log('• Users can cheat trivially');
  console.log('• No server-side validation exists');
  console.log('• All answers exposed to client');
  
  console.log('\n🛡️  RECOMMENDED FIXES:');
  console.log('======================');
  console.log('• Remove answers from client-side API responses');
  console.log('• Implement server-side answer validation');
  console.log('• Add quiz session management');
  console.log('• Implement rate limiting and anti-cheating measures');
  
  return results;
}

// Auto-run if script is loaded directly
if (typeof window !== 'undefined') {
  // Add a button to run the demonstration
  const button = document.createElement('button');
  button.textContent = '🔍 Run Security Demo';
  button.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 9999;
    padding: 10px;
    background: #ff4444;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
  `;
  button.onclick = demonstrateAnswerExtraction;
  document.body.appendChild(button);
  
  console.log('🔍 Security demonstration loaded. Click the red button or run demonstrateAnswerExtraction() in console.');
}

// Export for use in other contexts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    extractAnswersFromAPI,
    extractAnswersFromReactState,
    extractAnswersFromNetworkTab,
    extractAnswersFromStorage,
    interceptNetworkRequests,
    demonstrateAnswerExtraction
  };
}
