# Test info

- Name: Diagnostic Tests - Real Issues >> DIAGNOSTIC: Check question addition workflow
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/diagnostic-real-issues.spec.ts:205:7

# Error details

```
TimeoutError: locator.textContent: Timeout 10000ms exceeded.
Call log:
  - waiting for locator('text=Questions (')

    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/diagnostic-real-issues.spec.ts:224:73
```

# Page snapshot

```yaml
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "My Quizzes":
      - /url: /dashboard/quizzes
    - link "Activity":
      - /url: /dashboard/activity
    - link "Explore":
      - /url: /explore
    - link "Admin":
      - /url: /dashboard/admin
  - text: <NAME_EMAIL>
  - button "Sign Out"
- main:
  - heading "Edit Quiz" [level=1]
  - button "Back to Quizzes"
  - button "Preview Quiz"
  - tablist:
    - tab "Quiz Details"
    - tab "Questions" [selected]
    - tab "Publish"
  - tabpanel "Questions":
    - heading "Questions" [level=3]
    - paragraph: Add, edit, or remove questions from your quiz
    - tablist:
      - tab "Existing Questions" [selected]
      - tab "Add Question"
    - tabpanel "Existing Questions":
      - paragraph: No questions yet. Add your first question to get started.
      - button "Add First Question"
- alert
```

# Test source

```ts
  124 |     expect(true).toBe(true);
  125 |   });
  126 |
  127 |   test('DIAGNOSTIC: Check question management UI', async ({ page }) => {
  128 |     console.log('🔍 Testing Question Management UI...');
  129 |
  130 |     // Create a quiz first
  131 |     await page.goto('/dashboard/quizzes/create');
  132 |     await page.fill('input[id="title"]', 'Question UI Test');
  133 |     await page.fill('textarea[id="description"]', 'Testing question UI');
  134 |     await page.click('button[type="submit"]');
  135 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  136 |
  137 |     // Check if edit page loads
  138 |     const is404 = await page.locator('text=404, text=Not Found').isVisible();
  139 |     if (is404) {
  140 |       console.log('❌ Edit page shows 404 - cannot test question UI');
  141 |       return;
  142 |     }
  143 |
  144 |     console.log('✅ Edit page loads');
  145 |
  146 |     // Look for Questions tab/button
  147 |     const questionsTab = page.locator('button:has-text("Questions"), .questions-tab, [data-testid="questions-tab"]');
  148 |     const questionsTabExists = await questionsTab.isVisible();
  149 |     console.log('Questions tab exists:', questionsTabExists);
  150 |
  151 |     if (questionsTabExists) {
  152 |       await questionsTab.click();
  153 |       await page.waitForTimeout(1000);
  154 |
  155 |       // Look for Add Question button
  156 |       const addQuestionBtn = page.locator('button:has-text("Add Question"), .add-question, [data-testid="add-question"]');
  157 |       const addQuestionExists = await addQuestionBtn.isVisible();
  158 |       console.log('Add Question button exists:', addQuestionExists);
  159 |
  160 |       if (addQuestionExists) {
  161 |         await addQuestionBtn.click();
  162 |         await page.waitForTimeout(1000);
  163 |
  164 |         // Look for question form elements
  165 |         const questionTypeSelect = page.locator('select[id="questionType"]');
  166 |         const questionTextArea = page.locator('textarea[id="questionText"]');
  167 |         const pointsInput = page.locator('input[id="points"]');
  168 |
  169 |         console.log('Question type select exists:', await questionTypeSelect.isVisible());
  170 |         console.log('Question text area exists:', await questionTextArea.isVisible());
  171 |         console.log('Points input exists:', await pointsInput.isVisible());
  172 |
  173 |         if (await questionTypeSelect.isVisible()) {
  174 |           // Try to select multiple choice
  175 |           await questionTypeSelect.selectOption('multiple_choice');
  176 |           await page.waitForTimeout(1000);
  177 |
  178 |           // Look for option inputs
  179 |           const optionInputs = page.locator('input[placeholder*="option"], .option-input');
  180 |           const optionCount = await optionInputs.count();
  181 |           console.log('Option inputs found:', optionCount);
  182 |
  183 |           if (optionCount > 0) {
  184 |             console.log('✅ Question form UI appears to be implemented');
  185 |           } else {
  186 |             console.log('❌ Question form missing option inputs');
  187 |           }
  188 |         } else {
  189 |           console.log('❌ Question form not properly implemented');
  190 |         }
  191 |       } else {
  192 |         console.log('❌ Add Question button not found');
  193 |       }
  194 |     } else {
  195 |       console.log('❌ Questions tab not found');
  196 |
  197 |       // Log available buttons
  198 |       const allButtons = await page.locator('button').allTextContents();
  199 |       console.log('Available buttons:', allButtons);
  200 |     }
  201 |
  202 |     expect(true).toBe(true);
  203 |   });
  204 |
  205 |   test('DIAGNOSTIC: Check question addition workflow', async ({ page }) => {
  206 |     console.log('🔍 Testing Question Addition Workflow...');
  207 |
  208 |     // Create a quiz first
  209 |     await page.goto('/dashboard/quizzes/create');
  210 |     await page.fill('input[id="title"]', 'Question Addition Debug');
  211 |     await page.fill('textarea[id="description"]', 'Testing question addition');
  212 |     await page.click('button[type="submit"]');
  213 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  214 |
  215 |     console.log('✅ Quiz created and edit page loaded');
  216 |
  217 |     // Navigate to Questions tab
  218 |     await page.click('button:has-text("Questions")');
  219 |     await page.waitForTimeout(1000);
  220 |
  221 |     console.log('✅ Questions tab clicked');
  222 |
  223 |     // Check initial state
> 224 |     const initialQuestionCount = await page.locator('text=Questions (').textContent();
      |                                                                         ^ TimeoutError: locator.textContent: Timeout 10000ms exceeded.
  225 |     console.log('Initial question count text:', initialQuestionCount);
  226 |
  227 |     // Click Add Question tab
  228 |     await page.click('button:has-text("Add Question")');
  229 |     await page.waitForTimeout(1000);
  230 |
  231 |     console.log('✅ Add Question tab clicked');
  232 |
  233 |     // Fill in question details
  234 |     await page.selectOption('select[id="questionType"]', 'multiple_choice');
  235 |     await page.fill('textarea[id="questionText"]', 'Debug test question');
  236 |     await page.fill('input[id="points"]', '2');
  237 |
  238 |     console.log('✅ Question details filled');
  239 |
  240 |     // Fill in options
  241 |     const optionInputs = page.locator('input[placeholder="Enter option text"]');
  242 |     const optionCount = await optionInputs.count();
  243 |     console.log('Option inputs found:', optionCount);
  244 |
  245 |     if (optionCount >= 2) {
  246 |       await optionInputs.nth(0).fill('Option A Debug');
  247 |       await optionInputs.nth(1).fill('Option B Debug');
  248 |       console.log('✅ Options filled');
  249 |
  250 |       // Mark first option as correct
  251 |       const correctRadio = page.locator('input[type="radio"][name="correctOption"]');
  252 |       const radioCount = await correctRadio.count();
  253 |       console.log('Correct answer radios found:', radioCount);
  254 |
  255 |       if (radioCount > 0) {
  256 |         await correctRadio.first().check();
  257 |         console.log('✅ Correct answer marked');
  258 |       }
  259 |     }
  260 |
  261 |     // Listen for network requests
  262 |     page.on('response', response => {
  263 |       if (response.url().includes('/api/quizzes/') && response.url().includes('/questions')) {
  264 |         console.log('📡 API Response:', response.status(), response.url());
  265 |       }
  266 |     });
  267 |
  268 |     // Save the question
  269 |     console.log('🔄 Clicking Add Question button...');
  270 |     await page.click('button:has-text("Add Question")');
  271 |     await page.waitForTimeout(3000);
  272 |
  273 |     // Check if we're on existing questions tab
  274 |     const currentTab = await page.locator('.tabs-list button[data-state="active"]').textContent();
  275 |     console.log('Current active tab:', currentTab);
  276 |
  277 |     // Check for questions in the list
  278 |     const questionsList = page.locator('text=Questions (');
  279 |     const questionsText = await questionsList.textContent();
  280 |     console.log('Questions count after addition:', questionsText);
  281 |
  282 |     // Look for the specific question text
  283 |     const questionVisible = await page.locator('text=Debug test question').isVisible();
  284 |     console.log('Question visible in list:', questionVisible);
  285 |
  286 |     // Check for any error messages
  287 |     const errorMessages = await page.locator('text=Error, text=Failed').count();
  288 |     console.log('Error messages found:', errorMessages);
  289 |
  290 |     // Check for success messages
  291 |     const successMessages = await page.locator('text=success, text=added').count();
  292 |     console.log('Success messages found:', successMessages);
  293 |
  294 |     expect(true).toBe(true);
  295 |   });
  296 | });
  297 |
```