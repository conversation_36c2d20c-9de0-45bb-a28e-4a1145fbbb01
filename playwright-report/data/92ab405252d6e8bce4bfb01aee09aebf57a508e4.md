# Test info

- Name: Dashboard - User Role >> should show user-specific dashboard content
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/dashboard.spec.ts:200:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/dashboard.spec.ts:186:16
```

# Page snapshot

```yaml
- heading "Login" [level=3]
- paragraph: Enter your credentials to access your account
- text: Invalid email or password Email
- textbox "Email": <EMAIL>
- text: Password
- link "Forgot password?":
  - /url: /auth/forgot-password
- textbox "Password": user123
- button "Login"
- text: Or continue with
- button "Google"
- button "GitHub"
- paragraph:
  - text: Don't have an account?
  - link "Sign up":
    - /url: /auth/register
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
   86 |     // Look for quiz statistics
   87 |     const statsElements = page.locator('.quiz-stats, .statistics, .metrics');
   88 |     if (await statsElements.count() > 0) {
   89 |       await expect(statsElements.first()).toBeVisible();
   90 |
   91 |       // Should show numbers or charts
   92 |       const numbers = page.locator('text=/\\d+/');
   93 |       await expect(numbers.first()).toBeVisible();
   94 |     }
   95 |   });
   96 |
   97 |   test('should handle responsive design', async ({ page }) => {
   98 |     // Test mobile viewport
   99 |     await page.setViewportSize({ width: 375, height: 667 });
  100 |     await page.reload();
  101 |
  102 |     // Should still show main content
  103 |     await expect(page.locator('h1')).toBeVisible();
  104 |
  105 |     // Mobile menu should be accessible
  106 |     const mobileMenu = page.locator('.mobile-menu, .hamburger, button[aria-label*="menu"]');
  107 |     if (await mobileMenu.isVisible()) {
  108 |       await mobileMenu.click();
  109 |       await expect(page.locator('nav, .navigation')).toBeVisible();
  110 |     }
  111 |
  112 |     // Reset to desktop
  113 |     await page.setViewportSize({ width: 1280, height: 720 });
  114 |   });
  115 |
  116 |   test('should search functionality', async ({ page }) => {
  117 |     // Look for search input
  118 |     const searchInput = page.locator('input[type="search"], input[placeholder*="search"], input[placeholder*="Search"]');
  119 |     if (await searchInput.isVisible()) {
  120 |       await searchInput.fill('security');
  121 |       await searchInput.press('Enter');
  122 |
  123 |       // Should show search results or filter content
  124 |       await page.waitForTimeout(1000); // Wait for search to process
  125 |
  126 |       // Verify search worked (results or filtered content)
  127 |       const results = page.locator('.search-results, .filtered-content');
  128 |       if (await results.isVisible()) {
  129 |         await expect(results).toBeVisible();
  130 |       }
  131 |     }
  132 |   });
  133 |
  134 |   test('should handle notifications', async ({ page }) => {
  135 |     // Look for notification bell or indicator
  136 |     const notificationBell = page.locator('.notification-bell, .notifications, [data-testid="notifications"]');
  137 |     if (await notificationBell.isVisible()) {
  138 |       await notificationBell.click();
  139 |
  140 |       // Should show notifications dropdown or panel
  141 |       await expect(page.locator('.notification-dropdown, .notification-panel')).toBeVisible();
  142 |     }
  143 |   });
  144 |
  145 |   test('should display quick actions', async ({ page }) => {
  146 |     // Look for quick action buttons
  147 |     const quickActions = page.locator('.quick-actions, .action-buttons');
  148 |     if (await quickActions.isVisible()) {
  149 |       await expect(quickActions).toBeVisible();
  150 |
  151 |       // Should have create quiz button for admin
  152 |       const createQuizBtn = page.locator('button:has-text("Create Quiz"), a:has-text("Create Quiz")');
  153 |       if (await createQuizBtn.isVisible()) {
  154 |         await expect(createQuizBtn).toBeVisible();
  155 |       }
  156 |     }
  157 |   });
  158 |
  159 |   test('should handle dark mode toggle', async ({ page }) => {
  160 |     // Look for theme toggle
  161 |     const themeToggle = page.locator('.theme-toggle, .dark-mode-toggle, button[aria-label*="theme"]');
  162 |     if (await themeToggle.isVisible()) {
  163 |       // Get initial theme
  164 |       const initialTheme = await page.evaluate(() => document.documentElement.classList.contains('dark'));
  165 |
  166 |       // Toggle theme
  167 |       await themeToggle.click();
  168 |
  169 |       // Wait for theme change
  170 |       await page.waitForTimeout(500);
  171 |
  172 |       // Verify theme changed
  173 |       const newTheme = await page.evaluate(() => document.documentElement.classList.contains('dark'));
  174 |       expect(newTheme).not.toBe(initialTheme);
  175 |     }
  176 |   });
  177 | });
  178 |
  179 | test.describe('Dashboard - User Role', () => {
  180 |   test.beforeEach(async ({ page }) => {
  181 |     // Login as regular user
  182 |     await page.goto('/auth/login');
  183 |     await page.fill('input[id="email"]', '<EMAIL>');
  184 |     await page.fill('input[id="password"]', 'user123');
  185 |     await page.click('button[type="submit"]');
> 186 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
  187 |   });
  188 |
  189 |   test('should hide admin features for regular users', async ({ page }) => {
  190 |     // Should not show admin navigation
  191 |     await expect(page.locator('a[href="/dashboard/admin"]')).not.toBeVisible();
  192 |
  193 |     // Should not show create quiz button
  194 |     await expect(page.locator('button:has-text("Create Quiz")')).not.toBeVisible();
  195 |
  196 |     // Should show user-appropriate content
  197 |     await expect(page.locator('text=Take Quiz, text=Browse Quizzes')).toBeVisible();
  198 |   });
  199 |
  200 |   test('should show user-specific dashboard content', async ({ page }) => {
  201 |     // Should show user's quiz history or progress
  202 |     const userContent = page.locator('.user-progress, .quiz-history, .my-quizzes');
  203 |     if (await userContent.isVisible()) {
  204 |       await expect(userContent).toBeVisible();
  205 |     }
  206 |
  207 |     // Should show available quizzes to take
  208 |     const availableQuizzes = page.locator('.available-quizzes, .browse-quizzes');
  209 |     if (await availableQuizzes.isVisible()) {
  210 |       await expect(availableQuizzes).toBeVisible();
  211 |     }
  212 |   });
  213 | });
  214 |
```