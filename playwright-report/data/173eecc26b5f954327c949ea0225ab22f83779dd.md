# Test info

- Name: Analytics - User View >> should show user personal analytics
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/analytics.spec.ts:197:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/analytics.spec.ts:194:16
```

# Page snapshot

```yaml
- heading "Login" [level=3]
- paragraph: Enter your credentials to access your account
- text: Invalid email or password Email
- textbox "Email": <EMAIL>
- text: Password
- link "Forgot password?":
  - /url: /auth/forgot-password
- textbox "Password": user123
- button "Login"
- text: Or continue with
- button "Google"
- button "GitHub"
- paragraph:
  - text: Don't have an account?
  - link "Sign up":
    - /url: /auth/register
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
   94 |         // Wait for data to update
   95 |         await page.waitForTimeout(1000);
   96 |
   97 |         // Verify data updated (this is basic - real test would check specific values)
   98 |         await expect(page.locator('.metric, .stat-card')).toBeVisible();
   99 |       }
  100 |     }
  101 |   });
  102 |
  103 |   test('should export analytics data', async ({ page }) => {
  104 |     // Look for export button
  105 |     const exportBtn = page.locator('button:has-text("Export"), button:has-text("Download")');
  106 |     if (await exportBtn.isVisible()) {
  107 |       // Set up download handler
  108 |       const downloadPromise = page.waitForEvent('download');
  109 |
  110 |       await exportBtn.click();
  111 |
  112 |       // Wait for download to start
  113 |       const download = await downloadPromise;
  114 |
  115 |       // Verify download started
  116 |       expect(download.suggestedFilename()).toMatch(/\.(csv|xlsx|pdf)$/);
  117 |     }
  118 |   });
  119 |
  120 |   test('should display quiz-specific analytics', async ({ page }) => {
  121 |     // Look for quiz selection dropdown
  122 |     const quizSelector = page.locator('select[name="quiz"], .quiz-selector');
  123 |     if (await quizSelector.isVisible()) {
  124 |       // Select a specific quiz
  125 |       await quizSelector.click();
  126 |
  127 |       const quizOptions = page.locator('option, .quiz-option');
  128 |       if (await quizOptions.count() > 1) {
  129 |         await quizOptions.nth(1).click();
  130 |
  131 |         // Wait for data to load
  132 |         await page.waitForTimeout(1000);
  133 |
  134 |         // Should show quiz-specific metrics
  135 |         await expect(page.locator('.quiz-analytics, .quiz-metrics')).toBeVisible();
  136 |       }
  137 |     }
  138 |   });
  139 |
  140 |   test('should show question-level analytics', async ({ page }) => {
  141 |     // Look for question analytics section
  142 |     const questionAnalytics = page.locator('.question-analytics, .question-performance');
  143 |     if (await questionAnalytics.isVisible()) {
  144 |       await expect(questionAnalytics).toBeVisible();
  145 |
  146 |       // Should show question difficulty or success rates
  147 |       const questionMetrics = page.locator('.question-metric, .question-stat');
  148 |       if (await questionMetrics.count() > 0) {
  149 |         await expect(questionMetrics.first()).toBeVisible();
  150 |       }
  151 |     }
  152 |   });
  153 |
  154 |   test('should display real-time analytics', async ({ page }) => {
  155 |     // Look for real-time indicators
  156 |     const realTimeSection = page.locator('.real-time, .live-data');
  157 |     if (await realTimeSection.isVisible()) {
  158 |       await expect(realTimeSection).toBeVisible();
  159 |
  160 |       // Should show current active users or sessions
  161 |       const liveMetrics = page.locator('text=/live/i, text=/active/i, text=/online/i');
  162 |       if (await liveMetrics.count() > 0) {
  163 |         await expect(liveMetrics.first()).toBeVisible();
  164 |       }
  165 |     }
  166 |   });
  167 |
  168 |   test('should handle analytics refresh', async ({ page }) => {
  169 |     // Look for refresh button
  170 |     const refreshBtn = page.locator('button:has-text("Refresh"), button[aria-label*="refresh"]');
  171 |     if (await refreshBtn.isVisible()) {
  172 |       // Get initial data
  173 |       const initialMetric = await page.locator('.metric, .stat-card').first().textContent();
  174 |
  175 |       // Click refresh
  176 |       await refreshBtn.click();
  177 |
  178 |       // Wait for refresh to complete
  179 |       await page.waitForTimeout(2000);
  180 |
  181 |       // Verify page is still functional
  182 |       await expect(page.locator('.metric, .stat-card')).toBeVisible();
  183 |     }
  184 |   });
  185 | });
  186 |
  187 | test.describe('Analytics - User View', () => {
  188 |   test.beforeEach(async ({ page }) => {
  189 |     // Login as regular user
  190 |     await page.goto('/auth/login');
  191 |     await page.fill('input[id="email"]', '<EMAIL>');
  192 |     await page.fill('input[id="password"]', 'user123');
  193 |     await page.click('button[type="submit"]');
> 194 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
  195 |   });
  196 |
  197 |   test('should show user personal analytics', async ({ page }) => {
  198 |     // Navigate to user analytics (if available)
  199 |     const analyticsLink = page.locator('a[href*="analytics"], text=Analytics');
  200 |     if (await analyticsLink.isVisible()) {
  201 |       await analyticsLink.click();
  202 |
  203 |       // Should show personal performance
  204 |       const personalStats = page.locator('.personal-stats, .my-performance');
  205 |       if (await personalStats.isVisible()) {
  206 |         await expect(personalStats).toBeVisible();
  207 |
  208 |         // Should show user's quiz scores
  209 |         const scores = page.locator('text=/score/i, text=/points/i');
  210 |         if (await scores.count() > 0) {
  211 |           await expect(scores.first()).toBeVisible();
  212 |         }
  213 |       }
  214 |     }
  215 |   });
  216 |
  217 |   test('should not show admin analytics', async ({ page }) => {
  218 |     // Try to access admin analytics
  219 |     await page.goto('/dashboard/analytics');
  220 |
  221 |     // Should either redirect or show limited view
  222 |     const adminMetrics = page.locator('.admin-analytics, .system-metrics');
  223 |     await expect(adminMetrics).not.toBeVisible();
  224 |   });
  225 | });
  226 |
```