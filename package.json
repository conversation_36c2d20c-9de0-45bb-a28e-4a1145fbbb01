{"name": "hacking-quiz", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "seed": "tsx scripts/seed-quizzes.ts", "setup-admin": "tsx scripts/setup-admin.ts", "check-admin": "tsx scripts/check-admin.ts", "test-login": "tsx scripts/test-login.ts", "db:push": "prisma db push", "db:reset": "prisma db push --force-reset", "seed:enhanced": "tsx scripts/seed-enhanced-structure.ts", "generate:content": "tsx scripts/generate-quiz-content.ts", "generate:advanced": "tsx scripts/generate-advanced-security-quizzes.ts", "generate:categories": "tsx scripts/generate-general-categories.ts", "count:content": "tsx scripts/count-content.ts", "export:all": "tsx scripts/export-all-quizzes.ts", "verify:export": "tsx scripts/verify-export.ts", "debug:quiz": "tsx scripts/debug-quiz.ts", "setup:full": "npm run db:push && npm run seed:enhanced && npm run seed && npm run generate:content", "setup:production": "npm run db:push && npm run seed:enhanced && npm run generate:content", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:all": "npm run test && npm run test:e2e", "test:ci": "npm run test:coverage && npm run test:e2e", "test:security": "tsx scripts/run-security-tests.ts", "test:security:answer-exposure": "playwright test tests/security/answer-exposure.test.ts", "test:security:client-side": "playwright test tests/security/client-side-security.test.ts", "test:security:verify": "tsx scripts/final-security-verification.ts", "security:audit": "tsx scripts/final-security-verification.ts", "test:seed": "ts-node tests/utils/seed-test-data.ts seed", "test:cleanup": "ts-node tests/utils/seed-test-data.ts cleanup", "test:seed-users": "ts-node tests/utils/seed-test-data.ts users"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@prisma/client": "^6.8.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@types/react-syntax-highlighter": "^15.5.13", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "marked": "^15.0.12", "mongodb": "^6.16.0", "mongoose": "^8.15.0", "next": "15.1.8", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "prisma": "^6.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "shadcn-ui": "^0.9.5", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.27"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.52.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcrypt": "^5.0.2", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.8", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.8.4", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.2", "typescript": "^5"}}